# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.60.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.60.0-mr-3630.2...v3.60.1) (2025-09-16)

**Note:** Version bump only for package @digisac/root





# [3.60.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.60.0-rc.10...v3.60.0) (2025-09-15)



# [3.59.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.60.0-mr-3453.15...v3.59.0) (2025-09-09)

**Note:** Version bump only for package @digisac/root





# [3.59.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.59.0-rc.29...v3.59.0) (2025-09-09)

**Note:** Version bump only for package @digisac/root





## [3.58.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.58.1-mr-3562.5...v3.58.2) (2025-09-08)

**Note:** Version bump only for package @digisac/root





## [3.58.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.58.1-mr-3594.1...v3.58.1) (2025-09-04)

**Note:** Version bump only for package @digisac/root





# [3.58.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.58.0-rc.21...v3.58.0) (2025-09-02)

**Note:** Version bump only for package @digisac/root





## [3.57.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.57.2-mr-3500.5...v3.57.3) (2025-09-02)

**Note:** Version bump only for package @digisac/root





## [3.57.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.57.1-mr-3541.2...v3.57.2) (2025-09-01)

**Note:** Version bump only for package @digisac/root





## [3.57.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.57.0-mr-3560.1...v3.57.1) (2025-08-27)

**Note:** Version bump only for package @digisac/root





# [3.57.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.57.0-rc.15...v3.57.0) (2025-08-26)

**Note:** Version bump only for package @digisac/root





## [3.56.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.56.1-mr-3539.0...v3.56.1) (2025-08-21)

**Note:** Version bump only for package @digisac/root





# [3.56.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.56.0-rc.20...v3.56.0) (2025-08-18)

**Note:** Version bump only for package @digisac/root





## [3.55.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.1-mr-3518.1...v3.55.1) (2025-08-15)

**Note:** Version bump only for package @digisac/root





# [3.55.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.3...v3.55.0) (2025-08-11)



# [3.55.0-rc.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3438.4...v3.55.0-rc.15) (2025-08-08)



# [3.55.0-mr-3438.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3438.3...v3.55.0-mr-3438.4) (2025-08-08)



# [3.55.0-mr-3438.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3438.2...v3.55.0-mr-3438.3) (2025-08-08)


### Bug Fixes

* update notification message for insufficient credits ([f9a1382](https://gitlab.ikatec.cloud/digisac/digisac/commit/f9a13822144a20fc2f9ad5cc93176b1ff33c5511))



# [3.55.0-mr-3438.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.14...v3.55.0-mr-3438.2) (2025-08-08)


### Bug Fixes

* missing translation token ([492e73d](https://gitlab.ikatec.cloud/digisac/digisac/commit/492e73d5a70667f394527c907f6b027153ecf7f3))



# [3.55.0-rc.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3438.1...v3.55.0-rc.14) (2025-08-07)



# [3.55.0-mr-3438.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.2...v3.55.0-mr-3438.1) (2025-08-07)



# [3.55.0-mr-3483.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1-mr-3466.3...v3.55.0-mr-3483.1) (2025-08-07)



# [3.55.0-mr-3438.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.14...v3.55.0-mr-3438.0) (2025-08-07)



# [3.55.0-mr-3427.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3483.0...v3.55.0-mr-3427.14) (2025-08-07)



# [3.55.0-mr-3483.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.13...v3.55.0-mr-3483.0) (2025-08-06)


### Bug Fixes

* **tests:** update mocks for agent credits services ([b25ef06](https://gitlab.ikatec.cloud/digisac/digisac/commit/b25ef068155fe94033f83a7239b16f8d58febcfd))



# [3.55.0-mr-3427.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.13...v3.55.0-mr-3427.13) (2025-08-06)



# [3.55.0-rc.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.12...v3.55.0-rc.13) (2025-08-06)



# [3.55.0-mr-3427.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.12...v3.55.0-mr-3427.12) (2025-08-06)


### Bug Fixes

* **ai-agent-dashboard:** adiciona agente inteligente ao exportar os dados de uso no dashboard ([c63e37e](https://gitlab.ikatec.cloud/digisac/digisac/commit/c63e37e887b521d46acdc7149ddff64b90edb40b))
* remove needless showAgentToast call ([fa68def](https://gitlab.ikatec.cloud/digisac/digisac/commit/fa68defa69a823b67e5bc084ec76da80b6eda041))


### Features

* block agent credit limits ([200ac5b](https://gitlab.ikatec.cloud/digisac/digisac/commit/200ac5bd057026889f2528c07334fe8405028971))



# [3.55.0-rc.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.11...v3.55.0-rc.12) (2025-08-06)



# [3.55.0-mr-3427.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.11...v3.55.0-mr-3427.11) (2025-08-06)



# [3.55.0-rc.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.10...v3.55.0-rc.11) (2025-08-06)


### Bug Fixes

* lint at pipelineResource.test ([b5cdacb](https://gitlab.ikatec.cloud/digisac/digisac/commit/b5cdacb63b1cd3d64ce1412c9d8a55d13247ba87))



# [3.55.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3431.3...v3.55.0-rc.10) (2025-08-05)



# [3.55.0-mr-3431.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1...v3.55.0-mr-3431.3) (2025-08-05)



# [3.55.0-rc.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3468.2...v3.55.0-rc.9) (2025-08-05)



# [3.55.0-mr-3468.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.8...v3.55.0-mr-3468.2) (2025-08-05)



# [3.55.0-rc.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3459.3...v3.55.0-rc.8) (2025-08-05)



# [3.55.0-mr-3459.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3431.2...v3.55.0-mr-3459.3) (2025-08-05)



# [3.55.0-mr-3431.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3431.1...v3.55.0-mr-3431.2) (2025-08-05)



# [3.55.0-mr-3431.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3459.2...v3.55.0-mr-3431.1) (2025-08-05)


### Features

* corrige erro do copiloto tradução ([a1cd7bb](https://gitlab.ikatec.cloud/digisac/digisac/commit/a1cd7bb0491567da650d3419d8542d51e0aeb4bd))



# [3.55.0-mr-3459.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.10...v3.55.0-mr-3459.2) (2025-08-05)



# [3.55.0-mr-3427.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1-mr-3475.0...v3.55.0-mr-3427.10) (2025-08-05)



# [3.55.0-mr-3427.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3427.8...v3.55.0-mr-3427.9) (2025-08-05)



# [3.54.0-mr-3427.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.7...v3.54.0-mr-3427.8) (2025-08-05)


### Features

* **ai-agent-dashboard:** adiciona input de visualização de creditos do agente inteligente na configuração da conta ([72029d4](https://gitlab.ikatec.cloud/digisac/digisac/commit/72029d472b3fa75ee0d169d6e7bd0398575752cf))



# [3.55.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0...v3.55.0-rc.7) (2025-08-05)


### Bug Fixes

* **version:** versao alterada para 3.55.0-rc.6 ([8866639](https://gitlab.ikatec.cloud/digisac/digisac/commit/88666390da0983d6ae39bf74e7e94ff7c8614f7f))



## [3.55.5-mr-3439.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.5-mr-3439.1...v3.55.5-mr-3439.2) (2025-08-04)



## [3.55.5-mr-3439.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.6...v3.55.5-mr-3439.1) (2025-08-04)



# [3.55.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.1-mr-3443.3...v3.55.0-rc.6) (2025-08-04)


### Bug Fixes

* **version:** versao alterada para 3.55.0-rc.5 ([26051bd](https://gitlab.ikatec.cloud/digisac/digisac/commit/26051bd370f523841211935c713cb01fe48f6ae1))



## [3.55.1-mr-3443.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.4-mr-3466.1...v3.55.1-mr-3443.3) (2025-08-04)



# [3.54.0-mr-3427.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.5...v3.54.0-mr-3427.7) (2025-07-31)


### Bug Fixes

* **ai-agent-dashboard:** corrige apontamentos no front ([ee889cd](https://gitlab.ikatec.cloud/digisac/digisac/commit/ee889cd05a825ec495100e580f389cb516402136))



# [3.55.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-rc.16...v3.55.0-rc.5) (2025-07-31)



# [3.55.0-mr-3465.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.4...v3.55.0-mr-3465.0) (2025-07-31)



# [3.55.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3431.0...v3.55.0-rc.4) (2025-07-31)



# [3.55.0-mr-3431.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3468.1...v3.55.0-mr-3431.0) (2025-07-31)



# [3.55.0-mr-3468.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3468.0...v3.55.0-mr-3468.1) (2025-07-31)



# [3.55.0-mr-3468.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3467.0...v3.55.0-mr-3468.0) (2025-07-31)


### Bug Fixes

* ajustando posição dos botões do filtro ([5f896bd](https://gitlab.ikatec.cloud/digisac/digisac/commit/5f896bd091de81a48ef0e7a46e92491280013811))



# [3.55.0-mr-3458.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3458.1...v3.55.0-mr-3458.0) (2025-07-31)



# [3.54.0-mr-3458.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.4-mr-3466.0...v3.54.0-mr-3458.1) (2025-07-31)


### Features

* increasing code coverage from 60% to 87% ([ed234b8](https://gitlab.ikatec.cloud/digisac/digisac/commit/ed234b802a1d739232df48d88f98f1e73a2a8e92))
* novos testes unitarios para a classe cardResource ([35f87ab](https://gitlab.ikatec.cloud/digisac/digisac/commit/35f87ab821f1aaac0ee33a5a184f9f162c095ccc))



## [3.53.1-mr-3443.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.1-mr-3443.1...v3.53.1-mr-3443.2) (2025-07-31)



## [3.53.1-mr-3443.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3427.6...v3.53.1-mr-3443.1) (2025-07-31)



# [3.54.0-mr-3427.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-rc.15...v3.54.0-mr-3427.6) (2025-07-30)


### Features

* **ai-agent-dashboard:** remove dados mockados do front ([6394058](https://gitlab.ikatec.cloud/digisac/digisac/commit/639405844e0c190ee3dc475e0bf044c7e84f2799))



# [3.54.0-mr-3427.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3459.1...v3.54.0-mr-3427.5) (2025-07-30)


### Features

* **ai-agent-dashboard:** corrige testes unitários da sugestão de prompt ([aa3ce37](https://gitlab.ikatec.cloud/digisac/digisac/commit/aa3ce37f168813451cba1db5fdb68170f91a971d))



# [3.55.0-mr-3459.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3459.0...v3.55.0-mr-3459.1) (2025-07-30)



# [3.55.0-mr-3459.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.3...v3.55.0-mr-3459.0) (2025-07-30)



# [3.55.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3358.5...v3.55.0-rc.3) (2025-07-30)



# [3.55.0-mr-3358.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.2...v3.55.0-mr-3358.5) (2025-07-30)



# [3.55.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3458.0...v3.55.0-rc.2) (2025-07-30)


### Features

* improve code coverage from 40% to 63% ([6a2b789](https://gitlab.ikatec.cloud/digisac/digisac/commit/6a2b789304e6f348f50259b170dd5b221b1788e2))



# [3.54.0-mr-3458.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3331.10...v3.54.0-mr-3458.0) (2025-07-30)



# [3.55.0-mr-3331.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3431.9...v3.55.0-mr-3331.10) (2025-07-30)


### Features

* adiciona testes unitarios a classe CardResource ([4b2b7dc](https://gitlab.ikatec.cloud/digisac/digisac/commit/4b2b7dcf834eef6dd844891096207755cf906f81))



# [3.54.0-mr-3431.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.1...v3.54.0-mr-3431.9) (2025-07-30)



# [3.55.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-rc.13...v3.55.0-rc.1) (2025-07-30)


### Bug Fixes

* **version:** versao alterada para 3.55.0-rc.0 ([f8f7a21](https://gitlab.ikatec.cloud/digisac/digisac/commit/f8f7a21798b44e4d89acea190c5930de6892f3b2))


### Features

* **ai-agent-dashboard:** implementa consumo de ia para o agente inteligente no backend ([70065eb](https://gitlab.ikatec.cloud/digisac/digisac/commit/70065ebc83ee5723dc4d77bf2d0f74bce5c1a8c3))



# [3.54.0-mr-3427.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3432.0...v3.54.0-mr-3427.4) (2025-07-29)



## [3.53.2-mr-3442.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3431.8...v3.53.2-mr-3442.0) (2025-07-29)



# [3.54.0-mr-3431.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.1...v3.54.0-mr-3431.8) (2025-07-29)


### Features

* testes unitarios cardResource ([767b158](https://gitlab.ikatec.cloud/digisac/digisac/commit/767b158d9751f50d0d7b3705d1ce157adca557ab))



# [3.54.0-mr-3427.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.1-mr-3452.0...v3.54.0-mr-3427.3) (2025-07-28)



# [3.54.0-mr-3427.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0...v3.54.0-mr-3427.2) (2025-07-28)



# [3.54.0-mr-3431.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3431.6...v3.54.0-mr-3431.7) (2025-07-28)


### Features

* remoção do console.log() ([881ec6e](https://gitlab.ikatec.cloud/digisac/digisac/commit/881ec6e7297e30e6108ce19892836682e52ccb10))



# [3.54.0-mr-3431.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.14...v3.54.0-mr-3431.6) (2025-07-28)


### Features

* corige retornos do back para o front ([d1e3eeb](https://gitlab.ikatec.cloud/digisac/digisac/commit/d1e3eeb598f432b535769d3adc4c3c3892a97420))



# [3.54.0-mr-3431.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.13...v3.54.0-mr-3431.5) (2025-07-28)


### Features

* validação copiloto ([014dc72](https://gitlab.ikatec.cloud/digisac/digisac/commit/014dc72b7423c523a1d501b3cccc151e56ec0715))



# [3.54.0-mr-3431.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-rc.11...v3.54.0-mr-3431.4) (2025-07-28)



# [3.54.0-mr-3431.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3444.2...v3.54.0-mr-3431.3) (2025-07-26)



# [3.54.0-mr-3444.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3431.2...v3.54.0-mr-3444.2) (2025-07-26)



# [3.54.0-mr-3431.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.12...v3.54.0-mr-3431.2) (2025-07-25)



# [3.54.0-mr-3444.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-mr-3448.0...v3.54.0-mr-3444.1) (2025-07-25)


### Features

* corrige nome da variavel no copiloto ([abc2ca2](https://gitlab.ikatec.cloud/digisac/digisac/commit/abc2ca2f13b1b97a1443585bbe28e27ee090f9a1))



# [3.54.0-mr-3444.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.5-mr-3443.0...v3.54.0-mr-3444.0) (2025-07-25)


### Features

* adiciona novas mensagens para falta de credito para transcrição ([62abc0c](https://gitlab.ikatec.cloud/digisac/digisac/commit/62abc0ca1b72d928f81ba7fa274fbac39e4ca7e5))



## [3.51.5-mr-3443.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3424.1...v3.51.5-mr-3443.0) (2025-07-25)


### Bug Fixes

* SD-1491 fixing contacts query ([d453083](https://gitlab.ikatec.cloud/digisac/digisac/commit/d4530830e86ba2c7381f1618c97d62061f2d1f31))


### Features

* atualização do axios ([304c077](https://gitlab.ikatec.cloud/digisac/digisac/commit/304c077c0a8b2a77693bc589d92b4eb5e98591f2))



# [3.53.0-mr-3427.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.5-mr-3439.0...v3.53.0-mr-3427.1) (2025-07-24)



## [3.51.5-mr-3439.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.11...v3.51.5-mr-3439.0) (2025-07-24)


### Bug Fixes

* hsm countdown reinicia com mensagens recebidas tunel ([60efa15](https://gitlab.ikatec.cloud/digisac/digisac/commit/60efa15ab12ab7600fc30d48e9f18469ce54b139))


### Features

* adiciona bloqueio no copilot para msg unica de audio caso não tenha transcrição de audio habilitada ([e13135a](https://gitlab.ikatec.cloud/digisac/digisac/commit/e13135aaa616f7209faa92a8d0d7f54c831405a8))



# [3.53.0-mr-3427.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.5-mr-3432.0...v3.53.0-mr-3427.0) (2025-07-23)


### Features

* **ai-agent-dashboard:** adiciona card e gráfico de Agente Inteligente no front do dashboard com dados fixos até adequação do backend ([1767f04](https://gitlab.ikatec.cloud/digisac/digisac/commit/1767f04dcbd5b56070db586037a470beca268579))



# [3.52.0-mr-3358.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3401.3...v3.52.0-mr-3358.4) (2025-07-16)



# [3.52.0-mr-3358.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3379.3...v3.52.0-mr-3358.3) (2025-07-16)


### Bug Fixes

* correct translation for 'ACTIVE_TICKET_TOPICS' in Portuguese locale ([0ac42c7](https://gitlab.ikatec.cloud/digisac/digisac/commit/0ac42c7d741b9021a853d50302a2ea5b36b565bf))



# [3.52.0-mr-3331.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3284.2...v3.52.0-mr-3331.9) (2025-07-16)



# [3.48.0-mr-3358.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3385.2...v3.48.0-mr-3358.2) (2025-07-16)


### Bug Fixes

* add validation messages for ticket topic name in Portuguese, Spanish, and English ([08fefd2](https://gitlab.ikatec.cloud/digisac/digisac/commit/08fefd228417d3f93b1bf9197ff238b23e48062a))
* correct translation for ticket topics in Portuguese ([0512c5c](https://gitlab.ikatec.cloud/digisac/digisac/commit/0512c5cc3c185c5121125ab2656a5a5ae8ca80f2))
* refactor buildQuery to destructure paginate parameter for clarity ([c55ee35](https://gitlab.ikatec.cloud/digisac/digisac/commit/c55ee357f8e003b485917d27aeaa956f9903c641))



# [3.48.0-mr-3331.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3399.0...v3.48.0-mr-3331.8) (2025-07-14)


### Features

* add validation messages for organization name requirements in localization files ([32acd47](https://gitlab.ikatec.cloud/digisac/digisac/commit/32acd47cf1bcce89135e6c90ca7f871018332fef))



# [3.48.0-mr-3358.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3369.0...v3.48.0-mr-3358.1) (2025-07-14)



# [3.48.0-mr-3331.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3228.8...v3.48.0-mr-3331.7) (2025-07-14)


### Features

* enhance nebula-tokens with new styles for alerts, badges, and typography ([82ed69b](https://gitlab.ikatec.cloud/digisac/digisac/commit/82ed69bc4e4a839849dc63beff9e1e4b8b74b5c9))
* **nebula-tokens:** enhance styling variables for alerts, badges, buttons, and typography ([6eaadda](https://gitlab.ikatec.cloud/digisac/digisac/commit/6eaaddadede09062afc31f0b8b69145d238c6b69))
* update buildQuery functions to include default pagination handling across multiple APIs ([1743788](https://gitlab.ikatec.cloud/digisac/digisac/commit/17437884486d096c6173b6db5ae11ad0121ce6ff))



# [3.48.0-mr-3331.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3288.13...v3.48.0-mr-3331.6) (2025-07-02)



# [3.48.0-mr-3358.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3284.17...v3.48.0-mr-3358.0) (2025-07-02)



# [3.48.0-mr-3331.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.15...v3.48.0-mr-3331.5) (2025-07-02)


### Bug Fixes

* **ticket-topics:** update filter form submission logging and correct archive options values ([4401279](https://gitlab.ikatec.cloud/digisac/digisac/commit/4401279916b774f18d83bef7c190a643dd7816ae))


### Features

* **ticket-topics:** implement CRUD functionality for ticket topics ([afc51bf](https://gitlab.ikatec.cloud/digisac/digisac/commit/afc51bff21041dca975ef09894db086c68bae7d1))



# [3.48.0-mr-3331.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.19...v3.48.0-mr-3331.4) (2025-06-29)





## [3.54.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.3-mr-3496.2...v3.54.3) (2025-08-11)

**Note:** Version bump only for package @digisac/root





## [3.54.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1-mr-3466.3...v3.54.2) (2025-08-07)

**Note:** Version bump only for package @digisac/root





## [3.54.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1-mr-3475.0...v3.54.1) (2025-08-05)

**Note:** Version bump only for package @digisac/root





# [3.54.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-rc.16...v3.54.0) (2025-08-04)

**Note:** Version bump only for package @digisac/root





## [3.53.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.1-mr-3452.0...v3.53.1) (2025-07-29)

**Note:** Version bump only for package @digisac/root





# [3.53.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.14...v3.53.0) (2025-07-28)


### Bug Fixes

* importação global da pasta core ([ef65f5a](https://gitlab.ikatec.cloud/digisac/digisac/commit/ef65f5a1b3a97ba3a2cd57433a86a9efe1e51062))





# [3.52.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-rc.20...v3.52.0) (2025-07-28)

**Note:** Version bump only for package @digisac/root





## [3.51.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.4-mr-3105.30...v3.51.5) (2025-07-28)

**Note:** Version bump only for package @digisac/root





## [3.51.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.4-mr-3417.0...v3.51.4) (2025-07-17)

**Note:** Version bump only for package @digisac/root





## [3.51.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.2...v3.51.3) (2025-07-16)



## [3.51.1-mr-3405.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.0...v3.51.1-mr-3405.0) (2025-07-15)


### Bug Fixes

* **whatsapp:** atualização waVersion para 2.3000.1024760756 ([fb866e0](https://gitlab.ikatec.cloud/digisac/digisac/commit/fb866e0204cf69d6c595d1ad771c39bfd0565ce7))





## [3.51.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.2-mr-3408.0...v3.51.2) (2025-07-16)

**Note:** Version bump only for package @digisac/root





## [3.51.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.1-mr-3406.0...v3.51.1) (2025-07-16)

**Note:** Version bump only for package @digisac/root





# [3.51.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.0-rc.10...v3.51.0) (2025-07-14)

**Note:** Version bump only for package @digisac/root





## [3.50.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.2-mr-3360.3...v3.50.3) (2025-07-14)

**Note:** Version bump only for package @digisac/root





## [3.50.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.2-mr-3381.1...v3.50.2) (2025-07-08)

**Note:** Version bump only for package @digisac/root





## [3.50.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.20...v3.50.1) (2025-07-07)

**Note:** Version bump only for package @digisac/root





# [3.50.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.2...v3.50.0) (2025-07-07)



# [3.50.0-rc.18](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1...v3.50.0-rc.18) (2025-07-07)



# [3.50.0-mr-3376.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1-mr-3368.0...v3.50.0-mr-3376.0) (2025-07-07)


### Bug Fixes

* **departments:** refactor buildQuery to destructure paginate parameter ([e559cd6](https://gitlab.ikatec.cloud/digisac/digisac/commit/e559cd66d69211b78258bfb376979ed7fc0ddb6c))



# [3.50.0-rc.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3364.1...v3.50.0-rc.17) (2025-07-03)



# [3.50.0-mr-3364.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.16...v3.50.0-mr-3364.1) (2025-07-03)



# [3.50.0-rc.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3365.0...v3.50.0-rc.16) (2025-07-03)



# [3.50.0-mr-3365.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3364.0...v3.50.0-mr-3365.0) (2025-07-03)


### Bug Fixes

* **bot-interpolation-undefined:** corrige interpolação para funcionar corretamente com variaveis novas e antigas ([d0ea226](https://gitlab.ikatec.cloud/digisac/digisac/commit/d0ea226454832c9168f67dc93f200cfe204df27c))



# [3.50.0-mr-3364.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.15...v3.50.0-mr-3364.0) (2025-07-02)


### Bug Fixes

* SD-1562 (defect)  new contacts not showing up in the chat ([6e331fe](https://gitlab.ikatec.cloud/digisac/digisac/commit/6e331feab633e0a46ad00ad6883d98a6314a2312))



# [3.50.0-rc.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3356.2...v3.50.0-rc.15) (2025-07-02)



# [3.50.0-mr-3356.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3356.1...v3.50.0-mr-3356.2) (2025-07-01)



# [3.50.0-mr-3356.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.14...v3.50.0-mr-3356.1) (2025-07-01)



# [3.50.0-rc.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.20...v3.50.0-rc.14) (2025-06-30)



# [3.49.0-rc.20](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0...v3.49.0-rc.20) (2025-06-30)



# [3.50.0-rc.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3311.1...v3.50.0-rc.13) (2025-06-30)



# [3.50.0-mr-3311.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.12...v3.50.0-mr-3311.1) (2025-06-30)



# [3.50.0-rc.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3356.0...v3.50.0-rc.12) (2025-06-30)



# [3.50.0-mr-3356.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3355.1...v3.50.0-mr-3356.0) (2025-06-30)



# [3.50.0-mr-3355.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3356.0...v3.50.0-mr-3355.1) (2025-06-30)



# [3.49.0-mr-3356.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3355.0...v3.49.0-mr-3356.0) (2025-06-30)


### Features

* ajuster experience copiloto ([119c9a3](https://gitlab.ikatec.cloud/digisac/digisac/commit/119c9a3137ca2737f697bdae1d5b31e5c3ede6bf))



# [3.50.0-mr-3355.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.11...v3.50.0-mr-3355.0) (2025-06-30)


### Bug Fixes

* **bot-simulator-interpolation-error:** corrige função de interpolação de variaveis no simulador do bot ([8bb6898](https://gitlab.ikatec.cloud/digisac/digisac/commit/8bb6898d84594f6e1c75da1b260bf7feeb62fa50))



# [3.50.0-rc.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3311.0...v3.50.0-rc.11) (2025-06-30)



# [3.50.0-mr-3311.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3311.7...v3.50.0-mr-3311.0) (2025-06-30)



# [3.49.0-mr-3311.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.4...v3.49.0-mr-3311.7) (2025-06-30)



# [3.50.0-mr-3343.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.10...v3.50.0-mr-3343.9) (2025-06-30)



# [3.50.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3343.8...v3.50.0-rc.10) (2025-06-30)



# [3.50.0-mr-3343.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3334.2...v3.50.0-mr-3343.8) (2025-06-30)



# [3.50.0-mr-3334.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.19...v3.50.0-mr-3334.2) (2025-06-30)



# [3.49.0-mr-3311.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.9...v3.49.0-mr-3311.6) (2025-06-27)


### Bug Fixes

* corrigindo exportacao de relatorio do funil ([5a243d3](https://gitlab.ikatec.cloud/digisac/digisac/commit/5a243d3c08b96766200cd7dd528fedf3e4d290d4))



# [3.50.0-rc.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3259.8...v3.50.0-rc.9) (2025-06-27)



# [3.50.0-mr-3259.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.2-mr-3259.7...v3.50.0-mr-3259.8) (2025-06-27)



## [3.48.2-mr-3259.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.8...v3.48.2-mr-3259.7) (2025-06-27)



# [3.50.0-rc.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.1-mr-3323.0...v3.50.0-rc.8) (2025-06-27)



## [3.47.1-mr-3323.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3343.7...v3.47.1-mr-3323.0) (2025-06-27)



# [3.50.0-mr-3343.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3334.1...v3.50.0-mr-3343.7) (2025-06-27)



# [3.50.0-mr-3334.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.7...v3.50.0-mr-3334.1) (2025-06-27)



# [3.50.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3318.3...v3.50.0-rc.7) (2025-06-27)



# [3.50.0-mr-3318.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.3-mr-3349.0...v3.50.0-mr-3318.3) (2025-06-27)



## [3.48.2-mr-3259.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.1-mr-3259.5...v3.48.2-mr-3259.6) (2025-06-27)



## [3.45.1-mr-3259.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3348.1...v3.45.1-mr-3259.5) (2025-06-26)


### Bug Fixes

* **myplan-intl:** initial load on macos ([e9fed8e](https://gitlab.ikatec.cloud/digisac/digisac/commit/e9fed8e83e9e68fb714b2a744de56d95a6d023c9))



# [3.50.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.17...v3.50.0-rc.6) (2025-06-26)



# [3.50.0-mr-3343.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3343.5...v3.50.0-mr-3343.6) (2025-06-26)



# [3.50.0-mr-3343.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.2...v3.50.0-mr-3343.5) (2025-06-26)



# [3.50.0-mr-3343.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3343.3...v3.50.0-mr-3343.4) (2025-06-26)


### Bug Fixes

* changed to not mutate the original filter dates ([9092aca](https://gitlab.ikatec.cloud/digisac/digisac/commit/9092acab1f294687ec1b20eb1d3ecb285d6136e5))



# [3.50.0-mr-3343.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3343.2...v3.50.0-mr-3343.3) (2025-06-26)



# [3.50.0-mr-3343.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3343.1...v3.50.0-mr-3343.2) (2025-06-26)


### Bug Fixes

* alter fix startPeriod hour to 00:00:00 on page filter ([ea6b5b6](https://gitlab.ikatec.cloud/digisac/digisac/commit/ea6b5b6f715a7fe8f8711bb694676ed75a52f87c))



# [3.50.0-mr-3343.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1-mr-3305.3...v3.50.0-mr-3343.1) (2025-06-26)


### Bug Fixes

* fix evaluation metrics filter by day period ([466afe6](https://gitlab.ikatec.cloud/digisac/digisac/commit/466afe67e35fe813c4147d4f7b5babb4c9315659))
* pontos de teste ([3b0aca7](https://gitlab.ikatec.cloud/digisac/digisac/commit/3b0aca7ac7c038b854aa3ac6f133fac0a4c6a9f7))



# [3.50.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.3...v3.50.0-rc.5) (2025-06-26)



# [3.50.0-mr-3343.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.1...v3.50.0-mr-3343.0) (2025-06-26)


### Bug Fixes

* fixed service filter on evaluation metrics page ([a8f0347](https://gitlab.ikatec.cloud/digisac/digisac/commit/a8f0347463a30359336a338f624983f53f284ced))



# [3.50.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.15...v3.50.0-rc.4) (2025-06-26)



# [3.50.0-mr-3341.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3311.5...v3.50.0-mr-3341.0) (2025-06-25)



# [3.49.0-mr-3311.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3311.4...v3.49.0-mr-3311.5) (2025-06-25)


### Bug Fixes

* cleaning the gain filters. ([a5985b5](https://gitlab.ikatec.cloud/digisac/digisac/commit/a5985b5013b551cc050a2a1a2ca7d5b20ddf8eaa))



# [3.49.0-mr-3311.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3318.2...v3.49.0-mr-3311.4) (2025-06-25)


### Bug Fixes

* cleaning the gain filters. ([c05aa56](https://gitlab.ikatec.cloud/digisac/digisac/commit/c05aa56d49d1b119b9f4c4c326027f75ee52b64c))



# [3.50.0-mr-3318.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3318.1...v3.50.0-mr-3318.2) (2025-06-25)


### Features

* removendo label new ([50fa989](https://gitlab.ikatec.cloud/digisac/digisac/commit/50fa9891ebf078ba70f69315b9e0462bf784bab1))



# [3.49.0-mr-3318.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3334.0...v3.49.0-mr-3318.1) (2025-06-25)



# [3.50.0-mr-3334.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.3...v3.50.0-mr-3334.0) (2025-06-25)



# [3.50.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.2...v3.50.0-rc.3) (2025-06-25)



# [3.50.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3336.0...v3.50.0-rc.2) (2025-06-25)



# [3.50.0-mr-3336.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.14...v3.50.0-mr-3336.0) (2025-06-25)


### Bug Fixes

* ensure comment messages with files emit proper update events ([3fa8cdb](https://gitlab.ikatec.cloud/digisac/digisac/commit/3fa8cdbaaa3f2da64c45f68a8071c92adb438570))



# [3.50.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.12...v3.50.0-rc.1) (2025-06-25)


### Bug Fixes

* **version:** versao alterada para 3.50.0-rc.0 ([66b9617](https://gitlab.ikatec.cloud/digisac/digisac/commit/66b9617e44e3b2c4a46451fc738eddf149be03b4))



# [3.49.0-mr-3334.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3334.0...v3.49.0-mr-3334.1) (2025-06-24)



# [3.49.0-mr-3334.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.9...v3.49.0-mr-3334.0) (2025-06-24)



# [3.49.0-mr-3311.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3311.2...v3.49.0-mr-3311.3) (2025-06-24)



# [3.49.0-mr-3311.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.8...v3.49.0-mr-3311.2) (2025-06-24)



# [3.49.0-mr-3311.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3327.1...v3.49.0-mr-3311.1) (2025-06-24)



# [3.49.0-mr-3327.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3311.0...v3.49.0-mr-3327.1) (2025-06-24)



# [3.49.0-mr-3311.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.7...v3.49.0-mr-3311.0) (2025-06-24)



# [3.49.0-mr-3318.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.6...v3.49.0-mr-3318.0) (2025-06-23)



# [3.49.0-mr-3327.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3327.0...v3.49.0-mr-3327.0) (2025-06-23)


### Features

* implementando filtro por data de ganho ([bd39415](https://gitlab.ikatec.cloud/digisac/digisac/commit/bd39415cfe91a1c093c1b7abbb0abfc0f8ea8bf6))



# [3.48.0-mr-3327.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.17...v3.48.0-mr-3327.0) (2025-06-23)



# [3.49.0-mr-3300.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.3...v3.49.0-mr-3300.8) (2025-06-21)


### Features

* **BaseBotService:** implementa uso de alias para variáveis de interpolação ([8866f40](https://gitlab.ikatec.cloud/digisac/digisac/commit/8866f40203d80e53922ba5a0726d83aa0d989929))



# [3.49.0-mr-3300.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3300.6...v3.49.0-mr-3300.7) (2025-06-20)


### Features

* **SDIG-657-improved-interpolation-of-variables:** corrige label com  informações textuais e ajusta função que retorna o nome do contato ([2cef054](https://gitlab.ikatec.cloud/digisac/digisac/commit/2cef0549631e3d49589a215665e3be59bfc3a978))



# [3.49.0-mr-3300.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3326.0...v3.49.0-mr-3300.6) (2025-06-20)



# [3.49.0-mr-3326.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3325.0...v3.49.0-mr-3326.0) (2025-06-20)


### Bug Fixes

* SD-1526 - notifications would not work for new contacts ([1cd4465](https://gitlab.ikatec.cloud/digisac/digisac/commit/1cd44652fe43b2c1217e93aa458d5dc26de98adc))


### Features

* Finalizing the demand. ([c4747c5](https://gitlab.ikatec.cloud/digisac/digisac/commit/c4747c561ba191d912116213be7c3355cc15352f))
* Remove debug ([d4750b6](https://gitlab.ikatec.cloud/digisac/digisac/commit/d4750b620c69c26637c0f372ba834d76c768bc89))
* Remove debug ([411a405](https://gitlab.ikatec.cloud/digisac/digisac/commit/411a40506b0c631ff8795ad87548aef475925ed0))



# [3.48.0-mr-3311.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3311.0...v3.48.0-mr-3311.1) (2025-06-19)



# [3.48.0-mr-3311.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3317.0...v3.48.0-mr-3311.0) (2025-06-19)


### Features

* Completing the filter in saga. ([9c58892](https://gitlab.ikatec.cloud/digisac/digisac/commit/9c588921ea4abe4305b50f559c21f5d50efaa105))
* correcting the screen labels. ([b590ea1](https://gitlab.ikatec.cloud/digisac/digisac/commit/b590ea10ccdf5263e57d229de7a531f1b476401f))



# [3.47.0-mr-3300.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.12...v3.47.0-mr-3300.5) (2025-06-17)


### Features

* **SDIG-657-improved-interpolation-of-variables:** adiciona novas variáveis de interpolação no bot e ajusta limite de itens no autocomplete ([b7a2b0a](https://gitlab.ikatec.cloud/digisac/digisac/commit/b7a2b0a06b2283a7bc8db393289e3e90d907ef40))
* Validating whether the status is gained. ([4b5a981](https://gitlab.ikatec.cloud/digisac/digisac/commit/4b5a9813a498a8225da19e65209dd28e5616aa70))



## [3.45.1-mr-3259.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.1-mr-3259.3...v3.45.1-mr-3259.4) (2025-06-17)



## [3.45.1-mr-3259.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.11...v3.45.1-mr-3259.3) (2025-06-17)


### Bug Fixes

* **myplan-intl:** ensure initial language loads with useEffect ([d38a92f](https://gitlab.ikatec.cloud/digisac/digisac/commit/d38a92f0e6b606d821a7223254d36e1f39d20d94))



## [3.45.1-mr-3259.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.46.1-mr-3304.0...v3.45.1-mr-3259.2) (2025-06-16)


### Bug Fixes

* **myplan-intl:** initial language load ([b99e652](https://gitlab.ikatec.cloud/digisac/digisac/commit/b99e652fe03e6ab52eec8d8feb5f3ea4add3e649))
* **myplan:** post message dev environment ([d851682](https://gitlab.ikatec.cloud/digisac/digisac/commit/d8516822cc4ebb35a46629a36d265313ae0ed597))



## [3.45.1-mr-3259.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.1-mr-3259.0...v3.45.1-mr-3259.1) (2025-06-16)



## [3.45.1-mr-3259.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3308.4...v3.45.1-mr-3259.0) (2025-06-16)


### Features

* add new filter ([8f4aa7d](https://gitlab.ikatec.cloud/digisac/digisac/commit/8f4aa7d34d103ac7edb479e191ee9bc4e31617c4))
* **MyPlan:** send i18n changes to iframe ([8a4227c](https://gitlab.ikatec.cloud/digisac/digisac/commit/8a4227c99729417288a7fa1b4a67d47a0c78182b))





## [3.49.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1-mr-3383.0...v3.49.2) (2025-07-07)



## [3.49.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1-mr-3368.0...v3.49.1) (2025-07-07)

**Note:** Version bump only for package @digisac/root





## [3.49.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1-mr-3368.0...v3.49.1) (2025-07-07)

**Note:** Version bump only for package @digisac/root





# [3.49.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.3...v3.49.0) (2025-06-30)



# [3.49.0-mr-3337.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.19...v3.49.0-mr-3337.4) (2025-06-30)



# [3.49.0-rc.19](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3352.0...v3.49.0-rc.19) (2025-06-27)



# [3.49.0-mr-3352.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.3-mr-3349.0...v3.49.0-mr-3352.0) (2025-06-27)


### Bug Fixes

* remove redundant label margin style and enforce margin reset ([2cc19d4](https://gitlab.ikatec.cloud/digisac/digisac/commit/2cc19d45c7e13295f706e83db0ec1794831a73c9))



# [3.49.0-rc.18](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3348.1...v3.49.0-rc.18) (2025-06-27)



# [3.49.0-mr-3348.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3348.0...v3.49.0-mr-3348.1) (2025-06-26)


### Bug Fixes

* adjust token name label in the view modal ([5e362ab](https://gitlab.ikatec.cloud/digisac/digisac/commit/5e362ab8b5bdc132a3f71adee176fc4292fd99d6))



# [3.49.0-mr-3348.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.17...v3.49.0-mr-3348.0) (2025-06-26)


### Bug Fixes

* adjust label for webhook and profile name ([c57964d](https://gitlab.ikatec.cloud/digisac/digisac/commit/c57964d45f7dcf24dc02c625c3509d7d9c6d4d25))



# [3.49.0-rc.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.2...v3.49.0-rc.17) (2025-06-26)



# [3.49.0-rc.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.3...v3.49.0-rc.16) (2025-06-26)



# [3.49.0-mr-3337.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.2...v3.49.0-mr-3337.3) (2025-06-26)



# [3.49.0-mr-3337.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3344.0...v3.49.0-mr-3337.2) (2025-06-26)



# [3.49.0-mr-3344.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.1...v3.49.0-mr-3344.0) (2025-06-26)



# [3.49.0-mr-3337.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.15...v3.49.0-mr-3337.1) (2025-06-26)



# [3.49.0-rc.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.6...v3.49.0-rc.15) (2025-06-26)



# [3.49.0-mr-3317.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.14...v3.49.0-mr-3317.6) (2025-06-26)



# [3.49.0-rc.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.13...v3.49.0-rc.14) (2025-06-25)



# [3.49.0-rc.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1...v3.49.0-rc.13) (2025-06-25)



# [3.49.0-rc.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3304.0...v3.49.0-rc.12) (2025-06-25)



# [3.49.0-mr-3304.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1-mr-3338.0...v3.49.0-mr-3304.0) (2025-06-25)



# [3.49.0-rc.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0...v3.49.0-rc.11) (2025-06-24)



# [3.49.0-mr-3134.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3134.2...v3.49.0-mr-3134.3) (2025-06-24)



# [3.49.0-mr-3134.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.0...v3.49.0-mr-3134.2) (2025-06-24)



# [3.49.0-mr-3337.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.10...v3.49.0-mr-3337.0) (2025-06-24)



# [3.49.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.9...v3.49.0-rc.10) (2025-06-24)



# [3.49.0-rc.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3330.1...v3.49.0-rc.9) (2025-06-24)



# [3.49.0-mr-3330.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.8...v3.49.0-mr-3330.1) (2025-06-24)


### Bug Fixes

* adjust job import in workers ([766b47b](https://gitlab.ikatec.cloud/digisac/digisac/commit/766b47b67daea86aca8b6417cbb03dbe582488a4))



# [3.49.0-rc.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.7...v3.49.0-rc.8) (2025-06-24)



# [3.49.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3330.0...v3.49.0-rc.7) (2025-06-24)



# [3.49.0-mr-3330.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.5...v3.49.0-mr-3330.0) (2025-06-23)


### Bug Fixes

* add correct value to no expires token create ([0aca3bb](https://gitlab.ikatec.cloud/digisac/digisac/commit/0aca3bba27a1cde6f8ae448f28c5907bc6f3593e))



# [3.49.0-mr-3317.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3329.0...v3.49.0-mr-3317.5) (2025-06-23)


### Bug Fixes

* **TicketInactiveJob:** corrige casos em que a última mensagem do chamado é um comentário ([58afd70](https://gitlab.ikatec.cloud/digisac/digisac/commit/58afd70fb935caf6e79be35de5bb02f5914ca6ca))



# [3.49.0-mr-3329.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.6...v3.49.0-mr-3329.0) (2025-06-23)


### Features

* removendo label de new da funcionalidade smart summary triggers ([db48b04](https://gitlab.ikatec.cloud/digisac/digisac/commit/db48b04ea0a8b78392c3d4496002ecc331c80a0a))



# [3.49.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3325.1...v3.49.0-rc.6) (2025-06-23)



# [3.49.0-mr-3325.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.5...v3.49.0-mr-3325.1) (2025-06-23)



# [3.49.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3310.1...v3.49.0-rc.5) (2025-06-23)



# [3.49.0-mr-3310.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.4...v3.49.0-mr-3310.1) (2025-06-23)



# [3.49.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.17...v3.49.0-rc.4) (2025-06-23)



# [3.49.0-mr-3317.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.3...v3.49.0-mr-3317.4) (2025-06-23)


### Features

* **TicketInactiveJob:** implementa mais melhorias sugeridas em CR ([839f448](https://gitlab.ikatec.cloud/digisac/digisac/commit/839f44880c50bc1bc24073077c1c1b39b37a62aa))
* **TicketInactiveJob:** implementa melhorias sugeridas em CR ([a5216d6](https://gitlab.ikatec.cloud/digisac/digisac/commit/a5216d6fcda313a801d2012fbe7152d9ea2c12ea))



# [3.49.0-mr-3317.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.2...v3.49.0-mr-3317.3) (2025-06-21)


### Bug Fixes

* **TicketInactiveJob:** correção na indentação ([4697e74](https://gitlab.ikatec.cloud/digisac/digisac/commit/4697e74932e8219001176d89f199c5753368fb31))
* **TicketInactiveJob:** utilizar contactResource da classe BaseBotService ([17e1842](https://gitlab.ikatec.cloud/digisac/digisac/commit/17e184298c3ec4567dd91df42f52f8e045af31b6))



# [3.49.0-mr-3317.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.1...v3.49.0-mr-3317.2) (2025-06-20)



# [3.49.0-mr-3317.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3325.0...v3.49.0-mr-3317.1) (2025-06-20)



# [3.49.0-mr-3325.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.3...v3.49.0-mr-3325.0) (2025-06-20)


### Features

* adiciona endpoint no super admin para que o agnus possa habilitar o funil de vendas ([bfd29c2](https://gitlab.ikatec.cloud/digisac/digisac/commit/bfd29c2bb7a42980d4541c8c907af36b78824f73))



# [3.49.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3297.0...v3.49.0-rc.3) (2025-06-20)



# [3.49.0-mr-3297.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3310.0...v3.49.0-mr-3297.0) (2025-06-20)



# [3.49.0-mr-3310.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3297.3...v3.49.0-mr-3310.0) (2025-06-20)



# [3.48.0-mr-3297.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3310.4...v3.48.0-mr-3297.3) (2025-06-20)



# [3.48.0-mr-3310.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3324.0...v3.48.0-mr-3310.4) (2025-06-20)


### Bug Fixes

* pontos do teste ([5fbb25c](https://gitlab.ikatec.cloud/digisac/digisac/commit/5fbb25cb71e9dd4979db6dd089e443f35dfb0a85))
* pontos do teste ([362033e](https://gitlab.ikatec.cloud/digisac/digisac/commit/362033e4468111890653097da3e5099ca5960db6))



# [3.48.0-mr-3317.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.2...v3.48.0-mr-3317.0) (2025-06-19)


### Features

* **TicketInactiveJob:** implementa melhorias relacionadas ao lock ([a6a8110](https://gitlab.ikatec.cloud/digisac/digisac/commit/a6a81101f419308232b2528f537b3e3d4c6566e7))



# [3.49.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3229.7...v3.49.0-rc.2) (2025-06-18)



# [3.49.0-mr-3229.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3229.7...v3.49.0-mr-3229.7) (2025-06-18)



# [3.48.0-mr-3229.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.1...v3.48.0-mr-3229.7) (2025-06-18)


### Bug Fixes

* adding CSAT to plan history. ([b0a9505](https://gitlab.ikatec.cloud/digisac/digisac/commit/b0a9505728b77f21b64321180edd3af2d2886690))
* removing negative number. ([3a37b04](https://gitlab.ikatec.cloud/digisac/digisac/commit/3a37b0489d8344b4f673efc95ace77c43fdfe44e))



# [3.49.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.16...v3.49.0-rc.1) (2025-06-18)


### Bug Fixes

* **version:** versao alterada para 3.49.0-rc.0 ([20f9df2](https://gitlab.ikatec.cloud/digisac/digisac/commit/20f9df2692dfd8575b1d686b22b074e43282ab18))



# [3.48.0-mr-3297.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3297.1...v3.48.0-mr-3297.2) (2025-06-18)



# [3.48.0-mr-3297.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.1-mr-3319.0...v3.48.0-mr-3297.1) (2025-06-18)


### Bug Fixes

* Fixing to fetch the account id provided by the url. ([a3247e0](https://gitlab.ikatec.cloud/digisac/digisac/commit/a3247e00a5d09b84c4813f0f4a4ffd78be5badc8))


### Features

* **TicketInactiveJob:** melhora vazão no processamento de tickets inativos ([6554acc](https://gitlab.ikatec.cloud/digisac/digisac/commit/6554acc8e424964a3738e276e2c9e096e5bc8399))



## [3.46.1-mr-3304.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.10...v3.46.1-mr-3304.0) (2025-06-16)


### Bug Fixes

* reordering imports ([3551bc6](https://gitlab.ikatec.cloud/digisac/digisac/commit/3551bc658f59ffc7c7bc8a8283a47abd02836817))



# [3.48.0-mr-3134.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.9...v3.48.0-mr-3134.1) (2025-06-16)



# [3.48.0-mr-3229.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.8...v3.48.0-mr-3229.6) (2025-06-16)



# [3.48.0-mr-3310.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3024.11...v3.48.0-mr-3310.3) (2025-06-16)



# [3.48.0-mr-3310.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0-rc.13...v3.48.0-mr-3310.2) (2025-06-13)



# [3.48.0-mr-3310.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3310.0...v3.48.0-mr-3310.1) (2025-06-12)



# [3.48.0-mr-3310.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3236.1...v3.48.0-mr-3310.0) (2025-06-12)


### Features

* redesign de mensagens de audio no copilot ([c40f7b6](https://gitlab.ikatec.cloud/digisac/digisac/commit/c40f7b6132525a819c41b6c9163ddb542ccc104d))



# [3.48.0-mr-3297.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3248.2...v3.48.0-mr-3297.0) (2025-06-12)


### Bug Fixes

* Correcting comment in MR. ([71ad875](https://gitlab.ikatec.cloud/digisac/digisac/commit/71ad87582d45bf68092bf5f5ced8b525203cba55))



# [3.48.0-mr-3229.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0...v3.48.0-mr-3229.5) (2025-06-11)


### Bug Fixes

* SD-1528 - Buffer audios before converting ([6519a6b](https://gitlab.ikatec.cloud/digisac/digisac/commit/6519a6b8ddad736917154ac8533555628770eb8d))



# [3.47.0-mr-3297.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0-mr-3297.3...v3.47.0-mr-3297.4) (2025-06-10)



# [3.47.0-mr-3297.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0-rc.11...v3.47.0-mr-3297.3) (2025-06-10)



# [3.44.0-mr-3134.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.2-mr-3251.0...v3.44.0-mr-3134.0) (2025-05-30)



# [3.44.0-mr-3024.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.46.0-mr-3234.1...v3.44.0-mr-3024.5) (2025-05-30)


### Bug Fixes

* update localization strings for tag linking in multiple languages ([9956ba5](https://gitlab.ikatec.cloud/digisac/digisac/commit/9956ba5ef59b9756b784afb7157c481303a2ae66))



# [3.43.0-mr-3134.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-mr-3024.4...v3.43.0-mr-3134.8) (2025-05-28)


### Bug Fixes

* enable save button only when form is valid ([ee8fc27](https://gitlab.ikatec.cloud/digisac/digisac/commit/ee8fc27d2986b17d52a59175b09eec868ac23998))



# [3.44.0-mr-3024.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-mr-3225.3...v3.44.0-mr-3024.4) (2025-05-28)


### Bug Fixes

* disable submit button if form is invalid or not dirty in TagsForm and LinkTagsToDepartmentDialog ([ad6652d](https://gitlab.ikatec.cloud/digisac/digisac/commit/ad6652df0eff32dbf96c903c3ecca973493a32aa))
* refactor filter handling and debounce logic ([116325f](https://gitlab.ikatec.cloud/digisac/digisac/commit/116325f560afb0f1ad4a86f73f873064ea69b9dd))
* refactor InputFilter to use local state and improve handlers ([a1e5336](https://gitlab.ikatec.cloud/digisac/digisac/commit/a1e53369f3f30be2fba1edb69f800f19380edfcc))
* update InputText width in InputFilter component ([c46fb81](https://gitlab.ikatec.cloud/digisac/digisac/commit/c46fb81d3c0363d334b542071c28bdb030f9a331))



# [3.44.0-mr-3024.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-mr-3024.2...v3.44.0-mr-3024.3) (2025-05-28)



# [3.44.0-mr-3024.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-rc.10...v3.44.0-mr-3024.2) (2025-05-28)


### Bug Fixes

* update nebula-react and nebula-tokens to version 0.0.1-alpha.24 ([ef87c43](https://gitlab.ikatec.cloud/digisac/digisac/commit/ef87c4362d115e0328ee67a57b3cd78cbb88dafc))



# [3.44.0-mr-3024.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-mr-3024.0...v3.44.0-mr-3024.1) (2025-05-27)


### Bug Fixes

* update nebula-react and nebula-tokens to version 0.0.1-alpha.21 ([d45dfbe](https://gitlab.ikatec.cloud/digisac/digisac/commit/d45dfbe12cc8bbe56ed5c173e22945f02494e62a))



# [3.44.0-mr-3024.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-mr-3234.0...v3.44.0-mr-3024.0) (2025-05-27)


### Bug Fixes

* update tag creation messages and clean up dialog components ([be4d293](https://gitlab.ikatec.cloud/digisac/digisac/commit/be4d2939ab750d31870c5f6839a1d10772ef2c26))



# [3.43.0-mr-3227.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-rc.5...v3.43.0-mr-3227.0) (2025-05-26)


### Features

* add new event for personal access token notification with i18n ([f2425dd](https://gitlab.ikatec.cloud/digisac/digisac/commit/f2425ddcd52b330c5eb308f432643b4dd87968bc))
* create a cronjob to detect how many tokens needs a expiration notification ([c2988ba](https://gitlab.ikatec.cloud/digisac/digisac/commit/c2988ba6170f7694541650f4d4429b4900f3845a))



# [3.43.0-mr-3201.26](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.2-mr-3222.0...v3.43.0-mr-3201.26) (2025-05-22)



# [3.43.0-mr-3201.25](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3201.24...v3.43.0-mr-3201.25) (2025-05-22)


### Bug Fixes

* **personal-token:** add paginate false on query ([3489c28](https://gitlab.ikatec.cloud/digisac/digisac/commit/3489c2873bc652c081c2867225e5df7000689ba9))



# [3.43.0-mr-3201.24](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3201.23...v3.43.0-mr-3201.24) (2025-05-22)



# [3.43.0-mr-3201.23](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3201.22...v3.43.0-mr-3201.23) (2025-05-22)


### Bug Fixes

* fixed paginate parameters ([b51f71b](https://gitlab.ikatec.cloud/digisac/digisac/commit/b51f71b1492f571fe1e45342ff1118ecbb5d6483))



# [3.43.0-mr-3201.22](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3201.21...v3.43.0-mr-3201.22) (2025-05-22)



# [3.43.0-mr-3201.21](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3201.20...v3.43.0-mr-3201.21) (2025-05-22)



# [3.43.0-mr-3201.20](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-mr-3220.0...v3.43.0-mr-3201.20) (2025-05-22)



# [3.43.0-mr-3213.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0...v3.43.0-mr-3213.6) (2025-05-21)



# [3.43.0-mr-3213.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-rc.1...v3.43.0-mr-3213.5) (2025-05-21)



# [3.43.0-mr-3213.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.2-mr-3196.2...v3.43.0-mr-3213.4) (2025-05-21)


### Features

* obfuscate access token on show route ([3a8e8f6](https://gitlab.ikatec.cloud/digisac/digisac/commit/3a8e8f6590d413478eeabbaaf19a59e66763258e))



# [3.43.0-mr-3213.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.1-mr-3158.7...v3.43.0-mr-3213.3) (2025-05-20)



# [3.43.0-mr-3213.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-mr-3212.5...v3.43.0-mr-3213.2) (2025-05-20)



# [3.43.0-mr-3134.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3129.5...v3.43.0-mr-3134.7) (2025-05-15)



# [3.43.0-mr-3134.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3134.5...v3.43.0-mr-3134.6) (2025-05-14)



# [3.43.0-mr-3134.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-rc.12...v3.43.0-mr-3134.5) (2025-05-14)



## [3.42.1-mr-3134.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-rc.11...v3.42.1-mr-3134.4) (2025-05-14)


### Features

* **departments:** update success messages and improve filter display ([b37f810](https://gitlab.ikatec.cloud/digisac/digisac/commit/b37f81034ec9409a96b2e5724287b57de521d086))



# [3.43.0-mr-3192.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3188.1...v3.43.0-mr-3192.0) (2025-05-13)


### Features

* validate no named token is valid or not and return status from access token ([9d46540](https://gitlab.ikatec.cloud/digisac/digisac/commit/9d4654027d73ccd0568576e4769fb768f339cb24))



## [3.42.1-mr-3134.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-rc.10...v3.42.1-mr-3134.3) (2025-05-13)


### Features

* create migration and update model OAuthAccessToken ([46377ad](https://gitlab.ikatec.cloud/digisac/digisac/commit/46377ad5229c84d11dd11d4486fddd1ef8def47e))
* **departments:** remove old files ([a1ebbf1](https://gitlab.ikatec.cloud/digisac/digisac/commit/a1ebbf15422e02019e076a3c2f308d7113bdad49))



# [3.43.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3136.1...v3.43.0-rc.4) (2025-05-09)



# [3.43.0-mr-3136.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3136.0...v3.43.0-mr-3136.1) (2025-05-09)



# [3.43.0-mr-3136.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-rc.3...v3.43.0-mr-3136.0) (2025-05-09)



# [3.43.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.2...v3.43.0-rc.3) (2025-05-09)


### Bug Fixes

* **ci:** corrige bump de versão ([55c127d](https://gitlab.ikatec.cloud/digisac/digisac/commit/55c127dc19140e5bc3f4724d4e10d90a05005ebe))
* **ci:** corrige bump de versão ([2baa4b3](https://gitlab.ikatec.cloud/digisac/digisac/commit/2baa4b344883323e50fb25625109b34da143bb6e))
* **ci:** corrige gitlab-ci.yml ([213f8d2](https://gitlab.ikatec.cloud/digisac/digisac/commit/213f8d2efd28d569558e8b8cfcf549d6da056285))



## [3.42.1-mr-3169.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3168.0...v3.42.1-mr-3169.0) (2025-05-07)


### Bug Fixes

* add gap between dropdown columns in DropdownHeader ([4af3953](https://gitlab.ikatec.cloud/digisac/digisac/commit/4af39539da62074913ca2bbfc3dbde4a8bfbe43a))
* import React to ensure proper JSX handling in TagCreateDialog ([a11fc3e](https://gitlab.ikatec.cloud/digisac/digisac/commit/a11fc3e8addc7c86b0ed5c0cd739a49023893124))
* simplify multiValueRemove styles in TagsSelectContainer ([d298e3f](https://gitlab.ikatec.cloud/digisac/digisac/commit/d298e3f00169dc7919be5f723befc5fd761c959b))


### Features

* add InputFilter component and integrate into tags list ([2f690dd](https://gitlab.ikatec.cloud/digisac/digisac/commit/2f690dd98291eeb5b492372f1e7757b85c429bbb))



## [3.42.1-mr-3024.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.1...v3.42.1-mr-3024.0) (2025-05-06)





## [3.48.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.3-mr-3349.0...v3.48.3) (2025-06-30)

**Note:** Version bump only for package @digisac/root





## [3.48.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1-mr-3305.3...v3.48.2) (2025-06-26)

**Note:** Version bump only for package @digisac/root





## [3.48.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1-mr-3338.0...v3.48.1) (2025-06-25)

**Note:** Version bump only for package @digisac/root





# [3.48.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.17...v3.48.0) (2025-06-24)

**Note:** Version bump only for package @digisac/root





## [3.47.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0-rc.14...v3.47.3) (2025-06-18)

**Note:** Version bump only for package @digisac/root





## [3.47.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.2-mr-3313.0...v3.47.2) (2025-06-18)

**Note:** Version bump only for package @digisac/root





## [3.47.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.1-mr-3319.0...v3.47.1) (2025-06-18)

**Note:** Version bump only for package @digisac/root





# [3.47.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.46.0...v3.47.0) (2025-06-11)



## [3.45.1-mr-3254.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.46.0-rc.12...v3.45.1-mr-3254.0) (2025-06-03)


### Features

* **adapter:** ajustes ([6e67857](https://gitlab.ikatec.cloud/digisac/digisac/commit/6e678576699c8539cdc80ce81596200dae34425e))
* **sendMessage:** mock para envio de mensagem waba provedor meta ([9d99d6f](https://gitlab.ikatec.cloud/digisac/digisac/commit/9d99d6f887ca519a1431d995c3ab48d24aea2a35))





# [3.46.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.46.0-rc.19...v3.46.0) (2025-06-10)

**Note:** Version bump only for package @digisac/root





## [3.45.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.1-mr-3298.2...v3.45.2) (2025-06-09)

**Note:** Version bump only for package @digisac/root





## [3.45.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-mr-3281.4...v3.45.1) (2025-06-09)

**Note:** Version bump only for package @digisac/root





# [3.45.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-rc.12...v3.45.0) (2025-06-02)

**Note:** Version bump only for package @digisac/root





# [3.44.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-rc.12...v3.44.0) (2025-06-02)

**Note:** Version bump only for package @digisac/root





## [3.43.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.3-mr-3251.1...v3.43.4) (2025-06-02)

**Note:** Version bump only for package @digisac/root





## [3.43.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.2...v3.43.3) (2025-05-30)

**Note:** Version bump only for package @digisac/root





## [3.43.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.1-mr-3166.5...v3.43.2) (2025-05-27)

**Note:** Version bump only for package @digisac/root





## [3.43.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.1-mr-3226.1...v3.43.1) (2025-05-26)

**Note:** Version bump only for package @digisac/root





# [3.43.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-rc.14...v3.43.0) (2025-05-21)

**Note:** Version bump only for package @digisac/root





## [3.42.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.1-mr-3158.7...v3.42.2) (2025-05-21)

**Note:** Version bump only for package @digisac/root





## [3.42.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.1-mr-3186.0...v3.42.1) (2025-05-13)

**Note:** Version bump only for package @digisac/root





# [3.42.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.2...v3.42.0) (2025-05-12)



# [3.42.0-rc.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3175.0...v3.42.0-rc.12) (2025-05-08)



# [3.42.0-mr-3175.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.10...v3.42.0-mr-3175.0) (2025-05-08)


### Bug Fixes

* consertando problema de dois cliques na notificacao para fechar ([bf42da0](https://gitlab.ikatec.cloud/digisac/digisac/commit/bf42da0f254358245bd3371ae1eebb016afdc4b8))



# [3.42.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3163.2...v3.42.0-rc.10) (2025-05-06)



# [3.42.0-mr-3163.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.9...v3.42.0-mr-3163.2) (2025-05-06)



# [3.42.0-rc.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3124.0...v3.42.0-rc.9) (2025-05-06)



# [3.42.0-mr-3124.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.8...v3.42.0-mr-3124.0) (2025-05-06)



# [3.42.0-rc.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.7...v3.42.0-rc.8) (2025-05-06)



# [3.42.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3164.5...v3.42.0-rc.7) (2025-05-06)



# [3.42.0-mr-3164.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3164.4...v3.42.0-mr-3164.5) (2025-05-06)



# [3.42.0-mr-3164.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.6...v3.42.0-mr-3164.4) (2025-05-06)



# [3.42.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.1...v3.42.0-rc.6) (2025-05-06)



# [3.42.0-mr-3164.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3164.2...v3.42.0-mr-3164.3) (2025-05-06)



# [3.42.0-mr-3164.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3164.1...v3.42.0-mr-3164.2) (2025-05-06)



# [3.42.0-mr-3164.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3164.0...v3.42.0-mr-3164.1) (2025-05-06)



# [3.42.0-mr-3164.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3124.4...v3.42.0-mr-3164.0) (2025-05-06)


### Bug Fixes

* **gitlab-ci-build-mr-version:** get the start if LAST_VERSION to find the LAST_MR_TAG ([e3310ba](https://gitlab.ikatec.cloud/digisac/digisac/commit/e3310ba3df0a930ad9166367399eae6c5911f79a))



# [3.41.0-mr-3124.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3163.1...v3.41.0-mr-3124.4) (2025-05-06)



# [3.42.0-mr-3163.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3160.2...v3.42.0-mr-3163.1) (2025-05-06)



# [3.42.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3160.1...v3.42.0-rc.5) (2025-05-06)



# [3.41.0-mr-3124.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3153.0...v3.41.0-mr-3124.3) (2025-05-06)


### Features

* mantendo botão fixo caso smartSummary ativo ([804811c](https://gitlab.ikatec.cloud/digisac/digisac/commit/804811cc697c356a66faf30a3064b143b9a44003))



# [3.41.0-mr-3153.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.23...v3.41.0-mr-3153.0) (2025-05-06)



# [3.41.0-rc.23](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3122.1...v3.41.0-rc.23) (2025-05-06)



# [3.41.0-mr-3122.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3160.0...v3.41.0-mr-3122.1) (2025-05-06)



# [3.42.0-mr-3163.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.4...v3.42.0-mr-3163.0) (2025-05-06)


### Bug Fixes

* adiciona o menu de integrações ao menu vertical, e adciona outros itens ao menu mais opções ([c8cb6d0](https://gitlab.ikatec.cloud/digisac/digisac/commit/c8cb6d0ed878c7e381b0a061af2efb5f42e35971))



# [3.42.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0...v3.42.0-rc.4) (2025-05-05)



# [3.42.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.22...v3.42.0-rc.3) (2025-05-05)



# [3.41.0-rc.22](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3127.1...v3.41.0-rc.22) (2025-05-05)



# [3.41.0-mr-3127.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3127.0...v3.41.0-mr-3127.1) (2025-05-05)



# [3.41.0-mr-3127.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3085.0...v3.41.0-mr-3127.0) (2025-05-05)



# [3.41.0-mr-3085.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.21...v3.41.0-mr-3085.0) (2025-05-05)



# [3.41.0-rc.21](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3128.3...v3.41.0-rc.21) (2025-05-05)



# [3.41.0-mr-3128.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.6...v3.41.0-mr-3128.3) (2025-05-05)



# [3.41.0-mr-2919.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.5...v3.41.0-mr-2919.6) (2025-05-05)



# [3.41.0-mr-2919.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.4...v3.41.0-mr-2919.5) (2025-05-05)



# [3.41.0-mr-2919.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.2...v3.41.0-mr-2919.4) (2025-05-05)



# [3.42.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3124.2...v3.42.0-rc.2) (2025-05-05)



# [3.41.0-mr-3124.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.20...v3.41.0-mr-3124.2) (2025-05-05)


### Bug Fixes

* adicionando contador de itens novos ao mais opções ([b5b1756](https://gitlab.ikatec.cloud/digisac/digisac/commit/b5b175670fcda4cb3782a6c40b2edd9d279f1f72))
* typo ([f65a4df](https://gitlab.ikatec.cloud/digisac/digisac/commit/f65a4df31f7ae2d7a70804e5463e6e6c22665aad))



# [3.41.0-rc.20](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.3...v3.41.0-rc.20) (2025-05-05)



# [3.41.0-mr-2919.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3137.1...v3.41.0-mr-2919.3) (2025-05-05)



# [3.41.0-mr-3137.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.2...v3.41.0-mr-3137.1) (2025-05-02)



# [3.41.0-mr-2919.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.1...v3.41.0-mr-2919.2) (2025-05-02)



# [3.41.0-mr-2919.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.5-mr-3085.2...v3.41.0-mr-2919.1) (2025-05-02)



## [3.42.5-mr-3085.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3128.2...v3.42.5-mr-3085.2) (2025-05-02)



# [3.41.0-mr-3128.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.2-mr-3153.0...v3.41.0-mr-3128.2) (2025-05-02)



## [3.40.2-mr-3153.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.1-rc.0...v3.40.2-mr-3153.0) (2025-04-30)


### Bug Fixes

* SD-1470 and SD-1475 - restore focus to chat textbox when replying to a message or sending quick reply ([9727bb6](https://gitlab.ikatec.cloud/digisac/digisac/commit/9727bb6aebae0fa7245ee918b42212f5980fadfa))



## [3.42.1-rc.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.1-mr-3125.1...v3.42.1-rc.0) (2025-04-30)



## [3.42.1-mr-3125.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.1...v3.42.1-mr-3125.1) (2025-04-30)



# [3.42.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.19...v3.42.0-rc.1) (2025-04-30)



## [3.37.5-mr-3085.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3090.3...v3.37.5-mr-3085.1) (2025-04-29)


### Bug Fixes

* pontos mr ([17ebb30](https://gitlab.ikatec.cloud/digisac/digisac/commit/17ebb301118c394a19043c4e3ae203c0d3a07424))
* pontos mr ([eac9702](https://gitlab.ikatec.cloud/digisac/digisac/commit/eac9702b163a3fd1ad6284fcf7afdbb9f60d8812))
* SD-1415 - filter out archived departments ([b3eaf13](https://gitlab.ikatec.cloud/digisac/digisac/commit/b3eaf13a7716221cb50a1fac5773231be2bb83ae))



# [3.41.0-mr-3090.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.17...v3.41.0-mr-3090.3) (2025-04-29)



# [3.41.0-mr-3090.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.39.1-mr-3104.2...v3.41.0-mr-3090.2) (2025-04-29)



# [3.41.0-mr-3137.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.15...v3.41.0-mr-3137.0) (2025-04-29)



## [3.40.1-mr-3125.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3128.1...v3.40.1-mr-3125.0) (2025-04-28)



# [3.41.0-mr-3128.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.1-mr-3137.0...v3.41.0-mr-3128.1) (2025-04-28)



## [3.36.1-mr-3137.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.0...v3.36.1-mr-3137.0) (2025-04-28)



## [3.39.2-mr-3125.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.11...v3.39.2-mr-3125.0) (2025-04-28)



# [3.41.0-mr-3122.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.9...v3.41.0-mr-3122.0) (2025-04-25)



# [3.41.0-mr-3090.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.0-mr-3131.1...v3.41.0-mr-3090.1) (2025-04-25)



# [3.41.0-mr-3090.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3130.0...v3.41.0-mr-3090.0) (2025-04-25)



# [3.41.0-mr-2919.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3051.0...v3.41.0-mr-2919.0) (2025-04-25)



# [3.41.0-mr-3128.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.39.2-mr-3121.3...v3.41.0-mr-3128.0) (2025-04-25)



# [3.41.0-mr-3124.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.1-mr-3109.2...v3.41.0-mr-3124.0) (2025-04-25)



## [3.36.1-mr-3109.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.0-mr-3124.1...v3.36.1-mr-3109.2) (2025-04-25)



# [3.40.0-mr-3124.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3118.3...v3.40.0-mr-3124.1) (2025-04-25)


### Features

* finalizando gatilhos ([de2659f](https://gitlab.ikatec.cloud/digisac/digisac/commit/de2659f314c236b55d50f0055c80664833fe0b02))
* finalizando gatilhos ([b86884e](https://gitlab.ikatec.cloud/digisac/digisac/commit/b86884e40b2b88a91e6c8b3f28f0a78111415265))



## [3.39.2-mr-3127.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.7...v3.39.2-mr-3127.0) (2025-04-25)


### Bug Fixes

* add round style in select ([d6ae191](https://gitlab.ikatec.cloud/digisac/digisac/commit/d6ae191926212c15a03d9bd026d7295d4f9f06ab))


### Features

* add new translates ([75e7a74](https://gitlab.ikatec.cloud/digisac/digisac/commit/75e7a7465c45a3743ee2e2f861997203b6cab81d))



# [3.40.0-mr-3124.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3123.0...v3.40.0-mr-3124.0) (2025-04-24)


### Features

* criacao da notificacao e inicio do chat ([1f50173](https://gitlab.ikatec.cloud/digisac/digisac/commit/1f501738763570022c19bf4fc2e9304d78e16f82))
* **cypress:** remove o package cypress do projeto ([4dc7dcb](https://gitlab.ikatec.cloud/digisac/digisac/commit/4dc7dcb4206343e4e79a9cd747d6aca0ac3b9fe0))
* **eslint-config-project:** remove o package eslint-config-project do projeto ([abe52fe](https://gitlab.ikatec.cloud/digisac/digisac/commit/abe52fe7a8d46cff2f3e1ab73b573cc5c09b5588))
* finalizando notification ([be15c18](https://gitlab.ikatec.cloud/digisac/digisac/commit/be15c180615d0cd47447ef07d559b5d976ab8626))
* **k6:** remove o package k6 do projeto ([86abe85](https://gitlab.ikatec.cloud/digisac/digisac/commit/86abe85ff899165b287bd06dcc2c708cdad2e6fd))



## [3.36.1-mr-3090.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.39.0-mr-3100.1...v3.36.1-mr-3090.4) (2025-04-16)


### Features

* criando branch ([25bff23](https://gitlab.ikatec.cloud/digisac/digisac/commit/25bff2376496be767efacae4e8f992a5a7788d7d))



## [3.37.5-mr-3085.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.5-mr-3083.0...v3.37.5-mr-3085.0) (2025-04-14)



## [3.36.1-mr-2919.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.4-mr-3078.0...v3.36.1-mr-2919.1) (2025-04-11)



## [3.36.1-mr-2919.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.0-rc.7...v3.36.1-mr-2919.0) (2025-04-02)





## [3.41.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.1-mr-2602.11...v3.41.2) (2025-05-08)

**Note:** Version bump only for package @digisac/root





## [3.41.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3160.2...v3.41.1) (2025-05-06)

**Note:** Version bump only for package @digisac/root





# [3.41.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.19...v3.41.0) (2025-05-05)

**Note:** Version bump only for package @digisac/root





## [3.40.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.1-mr-3147.1...v3.40.1) (2025-04-30)

**Note:** Version bump only for package @digisac/root





# [3.40.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.0-rc.9...v3.40.0) (2025-04-28)

**Note:** Version bump only for package @digisac/root





## [3.39.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.39.1-mr-3093.1...v3.39.2) (2025-04-28)

**Note:** Version bump only for package @digisac/root





## [3.39.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.39.0-rc.19...v3.39.1) (2025-04-22)

**Note:** Version bump only for package @digisac/root





## [3.38.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.38.1-mr-3113.0...v3.38.1) (2025-04-22)

**Note:** Version bump only for package @digisac/root





# [3.38.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.38.0-rc.5...v3.38.0) (2025-04-14)

**Note:** Version bump only for package @digisac/root





## [3.37.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.4-mr-3080.0...v3.37.4) (2025-04-11)

**Note:** Version bump only for package @digisac/root





## [3.37.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.2-mr-3066.1...v3.37.3) (2025-04-10)

**Note:** Version bump only for package @digisac/root





## [3.37.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.6...v3.37.2) (2025-04-09)

**Note:** Version bump only for package @digisac/root





## [3.36.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.3-mr-3048.6...v3.36.6) (2025-04-08)

**Note:** Version bump only for package @digisac/root





## [3.37.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.0-rc.14...v3.37.1) (2025-04-07)

**Note:** Version bump only for package @digisac/root





## [3.36.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.5-mr-3059.0...v3.36.5) (2025-04-07)

**Note:** Version bump only for package @digisac/root





## [3.36.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.3-mr-3041.2...v3.36.4) (2025-04-03)

**Note:** Version bump only for package @digisac/root





## [3.36.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.3-mr-3047.1...v3.36.3) (2025-04-02)

**Note:** Version bump only for package @digisac/root





## [3.36.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.1-mr-3016.3...v3.36.2) (2025-03-27)

**Note:** Version bump only for package @digisac/root





## [3.36.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.0-mr-3038.1...v3.36.1) (2025-03-26)

**Note:** Version bump only for package @digisac/root





# [3.36.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.0-rc.8...v3.36.0) (2025-03-25)

**Note:** Version bump only for package @digisac/root





## [3.35.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.35.1-mr-3010.1...v3.35.1) (2025-03-21)

**Note:** Version bump only for package @digisac/root





# [3.35.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.35.0-rc.12...v3.35.0) (2025-03-17)

**Note:** Version bump only for package @digisac/root





## [3.34.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.34.3-mr-2986.1...v3.34.3) (2025-03-12)

**Note:** Version bump only for package @digisac/root





## [3.34.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.34.2-mr-2969.1...v3.34.2) (2025-03-07)

**Note:** Version bump only for package @digisac/root





## [3.34.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.34.1-mr-2968.0...v3.34.1) (2025-03-06)

**Note:** Version bump only for package @digisac/root





# [3.34.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.34.0-rc.9...v3.34.0) (2025-03-05)

**Note:** Version bump only for package @digisac/root





# [3.33.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.3...v3.33.0) (2025-02-24)



# [3.33.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.2...v3.33.0-rc.10) (2025-02-21)



# [3.33.0-rc.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.1...v3.33.0-rc.9) (2025-02-21)



# [3.33.0-rc.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2930.0...v3.33.0-rc.8) (2025-02-20)



# [3.33.0-mr-2930.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-rc.7...v3.33.0-mr-2930.0) (2025-02-20)



# [3.33.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2920.0...v3.33.0-rc.7) (2025-02-20)



# [3.33.0-mr-2920.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-rc.6...v3.33.0-mr-2920.0) (2025-02-20)



# [3.33.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.3...v3.33.0-rc.6) (2025-02-20)



# [3.33.0-mr-2719.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.2...v3.33.0-mr-2719.3) (2025-02-20)


### Bug Fixes

* remove character limitation from general validation of short text custom fields ([0d0480b](https://gitlab.ikatec.cloud/digisac/digisac/commit/0d0480be9e0dc0c6b4ae059ff5f849f76995b3cc))



# [3.33.0-mr-2719.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.1...v3.33.0-mr-2719.2) (2025-02-19)



# [3.33.0-mr-2719.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-rc.5...v3.33.0-mr-2719.1) (2025-02-19)



# [3.33.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.0...v3.33.0-rc.5) (2025-02-19)



# [3.33.0-mr-2719.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2746.13...v3.33.0-mr-2719.0) (2025-02-19)



# [3.33.0-mr-2746.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2841.2...v3.33.0-mr-2746.13) (2025-02-19)



# [3.33.0-mr-2841.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-rc.4...v3.33.0-mr-2841.2) (2025-02-19)



# [3.33.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.1-mr-2927.0...v3.33.0-rc.4) (2025-02-19)



# [3.33.0-mr-2746.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2746.11...v3.33.0-mr-2746.12) (2025-02-18)



# [3.33.0-mr-2746.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.4...v3.33.0-mr-2746.11) (2025-02-18)



# [3.33.0-mr-2719.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-mr-2920.1...v3.33.0-mr-2719.4) (2025-02-18)



# [3.32.0-mr-2920.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0...v3.32.0-mr-2920.1) (2025-02-18)



# [3.33.0-mr-2841.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-mr-2923.2...v3.33.0-mr-2841.1) (2025-02-18)



# [3.32.0-mr-2920.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-rc.3...v3.32.0-mr-2920.0) (2025-02-17)



# [3.33.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2912.0...v3.33.0-rc.3) (2025-02-17)



# [3.33.0-mr-2912.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-rc.2...v3.33.0-mr-2912.0) (2025-02-17)



# [3.33.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2900.0...v3.33.0-rc.2) (2025-02-17)



# [3.33.0-mr-2900.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-mr-2915.1...v3.33.0-mr-2900.0) (2025-02-17)



# [3.33.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-mr-2915.0...v3.33.0-rc.1) (2025-02-14)



# [3.32.0-mr-2912.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-mr-2912.0...v3.32.0-mr-2912.1) (2025-02-14)



# [3.32.0-mr-2912.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.18...v3.32.0-mr-2912.0) (2025-02-14)



## [3.31.1-dev.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2878.3...v3.31.1-dev.1) (2025-02-14)



## [3.31.1-mr-2878.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.16...v3.31.1-mr-2878.3) (2025-02-14)



## [3.31.1-mr-2878.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.15...v3.31.1-mr-2878.2) (2025-02-13)



# [3.31.0-mr-2892.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2908.0...v3.31.0-mr-2892.12) (2025-02-13)



# [3.31.0-mr-2892.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2892.10...v3.31.0-mr-2892.11) (2025-02-13)


### Features

* block right click on documents ([a5cc286](https://gitlab.ikatec.cloud/digisac/digisac/commit/a5cc286ed8e6a2a8014132f781078e1479877c4f))



# [3.31.0-mr-2892.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2892.9...v3.31.0-mr-2892.10) (2025-02-12)



# [3.31.0-mr-2892.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2892.8...v3.31.0-mr-2892.9) (2025-02-12)



# [3.31.0-mr-2892.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2906.3...v3.31.0-mr-2892.8) (2025-02-12)



# [3.31.0-mr-2906.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2906.2...v3.31.0-mr-2906.3) (2025-02-12)



# [3.31.0-mr-2906.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2906.1...v3.31.0-mr-2906.2) (2025-02-12)



# [3.31.0-mr-2906.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2906.0...v3.31.0-mr-2906.1) (2025-02-12)



# [3.31.0-mr-2906.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2892.7...v3.31.0-mr-2906.0) (2025-02-12)



# [3.31.0-mr-2892.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2892.6...v3.31.0-mr-2892.7) (2025-02-12)


### Bug Fixes

* corrige permissao para downloads de documents ([03944bd](https://gitlab.ikatec.cloud/digisac/digisac/commit/03944bdc6abbf12b27bfaf654e8db308913631cb))



# [3.31.0-mr-2892.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.13...v3.31.0-mr-2892.6) (2025-02-12)



# [3.31.0-mr-2892.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2885.4...v3.31.0-mr-2892.5) (2025-02-12)



# [3.31.0-mr-2892.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.12...v3.31.0-mr-2892.4) (2025-02-11)



# [3.31.0-mr-2892.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.10...v3.31.0-mr-2892.3) (2025-02-11)



# [3.31.0-mr-2841.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2874.13...v3.31.0-mr-2841.0) (2025-02-11)



# [3.31.0-mr-2874.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2874.12...v3.31.0-mr-2874.13) (2025-02-11)


### Bug Fixes

* aplica correcoes de cr ([10555b9](https://gitlab.ikatec.cloud/digisac/digisac/commit/10555b947649fc8522d68124f3d0be20e9f41d3b))



# [3.31.0-mr-2874.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2874.11...v3.31.0-mr-2874.12) (2025-02-11)


### Bug Fixes

* aplica correcoes de cr ([2eceef6](https://gitlab.ikatec.cloud/digisac/digisac/commit/2eceef6329ed93f6321e630a317018e95dfa1dcc))



# [3.31.0-mr-2874.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2885.3...v3.31.0-mr-2874.11) (2025-02-11)


### Bug Fixes

* aplica correcoes de cr ([e7b7881](https://gitlab.ikatec.cloud/digisac/digisac/commit/e7b78811df5f8c80f3c05bd0a860baea3a320d13))



# [3.31.0-mr-2874.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2892.2...v3.31.0-mr-2874.10) (2025-02-10)


### Bug Fixes

* corrige bug de unidefined ([71d0a72](https://gitlab.ikatec.cloud/digisac/digisac/commit/71d0a72bf8876721d0da9bd7a57aa3eb86b6da99))



# [3.31.0-mr-2892.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.9...v3.31.0-mr-2892.2) (2025-02-10)


### Bug Fixes

* corrige bug de unidefined ([f64548f](https://gitlab.ikatec.cloud/digisac/digisac/commit/f64548f7bb18255b3e6efd537ae30dece09ce1c3))



# [3.31.0-mr-2892.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2892.0...v3.31.0-mr-2892.1) (2025-02-10)



# [3.31.0-mr-2892.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2900.0...v3.31.0-mr-2892.0) (2025-02-10)



## [3.31.1-mr-2900.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2874.9...v3.31.1-mr-2900.0) (2025-02-10)



# [3.31.0-mr-2874.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2841.1...v3.31.0-mr-2874.9) (2025-02-10)



# [3.31.0-mr-2841.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.8...v3.31.0-mr-2841.1) (2025-02-10)



## [3.30.1-mr-2874.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.7...v3.30.1-mr-2874.8) (2025-02-07)


### Bug Fixes

* atrela controlsList para variavel ([47da826](https://gitlab.ikatec.cloud/digisac/digisac/commit/47da82635354d875c9427d3d5e78bd7b933dcbfe))



## [3.30.1-mr-2892.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1-mr-2874.7...v3.30.1-mr-2892.0) (2025-02-06)



## [3.30.1-mr-2874.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1-mr-2874.6...v3.30.1-mr-2874.7) (2025-02-06)


### Bug Fixes

* corrige erros de sitaxe encontrados ([a14dd41](https://gitlab.ikatec.cloud/digisac/digisac/commit/a14dd41fc295f6db20b5b7e9c38e84040718ff62))



## [3.30.1-mr-2874.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2839.3...v3.30.1-mr-2874.6) (2025-02-05)


### Bug Fixes

* ajusta bloquei dos downloads dentro do componet de reproducao ([f336a02](https://gitlab.ikatec.cloud/digisac/digisac/commit/f336a024701c9c9adfb28958053f420164f02c65))



# [3.26.0-mr-2746.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2877.5...v3.26.0-mr-2746.10) (2025-02-05)



## [3.31.1-mr-2878.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1-mr-2874.5...v3.31.1-mr-2878.1) (2025-02-05)


### Features

* altearando o openai como IA default do digisac ([45b4f8b](https://gitlab.ikatec.cloud/digisac/digisac/commit/45b4f8bdd15151e5521f592005e4e38e90e54296))



## [3.30.1-mr-2874.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2885.0...v3.30.1-mr-2874.5) (2025-02-05)


### Bug Fixes

* pega mimetype do component e reaproveita ele no bloqueio de documentos ([a524139](https://gitlab.ikatec.cloud/digisac/digisac/commit/a524139eea94b270e1255c444cce82f832a2739e))



## [3.30.1-mr-2874.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2858.2...v3.30.1-mr-2874.4) (2025-02-04)



## [3.31.1-mr-2878.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2877.0...v3.31.1-mr-2878.0) (2025-02-04)


### Features

* altearando o openai como IA default do digisac ([18fb7a3](https://gitlab.ikatec.cloud/digisac/digisac/commit/18fb7a357579d3ff09cae27590baff0d98fafe73))



## [3.30.1-mr-2841.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-mr-2858.0...v3.30.1-mr-2841.0) (2025-01-29)



## [3.30.1-mr-2840.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.1-mr-2854.3...v3.30.1-mr-2840.3) (2025-01-28)


### Bug Fixes

* corrige importacao ([8795ca7](https://gitlab.ikatec.cloud/digisac/digisac/commit/8795ca7e9a1e43966bed3a375e5e832e8b84f487))



## [3.30.1-mr-2840.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2855.0...v3.30.1-mr-2840.2) (2025-01-28)


### Bug Fixes

* altera image icon, e modifica icone para lucide ([57ef162](https://gitlab.ikatec.cloud/digisac/digisac/commit/57ef1620df867b209799cf6c54ac6b836a2a44d1))
* altera migration para executar novas permissoes em cargos no-admin ([df9a5cf](https://gitlab.ikatec.cloud/digisac/digisac/commit/df9a5cf638f91b74a514d8567d7912e76b6b7856))



## [3.30.1-mr-2840.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1...v3.30.1-mr-2840.1) (2025-01-27)


### Features

* criacao da permissao de download no front ([dbce815](https://gitlab.ikatec.cloud/digisac/digisac/commit/dbce8157a42273e75ad1328ed10baf9942bd588f))



## [3.30.1-mr-2840.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2814.0...v3.30.1-mr-2840.0) (2025-01-23)


### Features

* create migration run-seed-permissions ([fc043a7](https://gitlab.ikatec.cloud/digisac/digisac/commit/fc043a7e8c33e6293290dc160fb361cb22ad5fd2))



# [3.26.0-mr-2746.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.0-mr-2798.0...v3.26.0-mr-2746.9) (2025-01-10)



# [3.29.0-mr-2719.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.28.1-dev.0...v3.29.0-mr-2719.3) (2025-01-08)



# [3.29.0-mr-2719.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.1-dev.2...v3.29.0-mr-2719.2) (2025-01-07)



# [3.27.0-mr-2719.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.1-mr-2732.0...v3.27.0-mr-2719.1) (2025-01-06)



# [3.26.0-mr-2759.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2719.0...v3.26.0-mr-2759.5) (2025-01-02)



# [3.25.0-mr-2719.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2761.3...v3.25.0-mr-2719.0) (2024-12-31)



# [3.25.0-mr-2761.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-mr-2759.4...v3.25.0-mr-2761.3) (2024-12-31)



# [3.26.0-mr-2759.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-mr-2696.12...v3.26.0-mr-2759.4) (2024-12-31)



# [3.26.0-mr-2696.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-mr-2719.0...v3.26.0-mr-2696.12) (2024-12-31)


### Features

* temporarily removes custom field validations ([96ddcbf](https://gitlab.ikatec.cloud/digisac/digisac/commit/96ddcbf4db62b083fd0fa609693a7ac32cd379d3))



# [3.26.0-mr-2719.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-rc.11...v3.26.0-mr-2719.0) (2024-12-31)



# [3.26.0-mr-2755.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2761.2...v3.26.0-mr-2755.3) (2024-12-30)



# [3.25.0-mr-2761.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2759.3...v3.25.0-mr-2761.2) (2024-12-30)



# [3.25.0-mr-2759.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2761.1...v3.25.0-mr-2759.3) (2024-12-30)



# [3.25.0-mr-2761.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2761.0...v3.25.0-mr-2761.1) (2024-12-30)



# [3.25.0-mr-2761.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2759.2...v3.25.0-mr-2761.0) (2024-12-30)



# [3.25.0-mr-2759.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2759.1...v3.25.0-mr-2759.2) (2024-12-30)


### Bug Fixes

* adjust numerical custom fields limits validation ([7594e66](https://gitlab.ikatec.cloud/digisac/digisac/commit/7594e66f73f1b294cfae885e757c18ad49ae588d))


### Features

* adjusts to allow minimum or maximum limits on monetary and numerical custom fields ([137dd7e](https://gitlab.ikatec.cloud/digisac/digisac/commit/137dd7e2f5cd8e01dd847cb0b21b542ccb1e0e45))



# [3.25.0-mr-2759.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2755.3...v3.25.0-mr-2759.1) (2024-12-30)



# [3.25.0-mr-2755.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2755.2...v3.25.0-mr-2755.3) (2024-12-27)



# [3.25.0-mr-2755.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2759.0...v3.25.0-mr-2755.2) (2024-12-27)


### Features

* add identifier code change behavior for new custom fields ([2fe03a2](https://gitlab.ikatec.cloud/digisac/digisac/commit/2fe03a293fdc1c9a4a4cb4699ea00541dc1adbea))



# [3.25.0-mr-2759.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2757.0...v3.25.0-mr-2759.0) (2024-12-27)



# [3.25.0-mr-2757.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2755.1...v3.25.0-mr-2757.0) (2024-12-27)



# [3.25.0-mr-2755.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.1-mr-2756.0...v3.25.0-mr-2755.1) (2024-12-27)



# [3.26.0-mr-2696.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-rc.9...v3.26.0-mr-2696.11) (2024-12-24)



# [3.26.0-mr-2696.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-mr-2696.9...v3.26.0-mr-2696.10) (2024-12-23)


### Bug Fixes

* Correção de importações indevidas ([b3ff569](https://gitlab.ikatec.cloud/digisac/digisac/commit/b3ff5692d3ea8a94f2c6a18312b235e6faba43f2))



# [3.26.0-mr-2696.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-mr-2696.8...v3.26.0-mr-2696.9) (2024-12-23)



# [3.26.0-mr-2696.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0...v3.26.0-mr-2696.8) (2024-12-23)



# [3.26.0-mr-2719.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.2-dev.1...v3.26.0-mr-2719.2) (2024-12-20)



# [3.25.0-mr-2719.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.24.1-dev.2...v3.25.0-mr-2719.1) (2024-12-13)



## [3.24.1-dev.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-rc.9...v3.24.1-dev.2) (2024-12-13)





## [3.32.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.3-mr-2939.0...v3.32.3) (2025-02-24)

**Note:** Version bump only for package @digisac/root





## [3.32.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.2-mr-2934.0...v3.32.2) (2025-02-21)

**Note:** Version bump only for package @digisac/root





## [3.32.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.1-mr-2927.1...v3.32.1) (2025-02-21)

**Note:** Version bump only for package @digisac/root





# [3.32.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.22...v3.32.0) (2025-02-18)

**Note:** Version bump only for package @digisac/root





## [3.31.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2908.2...v3.31.2) (2025-02-18)

**Note:** Version bump only for package @digisac/root





## [3.31.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2885.4...v3.31.1) (2025-02-13)

**Note:** Version bump only for package @digisac/root





# [3.31.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-rc.18...v3.31.0) (2025-01-29)

**Note:** Version bump only for package @digisac/root





## [3.30.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1-mr-2854.6...v3.30.2) (2025-01-29)

**Note:** Version bump only for package @digisac/root





## [3.30.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1-mr-2844.3...v3.30.1) (2025-01-27)

**Note:** Version bump only for package @digisac/root





# [3.30.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.0-rc.11...v3.30.0) (2025-01-21)

**Note:** Version bump only for package @digisac/root





## [3.29.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.2-mr-2811.2...v3.29.3) (2025-01-20)

**Note:** Version bump only for package @digisac/root





## [3.29.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.1-mr-2817.1...v3.29.2) (2025-01-17)

**Note:** Version bump only for package @digisac/root





## [3.29.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.1-mr-2816.1...v3.29.1) (2025-01-17)

**Note:** Version bump only for package @digisac/root





# [3.29.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.0-rc.16...v3.29.0) (2025-01-14)

**Note:** Version bump only for package @digisac/root





## [3.28.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.28.1-mr-2806.2...v3.28.2) (2025-01-13)

**Note:** Version bump only for package @digisac/root





## [3.28.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.28.0...v3.28.1) (2025-01-10)


### Bug Fixes

* **whatsappBusiness:** add nullable check ([0919907](https://gitlab.ikatec.cloud/digisac/digisac/commit/09199077e9948e0aa15ec46d7c196c3330fa428a))





# [3.28.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.28.0-rc.4...v3.28.0) (2025-01-07)


### Features

* force build version 3.28.0 ([f4ec577](https://gitlab.ikatec.cloud/digisac/digisac/commit/f4ec5777c7c13e664ae70357fedca3ad0f1da426))





# [3.27.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-rc.10...v3.27.0) (2025-01-02)



# [3.26.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-mr-2731.3...v3.26.0) (2024-12-23)

**Note:** Version bump only for package @digisac/root





# [3.26.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-rc.7...v3.26.0) (2024-12-23)



## [3.25.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-rc.6...v3.25.2) (2024-12-20)

**Note:** Version bump only for package @digisac/root





## [3.25.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.2-mr-2735.1...v3.25.2) (2024-12-20)

**Note:** Version bump only for package @digisac/root





## [3.25.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2642.4...v3.25.1) (2024-12-16)

**Note:** Version bump only for package @digisac/root





# [3.25.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-rc.9...v3.25.0) (2024-12-16)

**Note:** Version bump only for package @digisac/root





# [3.24.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.24.0-rc.11...v3.24.0) (2024-12-09)

**Note:** Version bump only for package @digisac/root





# [3.23.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.23.0-rc.13...v3.23.0) (2024-12-02)

**Note:** Version bump only for package @digisac/root





## [3.22.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.22.1-mr-2668.0...v3.22.1) (2024-12-02)

**Note:** Version bump only for package @digisac/root





# [3.22.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.22.0-rc.2...v3.22.0) (2024-11-25)

**Note:** Version bump only for package @digisac/root





## [3.20.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.20.1-mr-2592.4...v3.20.1) (2024-11-22)

**Note:** Version bump only for package @digisac/root





## [3.21.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.2-mr-2593.2...v3.21.3) (2024-11-18)

**Note:** Version bump only for package @digisac/root





## [3.21.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.1...v3.21.2) (2024-11-13)



## [3.21.1-mr-2609.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.1-mr-2609.0...v3.21.1-mr-2609.1) (2024-11-13)



## [3.21.1-mr-2609.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.0...v3.21.1-mr-2609.0) (2024-11-13)

**Note:** Version bump only for package @digisac/root





## [3.21.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.1-mr-2610.0...v3.21.1) (2024-11-13)

**Note:** Version bump only for package @digisac/root





# [3.21.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.0-rc.3...v3.21.0) (2024-11-12)

**Note:** Version bump only for package @digisac/root





# [3.20.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.20.0-rc.9...v3.20.0) (2024-11-05)

**Note:** Version bump only for package @digisac/root





## [3.19.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.18.3...v3.19.3) (2024-10-31)

**Note:** Version bump only for package @digisac/root





## [3.18.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.18.3-mr-2577.2...v3.18.3) (2024-10-31)

**Note:** Version bump only for package @digisac/root





## [3.18.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.19.0...v3.18.2) (2024-10-31)

**Note:** Version bump only for package @digisac/root





# [3.19.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.19.0-rc.8...v3.19.0) (2024-10-23)

**Note:** Version bump only for package @digisac/root





## [3.18.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.18.1-mr-2515.0...v3.18.1) (2024-10-17)

**Note:** Version bump only for package @digisac/root





# [3.18.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.18.0-rc.19...v3.18.0) (2024-10-14)

**Note:** Version bump only for package @digisac/root





## [3.17.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.5-mr-2505.0...v3.17.5) (2024-10-10)

**Note:** Version bump only for package @digisac/root





## [3.17.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.4-mr-2474.0...v3.17.4) (2024-10-02)

**Note:** Version bump only for package @digisac/root





## [3.17.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.3-mr-2464.1...v3.17.3) (2024-09-26)

**Note:** Version bump only for package @digisac/root





## [3.17.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.1...v3.17.2) (2024-09-25)


### Bug Fixes

* **createAccount:** corrige utilização da feature-flag enableSearchMessages ([8423b42](https://gitlab.ikatec.cloud/digisac/digisac/commit/8423b4204d0387600393449227145f8232ef242f))





## [3.17.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.1-mr-2463.0...v3.17.1) (2024-09-25)

**Note:** Version bump only for package @digisac/root





# [3.17.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.0-rc.8...v3.17.0) (2024-09-24)

**Note:** Version bump only for package @digisac/root





## [3.16.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.11-mr-2449.1...v3.16.12) (2024-09-24)

**Note:** Version bump only for package @digisac/root





## [3.16.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.7-mr-2442.2...v3.16.11) (2024-09-24)

**Note:** Version bump only for package @digisac/root





## [3.16.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.10-mr-2435.1...v3.16.10) (2024-09-20)

**Note:** Version bump only for package @digisac/root





## [3.16.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.9-mr-2441.7...v3.16.9) (2024-09-18)

**Note:** Version bump only for package @digisac/root





## [3.16.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.7...v3.16.8) (2024-09-18)

**Note:** Version bump only for package @digisac/root





## [3.16.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.7-mr-2443.0...v3.16.7) (2024-09-18)

**Note:** Version bump only for package @digisac/root





## [3.16.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.6-mr-2425.2...v3.16.6) (2024-09-13)

**Note:** Version bump only for package @digisac/root





## [3.16.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.4-mr-2424.1...v3.16.5) (2024-09-13)

**Note:** Version bump only for package @digisac/root





## [3.16.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.4-mr-2423.0...v3.16.4) (2024-09-13)

**Note:** Version bump only for package @digisac/root





## [3.16.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.2...v3.16.3) (2024-09-11)


### Bug Fixes

* **account:** corrige bug em accountTransformer ([ce2311b](https://gitlab.ikatec.cloud/digisac/digisac/commit/ce2311bc4d241999c9ea938c7b5ed93ca1c26951))





## [3.16.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.2-mr-2407.1...v3.16.2) (2024-09-10)

**Note:** Version bump only for package @digisac/root





## [3.16.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.1-mr-2370.0...v3.16.1) (2024-09-09)

**Note:** Version bump only for package @digisac/root





# [3.16.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.0-rc.7...v3.16.0) (2024-09-05)

**Note:** Version bump only for package @digisac/root





## [3.15.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.3-mr-2400.1...v3.15.3) (2024-09-05)

**Note:** Version bump only for package @digisac/root





## [3.15.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.2-mr-2372.1...v3.15.2) (2024-08-29)

**Note:** Version bump only for package @digisac/root





## [3.15.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0...v3.15.1) (2024-08-21)

**Note:** Version bump only for package @digisac/root





# [3.15.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.1...v3.15.0) (2024-08-21)



# [3.15.0-rc.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.7...v3.15.0-rc.8) (2024-08-21)


### Bug Fixes

* **contacts:** corrige ortografico no nome da coluna archivedAt ([8a2dc9b](https://gitlab.ikatec.cloud/digisac/digisac/commit/8a2dc9b247fb4a168ee2b1bcdc5e497a57b8634b))



# [3.15.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.6...v3.15.0-rc.7) (2024-08-20)


### Bug Fixes

* corrige formatação da migration 20240726140451-update-duplicate-contacts ([d94d4a6](https://gitlab.ikatec.cloud/digisac/digisac/commit/d94d4a6db3ef3f95f8984727b53e3958df72fc2f))



# [3.15.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.5...v3.15.0-rc.6) (2024-08-20)


### Bug Fixes

* corrige problema na atualização do status de mensagens gupshup ([0fc204f](https://gitlab.ikatec.cloud/digisac/digisac/commit/0fc204fbab67a86f20d539ed0e22a33555b15116))



# [3.15.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.4...v3.15.0-rc.5) (2024-08-20)



# [3.15.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.7...v3.15.0-rc.4) (2024-08-20)



# [3.15.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-mr-2340.0...v3.15.0-rc.3) (2024-08-16)



# [3.15.0-mr-2340.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.2...v3.15.0-mr-2340.0) (2024-08-16)


### Bug Fixes

* alteração na execução do cron ([7e29349](https://gitlab.ikatec.cloud/digisac/digisac/commit/7e2934907277f2f17f219e25f5f88b4fe01179cb))



# [3.15.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-rc.0...v3.15.0-rc.2) (2024-08-15)



## [3.13.4-rc.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2331.2...v3.13.4-rc.0) (2024-08-15)



## [3.13.4-mr-2331.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.7-mr-2338.0...v3.13.4-mr-2331.2) (2024-08-15)



# [3.15.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.6...v3.15.0-rc.1) (2024-08-15)



# [3.14.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.5...v3.14.0-rc.6) (2024-08-15)



# [3.14.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.4...v3.14.0-rc.5) (2024-08-15)



# [3.14.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-mr-2327.1...v3.14.0-rc.4) (2024-08-14)



# [3.14.0-mr-2327.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.6...v3.14.0-mr-2327.1) (2024-08-14)



# [3.13.0-rc.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-mr-2306.6...v3.13.0-rc.13) (2024-08-14)



# [3.13.0-mr-2306.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.5-mr-2332.1...v3.13.0-mr-2306.6) (2024-08-14)



## [3.13.4-mr-2331.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2331.0...v3.13.4-mr-2331.1) (2024-08-13)



## [3.13.4-mr-2331.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2330.0...v3.13.4-mr-2331.0) (2024-08-13)


### Bug Fixes

* **profile:** melhora a checagem do atributo otpAuthActive na api ([1441d0c](https://gitlab.ikatec.cloud/digisac/digisac/commit/1441d0c639231d2d72e25d47498fe2960fbb634c))



# [3.14.0-mr-2327.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2325.0...v3.14.0-mr-2327.0) (2024-08-13)


### Features

* atualiza versão do whatsapp para 2.3000.1015602493 ([cc750c1](https://gitlab.ikatec.cloud/digisac/digisac/commit/cc750c1fb5831aacc05961a6d2274303d79d1b31))



# [3.14.0-dev.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-mr-2306.5...v3.14.0-dev.10) (2024-08-12)



# [3.13.0-mr-2306.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-mr-2306.4...v3.13.0-mr-2306.5) (2024-08-12)



# [3.13.0-mr-2306.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.3-mr-2323.3...v3.13.0-mr-2306.4) (2024-08-12)



# [3.13.0-mr-2306.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.2...v3.13.0-mr-2306.3) (2024-08-09)


### Bug Fixes

* adiciona required true na querie ([5d01350](https://gitlab.ikatec.cloud/digisac/digisac/commit/5d0135062c67bf7f677e45b456c36a23dc555eef))



# [3.14.0-mr-2312.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-mr-2306.2...v3.14.0-mr-2312.3) (2024-08-09)


### Bug Fixes

* alterado condição para exportar relatório ([ad98ce3](https://gitlab.ikatec.cloud/digisac/digisac/commit/ad98ce32ae2bb6e27da203a79dfb76b5a8785e90))



# [3.13.0-mr-2306.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.2-mr-2317.3...v3.13.0-mr-2306.2) (2024-08-09)


### Bug Fixes

* remove importacao desnecessaria ([647d32f](https://gitlab.ikatec.cloud/digisac/digisac/commit/647d32fed089535a4cad06e7c105bd389fe8a14a))
* remove importacao desnecessaria ([8558c95](https://gitlab.ikatec.cloud/digisac/digisac/commit/8558c95090fe786cd8a4605feed9f98437554cc4))



# [3.13.0-mr-2306.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-rc.12...v3.13.0-mr-2306.1) (2024-08-07)


### Bug Fixes

* sobe inclusao de usuarios que tenham o chat interno habilitado ([9c36cd3](https://gitlab.ikatec.cloud/digisac/digisac/commit/9c36cd3d33c855da630777f3f3f59d50e556bd43))



# [3.14.0-mr-2312.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-dev.9...v3.14.0-mr-2312.2) (2024-08-06)



# [3.14.0-dev.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.3-mr-2304.2...v3.14.0-dev.9) (2024-08-06)



# [3.14.0-mr-2307.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-mr-2312.1...v3.14.0-mr-2307.0) (2024-08-06)



# [3.14.0-mr-2312.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-mr-2312.0...v3.14.0-mr-2312.1) (2024-08-05)



# [3.14.0-mr-2312.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-mr-2307.5...v3.14.0-mr-2312.0) (2024-08-05)


### Bug Fixes

* validação de erro ([6666883](https://gitlab.ikatec.cloud/digisac/digisac/commit/6666883bd009327b2439716d7a822bf2c15deaf6))



# [3.14.0-mr-2307.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-dev.8...v3.14.0-mr-2307.5) (2024-08-03)



# [3.14.0-dev.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.3...v3.14.0-dev.8) (2024-08-03)



# [3.13.0-dev.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-dev.10...v3.13.0-dev.11) (2024-08-02)


### Reverts

* Revert "chore: atualiza yarn.lock para front e back" ([41ce8d2](https://gitlab.ikatec.cloud/digisac/digisac/commit/41ce8d2e1c6abacc5650279bc7613d77cf614bdd))



# [3.13.0-dev.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.3-mr-2307.4...v3.13.0-dev.10) (2024-08-02)



## [3.13.3-mr-2307.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-dev.9...v3.13.3-mr-2307.4) (2024-08-02)



# [3.13.0-dev.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-dev.8...v3.13.0-dev.9) (2024-08-02)



# [3.13.0-dev.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-dev.0...v3.13.0-dev.8) (2024-08-02)



# [3.13.0-dev.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.3-mr-2307.3...v3.13.0-dev.7) (2024-08-02)



## [3.13.3-mr-2307.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.3-mr-2304.1...v3.13.3-mr-2307.3) (2024-08-02)



## [3.12.3-mr-2307.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-mr-2306.0...v3.12.3-mr-2307.2) (2024-08-02)


### Bug Fixes

* corrige a lógica na redução de retentativas, quando está no stage de otp ([d2224e5](https://gitlab.ikatec.cloud/digisac/digisac/commit/d2224e58dde13a9ad7b04a318b9b32171f45909b))



# [3.13.0-mr-2306.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-dev.5...v3.13.0-mr-2306.0) (2024-08-02)


### Features

* cria job para alterar o token do chat interno e atualizar a url ([899f3eb](https://gitlab.ikatec.cloud/digisac/digisac/commit/899f3eb29c122b9eadf8aafc006730046831a507))



# [3.13.0-dev.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.3-mr-2304.0...v3.13.0-dev.5) (2024-07-31)





## [3.14.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0...v3.14.1) (2024-08-21)

**Note:** Version bump only for package @digisac/root





# [3.14.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.7...v3.14.0) (2024-08-21)

**Note:** Version bump only for package @digisac/root





## [3.13.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.7-mr-2338.0...v3.13.7) (2024-08-16)

**Note:** Version bump only for package @digisac/root





## [3.13.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.5-mr-2332.2...v3.13.6) (2024-08-14)

**Note:** Version bump only for package @digisac/root





## [3.13.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2325.1...v3.13.5) (2024-08-14)

**Note:** Version bump only for package @digisac/root





## [3.13.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2330.0...v3.13.4) (2024-08-13)

**Note:** Version bump only for package @digisac/root





## [3.13.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.3-mr-2323.3...v3.13.3) (2024-08-12)

**Note:** Version bump only for package @digisac/root





## [3.13.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.2-mr-2317.3...v3.13.2) (2024-08-09)

**Note:** Version bump only for package @digisac/root





## [3.13.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0...v3.13.1) (2024-08-07)

**Note:** Version bump only for package @digisac/root





# [3.13.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-rc.12...v3.13.0) (2024-08-07)

**Note:** Version bump only for package @digisac/root





## [3.12.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.3-mr-2304.4...v3.12.3) (2024-08-07)

**Note:** Version bump only for package @digisac/root





## [3.12.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.2-mr-2291.0...v3.12.2) (2024-07-26)

**Note:** Version bump only for package @digisac/root





## [3.12.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.1-mr-2290.1...v3.12.1) (2024-07-24)

**Note:** Version bump only for package @digisac/root





# [3.12.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.0-rc.10...v3.12.0) (2024-07-23)

**Note:** Version bump only for package @digisac/root





## [3.11.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.8-mr-2281.5...v3.11.9) (2024-07-18)

**Note:** Version bump only for package @digisac/root





## [3.11.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.7-mr-2272.3...v3.11.8) (2024-07-18)

**Note:** Version bump only for package @digisac/root





## [3.11.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.6-mr-2273.6...v3.11.7) (2024-07-18)

**Note:** Version bump only for package @digisac/root





## [3.11.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.5-mr-2278.1...v3.11.6) (2024-07-18)

**Note:** Version bump only for package @digisac/root





## [3.11.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.5-mr-2274.0...v3.11.5) (2024-07-17)

**Note:** Version bump only for package @digisac/root





## [3.11.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.3...v3.11.4) (2024-07-11)

**Note:** Version bump only for package @digisac/root





## [3.11.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.3-mr-2254.0...v3.11.3) (2024-07-10)

**Note:** Version bump only for package @digisac/root





## [3.11.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.2-mr-2245.0...v3.11.2) (2024-07-04)

**Note:** Version bump only for package @digisac/root





## [3.11.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.1-mr-2242.1...v3.11.1) (2024-07-04)

**Note:** Version bump only for package @digisac/root





# [3.11.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.0-rc.16...v3.11.0) (2024-07-03)



## [3.10.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.0-mr-2237.0...v3.10.3) (2024-07-02)

**Note:** Version bump only for package @digisac/root





## [3.10.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.10.2-mr-2228.2...v3.10.3) (2024-07-02)

**Note:** Version bump only for package @digisac/root





## [3.10.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.10.2-mr-2222.1...v3.10.2) (2024-06-28)

**Note:** Version bump only for package @digisac/root





## [3.10.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.10.1-mr-2215.0...v3.10.1) (2024-06-20)

**Note:** Version bump only for package @digisac/root





# [3.10.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.10.0-rc.6...v3.10.0) (2024-06-17)

**Note:** Version bump only for package @digisac/root





## [3.9.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.4-mr-2178.5...v3.9.5) (2024-06-05)

**Note:** Version bump only for package @digisac/root





## [3.9.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.4-mr-2163.0...v3.9.4) (2024-06-05)

**Note:** Version bump only for package @digisac/root





## [3.9.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.3-mr-2159.1...v3.9.3) (2024-05-28)

**Note:** Version bump only for package @digisac/root





## [3.9.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.2-mr-2157.1...v3.9.2) (2024-05-28)

**Note:** Version bump only for package @digisac/root





## [3.9.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.1-mr-2152.1...v3.9.1) (2024-05-22)

**Note:** Version bump only for package @digisac/root





# [3.9.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.0-rc.5...v3.9.0) (2024-05-20)

**Note:** Version bump only for package @digisac/root





## [3.8.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.4...v3.8.5) (2024-05-16)

**Note:** Version bump only for package @digisac/root





## [3.8.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.3-mr-2115.4...v3.8.4) (2024-05-09)

**Note:** Version bump only for package @digisac/root





## [3.8.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.2-mr-2124.2...v3.8.3) (2024-05-09)

**Note:** Version bump only for package @digisac/root





## [3.8.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.2-mr-2123.3...v3.8.2) (2024-05-09)

**Note:** Version bump only for package @digisac/root





## [3.8.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.1-mr-2121.1...v3.8.1) (2024-05-07)

**Note:** Version bump only for package @digisac/root





# [3.8.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1...v3.8.0) (2024-05-06)



# [3.8.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.0-rc.1...v3.8.0-rc.2) (2024-04-29)



# [3.8.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-dev.3...v3.8.0-rc.1) (2024-04-29)



## [3.7.1-dev.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2106.1...v3.7.1-dev.3) (2024-04-29)



## [3.7.1-mr-2106.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-dev.2...v3.7.1-mr-2106.1) (2024-04-29)



## [3.7.1-dev.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2106.0...v3.7.1-dev.2) (2024-04-29)



## [3.7.1-mr-2106.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.8...v3.7.1-mr-2106.0) (2024-04-29)


### Features

* **whatsapp:** adiciona as alterações relacionadas a versão 2.3000.x do whatsapp ([ba48393](https://gitlab.ikatec.cloud/digisac/digisac/commit/ba483933ed51f4b08426d1e95a6a4c5c7bb3619e))



## [3.7.1-mr-2094.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.7...v3.7.1-mr-2094.8) (2024-04-29)


### Bug Fixes

* **whatsapp:** corrige envio citação de mensagens. ([31a1671](https://gitlab.ikatec.cloud/digisac/digisac/commit/31a16713d71007d428d9a2ffb8dc5579229df027))



## [3.7.1-mr-2094.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.6...v3.7.1-mr-2094.7) (2024-04-28)



## [3.7.1-mr-2094.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.5...v3.7.1-mr-2094.6) (2024-04-28)


### Bug Fixes

* **distribution:** corrige erro apontado em trace. ([879dd7d](https://gitlab.ikatec.cloud/digisac/digisac/commit/879dd7d81d01a83c76382f7b656aec63a4aff35f))
* **whatsapp:** corrige problema de concorrência no envio de mensagem e no retorno. ([e612aff](https://gitlab.ikatec.cloud/digisac/digisac/commit/e612aff7a3959f17b6ea1330111c0d3c02c3af4c))



## [3.7.1-mr-2094.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.4...v3.7.1-mr-2094.5) (2024-04-26)



## [3.7.1-mr-2094.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-dev.1...v3.7.1-mr-2094.4) (2024-04-26)


### Bug Fixes

* **whatsapp:** incremento no timeout do evento de recebimento de mensagem, para evitar concorrência em sendAndSave. ([d45101f](https://gitlab.ikatec.cloud/digisac/digisac/commit/d45101fb33aed3b5d84e5b9ff6109bb6f62d35bc))



## [3.7.1-dev.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.3...v3.7.1-dev.1) (2024-04-25)



## [3.7.1-mr-2094.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.2...v3.7.1-mr-2094.3) (2024-04-25)



## [3.7.1-mr-2094.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-dev.0...v3.7.1-mr-2094.2) (2024-04-23)



## [3.7.1-dev.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.0...v3.7.1-dev.0) (2024-04-23)



## [3.2.1-mr-2094.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.0-mr-2098.0...v3.2.1-mr-2094.1) (2024-04-22)


### Bug Fixes

* **serverPod:** corrige inicialização da sessão no browserless ([a22d287](https://gitlab.ikatec.cloud/digisac/digisac/commit/a22d287605ee22da9c524ef02c2c1d5723b0eebc))
* **serverPod:** corrige reinicialização e backup das conexões. ([019d04e](https://gitlab.ikatec.cloud/digisac/digisac/commit/019d04e84fe2066218c9ede50dd7cb0a0c7917eb))
* **services:** corrige log para serverpod indisponível para a conexão. ([a21dde1](https://gitlab.ikatec.cloud/digisac/digisac/commit/a21dde1d37b613e0e93cf621fb5c75ab69f55bb9))


### Features

* adiciona argumento --ignore-engines no build da imagem do front. ([10f001e](https://gitlab.ikatec.cloud/digisac/digisac/commit/10f001e15a4c6e362b8a2a2ec78928353b6a3782))
* adiciona os testes e2e utilizados na LowLevel ([591cbbc](https://gitlab.ikatec.cloud/digisac/digisac/commit/591cbbc6968150f1008c520105e4da65730d84f2))
* atualiza imagem do browserless ([ad1c5c3](https://gitlab.ikatec.cloud/digisac/digisac/commit/ad1c5c3bcfad23e72c55a4f2f746d684afe5b835))
* **browser:** recupera instância da conexão com o serverpod relacionado. ([5e4b76e](https://gitlab.ikatec.cloud/digisac/digisac/commit/5e4b76ed767a90e9f8bbf844bb847e498b46df9d))
* **core:** adiciona tratativa ao nível de log warning ([7a0e70d](https://gitlab.ikatec.cloud/digisac/digisac/commit/7a0e70de18a359fc563ca35f390a72c8e6baf203))
* **core:** implementa timeout error na execução do pooling ([c89f887](https://gitlab.ikatec.cloud/digisac/digisac/commit/c89f887049929edeb3e5ca55ce365e517e5a7616))
* **tests:** atualiza test e2e do BrowserDataBackuper ([9486659](https://gitlab.ikatec.cloud/digisac/digisac/commit/94866591b23546b7f64c00d0cb60b1a773989afb))



## [3.2.1-mr-2094.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.1-mr-2087.4...v3.2.1-mr-2094.0) (2024-04-19)


### Bug Fixes

* ajustes para subir o serverPod e sincronizar conexões. ([44750bf](https://gitlab.ikatec.cloud/digisac/digisac/commit/44750bf0f2b147a42a6bb73b77422e7d2632d7c8))



## [3.2.1-mr-2087.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.6.4-mr-2092.0...v3.2.1-mr-2087.4) (2024-04-19)


### Features

* refatorações trazidas da MR-2076 ([9f8d790](https://gitlab.ikatec.cloud/digisac/digisac/commit/9f8d790c1e3a77084a4c6a6316c5a0780ec9871a))
* **whatsapp:** adiciona refatorações implementadas na MR-2076, como a remoção do PodGateway ([8241087](https://gitlab.ikatec.cloud/digisac/digisac/commit/8241087c721468fea2649ab63dd9369989c70e49))
* **whatsapp:** altera a chamada do job WhatsappRemoteRpcJob ([c5ad002](https://gitlab.ikatec.cloud/digisac/digisac/commit/c5ad002523f9b03536737dbe3cdbe2b5ba16f32b))



## [3.2.1-mr-2087.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.0-rc.2...v3.2.1-mr-2087.3) (2024-04-17)



## [3.2.1-mr-2087.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.0-rc.1...v3.2.1-mr-2087.2) (2024-04-17)



## [3.2.1-mr-2087.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.6.3...v3.2.1-mr-2087.1) (2024-04-16)



## [3.2.1-mr-2087.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.1-mr-2084.3...v3.2.1-mr-2087.0) (2024-04-16)





## [3.7.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2092.0...v3.7.1) (2024-04-30)

**Note:** Version bump only for package @digisac/root





# [3.7.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.0-mr-2097.1...v3.7.0) (2024-04-22)

**Note:** Version bump only for package @digisac/root





## [3.6.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.6.1...v3.6.3) (2024-04-16)

**Note:** Version bump only for package @digisac/root





## [3.6.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.6.0...v3.6.1) (2024-04-12)

**Note:** Version bump only for package @digisac/root





# [3.6.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.5.0...v3.6.0) (2024-04-08)

**Note:** Version bump only for package @digisac/root





# [3.5.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.5.0-rc.3...v3.5.0) (2024-03-25)



## [3.4.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.1-mr-2052.0...v3.4.5) (2024-03-13)

**Note:** Version bump only for package @digisac/root





## [3.4.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.4...v3.4.5) (2024-03-13)

**Note:** Version bump only for package @digisac/root





## [3.4.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.3...v3.4.4) (2024-03-13)


### Bug Fixes

* **campaign:** corrige import de model ([538ce7e](https://gitlab.ikatec.cloud/digisac/digisac/commit/538ce7ee38ce63e71e07871a22dbfd80ea8b2f3d))





## [3.4.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.2-mr-1981.11...v3.4.3) (2024-03-13)

**Note:** Version bump only for package @digisac/root





## [3.4.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.1...v3.4.2) (2024-03-11)

**Note:** Version bump only for package @digisac/root





## [3.4.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.0-rc.6...v3.4.1) (2024-03-11)


### Bug Fixes

* ajuste em CSS no alinhamento do contador de chamados. ([8462e34](https://gitlab.ikatec.cloud/digisac/digisac/commit/8462e34346aad3afc910e957824e7e64c5958bb8))





## [3.3.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.3.2-mr-2040.6...v3.3.2) (2024-03-01)

**Note:** Version bump only for package @digisac/root





## [3.3.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.3.1-mr-2034.0...v3.3.1) (2024-02-28)

**Note:** Version bump only for package @digisac/root





# [3.3.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.3.0-rc.11...v3.3.0) (2024-02-26)

**Note:** Version bump only for package @digisac/root





## [3.2.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.3-mr-2016.0...v3.2.3) (2024-02-14)

**Note:** Version bump only for package @digisac/root





## [3.2.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.2-mr-2015.2...v3.2.2) (2024-02-09)

**Note:** Version bump only for package @digisac/root





## [3.2.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-mr-2004.4...v3.2.1) (2024-02-07)

**Note:** Version bump only for package @digisac/root





# [3.2.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-rc.37...v3.2.0) (2024-02-06)



# [3.2.0-rc.25](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-rc.24...v3.2.0-rc.25) (2024-02-05)



# [3.2.0-rc.24](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-rc.23...v3.2.0-rc.24) (2024-02-05)



# [3.2.0-rc.23](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.8...v3.2.0-rc.23) (2024-02-05)



# [3.2.0-rc.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-rc.16...v3.2.0-rc.17) (2024-02-02)



# [3.2.0-rc.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-mr-1995.0...v3.2.0-rc.16) (2024-02-02)



# [3.2.0-mr-1995.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.8-mr-1989.0...v3.2.0-mr-1995.0) (2024-02-02)

**Note:** Version bump only for package @digisac/root





## [3.1.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.9...v3.1.10) (2024-02-06)

**Note:** Version bump only for package @digisac/root





## [3.1.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.8-mr-1998.1...v3.1.9) (2024-02-05)

**Note:** Version bump only for package @digisac/root





## [3.1.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.8-mr-1989.0...v3.1.8) (2024-02-02)

**Note:** Version bump only for package @digisac/root





## [3.1.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.6...v3.1.7) (2024-01-30)

**Note:** Version bump only for package @digisac/root





## [3.1.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.5...v3.1.6) (2024-01-30)

**Note:** Version bump only for package @digisac/root





## [3.1.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.4...v3.1.5) (2024-01-22)

**Note:** Version bump only for package @digisac/root





## [3.1.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.3...v3.1.4) (2024-01-22)

**Note:** Version bump only for package @digisac/root





## [3.1.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.2...v3.1.3) (2024-01-19)

**Note:** Version bump only for package @digisac/root





## [3.1.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.2-mr-1940.1...v3.1.2) (2024-01-16)

**Note:** Version bump only for package @digisac/root





## [3.1.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.1-mr-1934.1...v3.1.1) (2024-01-11)

**Note:** Version bump only for package @digisac/root





# [3.1.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.0-rc.33...v3.1.0) (2024-01-10)

**Note:** Version bump only for package @digisac/root





## [3.0.19](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.18-mr-1907.3...v3.0.19) (2024-01-02)

**Note:** Version bump only for package @digisac/root





## [3.0.18](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.17-mr-1913.1...v3.0.18) (2024-01-02)

**Note:** Version bump only for package @digisac/root





## [3.0.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.16...v3.0.17) (2024-01-02)

**Note:** Version bump only for package @digisac/root





## [3.0.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.16-mr-1882.1...v3.0.16) (2023-12-11)

**Note:** Version bump only for package @digisac/root





## [3.0.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.15-mr-1849.0...v3.0.15) (2023-11-17)

**Note:** Version bump only for package @digisac/root





## [3.0.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.13...v3.0.14) (2023-11-08)


### Reverts

* Revert "fix: validação para migrar estrutura do  serverpod da low level para digisac" ([f029477](https://gitlab.ikatec.cloud/digisac/digisac/commit/f029477b6646177c75eaa81425b017b350178578))





## [3.0.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.12...v3.0.13) (2023-11-08)


### Bug Fixes

* validação para migrar estrutura do  serverpod da low level para digisac ([b087442](https://gitlab.ikatec.cloud/digisac/digisac/commit/b087442ddcc8f0bf228633deee569ba5117f4fab))





## [3.0.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.11...v3.0.12) (2023-11-03)

**Note:** Version bump only for package @digisac/root





## [3.0.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.11-mr-1827.0...v3.0.11) (2023-10-30)

**Note:** Version bump only for package @digisac/root





## [3.0.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.9...v3.0.10) (2023-09-20)


### Bug Fixes

* **contacts:** MN-5216 - correção na exportação de contatos ([fa36704](https://gitlab.ikatec.cloud/digisac/digisac/commit/fa367048037582006703294203260ee60178057e))





## [3.0.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.8...v3.0.9) (2023-09-20)


### Bug Fixes

* **services:** MN-5201 - Correção na sincronização de templates da Gupshup ([53e72ab](https://gitlab.ikatec.cloud/digisac/digisac/commit/53e72abe6c0e9cd3a8c6b3c519d0e5e618f6062a))





## [3.0.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.7...v3.0.8) (2023-09-20)


### Bug Fixes

* correção de versão ([141d9cd](https://gitlab.ikatec.cloud/digisac/digisac/commit/141d9cdd3ee477208ade368387050f493a7d6b2f))
* correção para executar o lerna version ([15e29b8](https://gitlab.ikatec.cloud/digisac/digisac/commit/15e29b804396878191d4fe8f6d68b7872346e850))





## [3.0.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.5...v3.0.7) (2023-08-17)

**Note:** Version bump only for package @digisac/root





## [3.0.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.0-mr-1632.14...v3.0.5) (2023-08-11)


### Features

* test subindo feature ([e07bc51](https://gitlab.ikatec.cloud/digisac/digisac/commit/e07bc516e993d98fc5ed0d21523164206503f455))





# [3.0.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.0.1-mr-1358.37...v3.0.0) (2023-01-31)
## [2.10.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.10.4-mr-1435.0...v2.10.5) (2023-01-16)

**Note:** Version bump only for package @digisac/root





## [2.10.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.10.4-mr-1434.0...v2.10.4) (2023-01-16)

**Note:** Version bump only for package @digisac/root





## [2.10.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.10.2...v2.10.3) (2022-12-21)


### Bug Fixes

* Release - 2.10.3 ([9f051bc](https://gitlab.ikatec.cloud/digisac/digisac/commit/9f051bc3a13ed322530aa7434017c8634a7bcdb5))





## [2.10.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.10.1...v2.10.2) (2022-12-21)


### Bug Fixes

* Release - 2.10.2 ([4bc3c5c](https://gitlab.ikatec.cloud/digisac/digisac/commit/4bc3c5ce6202142fa064dc88b2f91833d9d1edad))





## [2.10.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.10.0...v2.10.1) (2022-12-15)


### Bug Fixes

* MN-4317- remove linhas reintroduzidas por resolução de conflito no controller de contacts ([c44785e](https://gitlab.ikatec.cloud/digisac/digisac/commit/c44785e9fa6ad242c25a460760c02e9a045ab64c))





# [2.10.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.9.2...v2.10.0) (2022-12-15)


### Features

* release 2.9.5 ([9bb1f89](https://gitlab.ikatec.cloud/digisac/digisac/commit/9bb1f8925dbc3ea28f41ae54d6b77c3a71d17fc4))





## [2.9.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.9.1...v2.9.2) (2022-10-14)

**Note:** Version bump only for package @digisac/root





## [2.9.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.9.0...v2.9.1) (2022-10-10)


### Bug Fixes

* MN-4223 - Corrige migração de hsm para templates e interpolação de parâmetros ([739045f](https://gitlab.ikatec.cloud/digisac/digisac/commit/739045f752d7a5d783c5ab97e315ff8f27c9807e))





# [2.9.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.8.1...v2.9.0) (2022-10-03)


### Features

* Atualização da master 2.8.1 ([b984950](https://gitlab.ikatec.cloud/digisac/digisac/commit/b984950c3ae837c4bb20a1c7982e689cecece353))





## [2.8.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.8.0...v2.8.1) (2022-09-05)


### Reverts

* Revert "chore(release): bump version v2.7.2" ([765719f](https://gitlab.ikatec.cloud/digisac/digisac/commit/765719fb9a5a4597c62d5db163ff1b01057d644d))





## [2.7.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.0...v2.7.1) (2022-08-16)


### Bug Fixes

* **account:** SI-76 - exclui a permissão tickets.view.all.departments na criação do cargo Administrador. ([d2a9730](https://gitlab.ikatec.cloud/digisac/digisac/commit/d2a973041f03792c6fb35b378dd9abd0a2553430))





# [2.7.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.6.2...v2.7.0) (2022-08-08)



## [2.4.1-rc.31](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.30...v2.4.1-rc.31) (2022-08-02)


### Bug Fixes

* SI-13 - Retirar opção de legenda dos anexos nas conexões Instagram e Facebook ([dd143fa](https://gitlab.ikatec.cloud/digisac/digisac/commit/dd143fa85fe31f5e190895728e9714b9f39447f9))



## [2.4.1-rc.30](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.29...v2.4.1-rc.30) (2022-08-02)


### Bug Fixes

* SI-39 - altera a propriedade resize para default em campos do tipo textarea ([cc1902f](https://gitlab.ikatec.cloud/digisac/digisac/commit/cc1902fe89a26ea853dbbd09e793fcc20985b246))



## [2.4.1-rc.29](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.28...v2.4.1-rc.29) (2022-08-02)



## [2.4.1-rc.28](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.27...v2.4.1-rc.28) (2022-07-29)


### Features

* **tutorials:** MN-3698 - nova ordem dos videos tutoriais ([a277283](https://gitlab.ikatec.cloud/digisac/digisac/commit/a277283fa9bba2b18775332ea90b2fd34727f618))



## [2.4.1-rc.27](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1...v2.4.1-rc.27) (2022-07-29)


### Features

* QA-229 - automação nova tela de campanha ([063865b](https://gitlab.ikatec.cloud/digisac/digisac/commit/063865b66d5d7e8f7ec5cd59ebbb445fc5b5f40f))



## [2.4.1-rc.26](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.25...v2.4.1-rc.26) (2022-07-22)



## [2.4.1-rc.25](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.24...v2.4.1-rc.25) (2022-07-22)



## [2.4.1-rc.24](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.23...v2.4.1-rc.24) (2022-07-22)


### Features

* **filter:** MN-3495 - Possibilidade de filtrar contatos sem tags ([dbebdc9](https://gitlab.ikatec.cloud/digisac/digisac/commit/dbebdc93220e5be9363bb72660f88efd3eb42aa1))



## [2.4.1-rc.23](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.22...v2.4.1-rc.23) (2022-07-20)


### Bug Fixes

* **colors:** MN-3635 - alteração na cores do Digisac ([02bf76b](https://gitlab.ikatec.cloud/digisac/digisac/commit/02bf76b54aae8971d6f7d2a2166ae677b70876fb))



## [2.4.1-rc.22](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.21...v2.4.1-rc.22) (2022-07-20)


### Bug Fixes

* **language:** MN-3674 - modal de cadastro de cargo sem internacionalização ([ccd248f](https://gitlab.ikatec.cloud/digisac/digisac/commit/ccd248ff0f7d7f962c4a1be9b95656d27665903c))



## [2.4.1-rc.21](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.20...v2.4.1-rc.21) (2022-07-20)


### Bug Fixes

* **campaign:** MN-3424 - Restrições na campanha ([7823c37](https://gitlab.ikatec.cloud/digisac/digisac/commit/7823c372163357e98e688db58abb1ea2c21823e3))



## [2.4.1-rc.20](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.19...v2.4.1-rc.20) (2022-07-18)


### Bug Fixes

* **internalChat:** correção na validação para geração de token ([50dce64](https://gitlab.ikatec.cloud/digisac/digisac/commit/50dce644617b8380f8c918dd9684c57993e3f300))



## [2.4.1-rc.19](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.18...v2.4.1-rc.19) (2022-07-15)


### Bug Fixes

* **internalChat:**  correção de verificação ([253a5b8](https://gitlab.ikatec.cloud/digisac/digisac/commit/253a5b831f86a2377a96c594c2d562ecaf07629e))



## [2.4.1-rc.18](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.17...v2.4.1-rc.18) (2022-07-14)


### Bug Fixes

* **internalChat:** MN-3980 - correção na geração de token ([42360a6](https://gitlab.ikatec.cloud/digisac/digisac/commit/42360a6792196c3718abe782afaf4d6e1e1ed9ae))



## [2.4.1-rc.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.16...v2.4.1-rc.17) (2022-07-13)



## [2.4.1-rc.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.15...v2.4.1-rc.16) (2022-07-12)


### Bug Fixes

* **thumbnails:** MN-2847 -Duplicação de imagens thumbnails ([08d501b](https://gitlab.ikatec.cloud/digisac/digisac/commit/08d501b900b567a2ecb66365673e800308271d28))



## [2.4.1-rc.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.0...v2.4.1-rc.15) (2022-07-12)


### Bug Fixes

* **profile:** SI-50 - corrige quando tenta salvar senha em branco na tela de perfil ([a3ab26c](https://gitlab.ikatec.cloud/digisac/digisac/commit/a3ab26c6ea1f5fc2a6d560412080638afb9611bf))





## [2.6.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.6.1...v2.6.2) (2022-08-05)


### Bug Fixes

* SI-138  - correção de parâmetros de HSM  e flags do chat interno ([c6d8d6d](https://gitlab.ikatec.cloud/digisac/digisac/commit/c6d8d6dd9c9a0a653373dbff720974c1039e6aa5))





## [2.6.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.6.0...v2.6.1) (2022-08-05)


### Bug Fixes

* versão 2.5.1 ([c5076fe](https://gitlab.ikatec.cloud/digisac/digisac/commit/c5076fe349ac9169a7afa5b56c5260d80bff57f3))





# [2.6.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.5...v2.6.0) (2022-08-05)


### Bug Fixes

* update keys for digisac.biz ([bd63901](https://gitlab.ikatec.cloud/digisac/digisac/commit/bd63901b243d888b6c732354c9b92fc45e7b712d))


### Features

* adiciona digisac.biz dentro do projeto ([c7cdcd4](https://gitlab.ikatec.cloud/digisac/digisac/commit/c7cdcd43f66abae24bee6bdddb92011fcaa0c47f))





## [2.5.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.4...v2.5.5) (2022-08-05)

**Note:** Version bump only for package @digisac/root





## [2.5.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.3...v2.5.4) (2022-08-05)


### Bug Fixes

* version 2.5.0 ([100e5c6](https://gitlab.ikatec.cloud/digisac/digisac/commit/100e5c63fac5563e1b1da540f5a97c0657f9eaf6))





## [2.5.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.2...v2.5.3) (2022-08-05)


### Bug Fixes

* version 2.4.3 ([f417337](https://gitlab.ikatec.cloud/digisac/digisac/commit/f417337366499b1181c1c35ba0eba109faec2be1))





## [2.5.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.1...v2.5.2) (2022-08-04)

**Note:** Version bump only for package @digisac/root





## [2.5.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1...v2.5.1) (2022-08-04)

**Note:** Version bump only for package @digisac/root





## [2.4.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.0...v2.4.1) (2022-07-29)


### Bug Fixes

* **whatsApp:** MN-4026 - Atualiza versão do WhatsApp web para 2.2226.5 ([fb5349c](https://gitlab.ikatec.cloud/digisac/digisac/commit/fb5349cc2918fea70fe34943d18c819f8838906d))





# [2.4.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.3.2...v2.4.0) (2022-07-04)


### Features

* **version:** versão 2.4.3 ([948d839](https://gitlab.ikatec.cloud/digisac/digisac/commit/948d83905515c4787772b2ec521bd10f77039f0f))





## [2.3.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.3.1...v2.3.2) (2022-06-10)


### Bug Fixes

* corrige o tratamento de usuário já autenticado em outra sessão ([91f5a9b](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/91f5a9bbbcbf97c05d60e9b06c85ba373fd58aaa))





## [2.3.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.3.0...v2.3.1) (2022-06-06)


### Bug Fixes

* **bot:** correções e melhorias no socket ([2b87642](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/2b8764230e78d132401866b7a88c6cd301088e5b))





# [2.3.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.6...v2.3.0) (2022-05-30)


### Features

* nova conexão com Google Businesss ([3f5e6dd](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/3f5e6dd4761c99bf6175a2825e49ad237bb9177e))





## [2.2.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.6-mr-1153.3...v2.2.6) (2022-05-26)

**Note:** Version bump only for package @digisac/root





## [2.2.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.4-rc.12...v2.2.5) (2022-05-23)



## [2.2.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.4-rc.1...v2.2.4) (2022-05-09)

**Note:** Version bump only for package @digisac/root





## [2.2.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.4-rc.1...v2.2.4) (2022-05-09)



## [2.2.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.2...v2.2.3) (2022-04-27)


### Bug Fixes

* **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([bf0359c](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/bf0359c2521ec73756f105230d8db70f37c1b960))



## [2.2.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.1...v2.2.2) (2022-04-20)


### Bug Fixes

* **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([aaa401e](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/aaa401eace6a85ff60291b310a0337e803dc45a9))



## [2.2.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.0...v2.2.1) (2022-04-11)


### Bug Fixes

* **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([38880cd](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/38880cdf931a33fc4c60692e28be54a66671fbb8))



# [2.2.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.245...v2.2.0) (2022-04-07)



## [2.0.1-rc.245](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.244...v2.0.1-rc.245) (2022-04-07)



## [2.0.1-rc.244](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.243...v2.0.1-rc.244) (2022-04-07)



## [2.0.1-rc.243](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.1.0...v2.0.1-rc.243) (2022-04-07)



# [2.1.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.242...v2.1.0) (2022-04-06)


### Features

* ajusta versao de master ([c3fba04](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/c3fba048b00e951e84f80a68aee93e4cf0a3c65a))
* ajusta versao de master ([add0443](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/add04433bf8cf5260b4d567059cc0c172cf889fd))
* ajusta versao de master ([d6d90e9](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/d6d90e9a9f12be645424f0d52310e8cd2cfd75f5))
* ajusta versao de master ([30f5452](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/30f5452dcbeb6dad74a44d85573dd18819ca3b1d))



## [2.0.1-rc.242](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.241...v2.0.1-rc.242) (2022-04-06)



## [2.0.1-rc.241](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.240...v2.0.1-rc.241) (2022-04-06)



## [2.0.1-rc.240](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.239...v2.0.1-rc.240) (2022-04-06)



## [2.0.1-rc.239](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-mr-1022.0...v2.0.1-rc.239) (2022-04-06)



## [2.0.1-mr-1022.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.238...v2.0.1-mr-1022.0) (2022-04-06)



## [2.0.1-rc.238](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.237...v2.0.1-rc.238) (2022-04-05)



## [2.0.1-rc.237](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.236...v2.0.1-rc.237) (2022-04-05)


### Bug Fixes

* **style:** corrige ajustes de layout realizados no chat para evitar barra de rolagem, porém tinha quebrado a exportação de pdf. ([070ef12](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/070ef12fcf5397679dd47936a31ddb9d681b27e8))



## [2.0.1-rc.236](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.235...v2.0.1-rc.236) (2022-04-05)



## [2.0.1-rc.235](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.234...v2.0.1-rc.235) (2022-04-04)



## [2.0.1-rc.234](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.233...v2.0.1-rc.234) (2022-04-04)



## [2.0.1-rc.233](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.232...v2.0.1-rc.233) (2022-04-04)



## [2.0.1-rc.232](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.231...v2.0.1-rc.232) (2022-04-01)



## [2.0.1-rc.231](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.230...v2.0.1-rc.231) (2022-03-30)



## 2.0.1-rc.230 (2022-03-30)





## [2.2.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.2...v2.2.3) (2022-04-27)


### Bug Fixes

* **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([bf0359c](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/bf0359c2521ec73756f105230d8db70f37c1b960))





## [2.2.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.1...v2.2.2) (2022-04-20)


### Bug Fixes

* **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([aaa401e](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/aaa401eace6a85ff60291b310a0337e803dc45a9))





## [2.2.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.0...v2.2.1) (2022-04-11)


### Bug Fixes

* **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([38880cd](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/38880cdf931a33fc4c60692e28be54a66671fbb8))





# [2.2.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.245...v2.2.0) (2022-04-07)



# [2.1.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.242...v2.1.0) (2022-04-06)


### Features

* ajusta versao de master ([c3fba04](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/c3fba048b00e951e84f80a68aee93e4cf0a3c65a))
* ajusta versao de master ([add0443](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/add04433bf8cf5260b4d567059cc0c172cf889fd))
* ajusta versao de master ([d6d90e9](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/d6d90e9a9f12be645424f0d52310e8cd2cfd75f5))
* ajusta versao de master ([30f5452](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/30f5452dcbeb6dad74a44d85573dd18819ca3b1d))



## [2.0.1-rc.241](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.240...v2.0.1-rc.241) (2022-04-06)





# [2.1.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.241...v2.1.0) (2022-04-06)


### Features

* ajusta versao de master ([c3fba04](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/c3fba048b00e951e84f80a68aee93e4cf0a3c65a))
* ajusta versao de master ([add0443](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/add04433bf8cf5260b4d567059cc0c172cf889fd))
* ajusta versao de master ([d6d90e9](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/d6d90e9a9f12be645424f0d52310e8cd2cfd75f5))
* ajusta versao de master ([30f5452](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/30f5452dcbeb6dad74a44d85573dd18819ca3b1d))





**Note:** Version bump only for package @digisac/root
## [1.179.3-mr-367.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-mr-367.0...v1.179.3-mr-367.1) (2021-08-30)
## [1.179.3-mr-368.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.7...v1.179.3-mr-368.0) (2021-08-28)
## [1.179.74](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.73...v1.179.74) (2022-01-04)


### Bug Fixes

* **whatsapp:** altera consulta que força sincronização de mensagens para recuperar os idFromServices distintamente. ([04f7445](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/04f74457684bc477c45ea24f0202625ba1ef093c))





## [1.179.73](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.72...v1.179.73) (2022-01-03)

**Note:** Version bump only for package @digisac/root





## [1.179.72](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.71...v1.179.72) (2022-01-03)

**Note:** Version bump only for package @digisac/root





## [1.179.71](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.70...v1.179.71) (2022-01-03)

**Note:** Version bump only for package @digisac/root





## [1.179.70](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.69...v1.179.70) (2022-01-03)


### Bug Fixes

* **statistics:** ajusta a comparação de datas pois devido ao timezone alguns tickets estavam sendo contados no dia seguinte. ([d688bca](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/d688bca64674ae11c7981e3bca0367d0cbd8a9a0))





## [1.179.69](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.68...v1.179.69) (2022-01-03)


### Bug Fixes

* **integrations:** ajusta permissão de visualização para os pontos onde se tem acesso a integrações. ([ac07e20](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/ac07e207ce2de0a51987878a1b3d0c00435b19a3))





## [1.179.68](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.67...v1.179.68) (2022-01-03)

**Note:** Version bump only for package @digisac/root





## [1.179.67](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.66...v1.179.67) (2021-12-20)

**Note:** Version bump only for package @digisac/root





## [1.179.66](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.65...v1.179.66) (2021-12-20)


### Bug Fixes

* **contacts:** corrige pontos do sistema em que o ofuscamento de número de telefones não estava atendendo a permissão contacts.view.number ([7aa5e2b](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7aa5e2bbf792f263a105aab511e02ed01b99cc48))





## [1.179.65](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.64...v1.179.65) (2021-12-10)

**Note:** Version bump only for package @digisac/root





## [1.179.64](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.63...v1.179.64) (2021-12-10)

**Note:** Version bump only for package @digisac/root





## [1.179.63](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.62...v1.179.63) (2021-12-10)

**Note:** Version bump only for package @digisac/root





## [1.179.62](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.61...v1.179.62) (2021-12-10)

**Note:** Version bump only for package @digisac/root





## [1.179.61](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.60...v1.179.61) (2021-12-10)


### Bug Fixes

* **timezones:** Copia os arquivos do package node_modules/timezones.json, para utils tanto no back como no front; Modifica as abreviações que estão em duplicidade para serem tratadas distintamente. ([746a572](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/746a572317d7b4a310b27f7947c6f44072b19265))





## [1.179.60](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.59...v1.179.60) (2021-11-27)

**Note:** Version bump only for package @digisac/root





## [1.179.59](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.58...v1.179.59) (2021-11-27)

**Note:** Version bump only for package @digisac/root





## [1.179.58](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.57...v1.179.58) (2021-11-27)

**Note:** Version bump only for package @digisac/root





## [1.179.57](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.56...v1.179.57) (2021-11-18)

**Note:** Version bump only for package @digisac/root





## [1.179.56](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.55...v1.179.56) (2021-11-18)

**Note:** Version bump only for package @digisac/root





## [1.179.55](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.54...v1.179.55) (2021-11-12)

**Note:** Version bump only for package @digisac/root





## [1.179.54](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.53...v1.179.54) (2021-11-12)

**Note:** Version bump only for package @digisac/root





## [1.179.53](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.52...v1.179.53) (2021-11-12)


### Features

* **email:** disponibiliza script que reconverte mensagens de e-mail que já foram convertidas a partir do HTML, necessário quando não foi recebido o text pelo IMAP. ([550adec](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/550adecdd866afa17c371fac6bf0d86d7cede132))





## [1.179.52](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.51...v1.179.52) (2021-11-12)

**Note:** Version bump only for package @digisac/root





## [1.179.51](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.50...v1.179.51) (2021-11-12)

**Note:** Version bump only for package @digisac/root





## [1.179.50](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.49...v1.179.50) (2021-11-12)

**Note:** Version bump only for package @digisac/root





## [1.179.49](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.48...v1.179.49) (2021-11-12)

**Note:** Version bump only for package @digisac/root





## [1.179.48](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.47...v1.179.48) (2021-11-01)

**Note:** Version bump only for package @digisac/root





## [1.179.47](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.46...v1.179.47) (2021-11-01)

**Note:** Version bump only for package @digisac/root





## [1.179.46](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.45...v1.179.46) (2021-10-29)

**Note:** Version bump only for package @digisac/root





## [1.179.45](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.44...v1.179.45) (2021-10-27)

**Note:** Version bump only for package @digisac/root





## [1.179.44](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.43...v1.179.44) (2021-10-26)

**Note:** Version bump only for package @digisac/root





## [1.179.43](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.42...v1.179.43) (2021-10-26)

**Note:** Version bump only for package @digisac/root





## [1.179.42](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.41...v1.179.42) (2021-10-26)

**Note:** Version bump only for package @digisac/root





## [1.179.41](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.40...v1.179.41) (2021-10-26)

**Note:** Version bump only for package @digisac/root





## [1.179.40](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.39...v1.179.40) (2021-10-26)

**Note:** Version bump only for package @digisac/root





## [1.179.39](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.38...v1.179.39) (2021-10-22)


### Features

* **files:** trata range em arquivos ([97cb074](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/97cb0740ad945cd361f444ea569c27a0ea629d78))





## [1.179.3-rc.7](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.6...v1.179.3-rc.7) (2021-08-26)

**Note:** Version bump only for package @digisac/root





## [1.179.3-mr-321.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.6...v1.179.3-mr-321.6) (2021-08-26)



## [1.179.3-mr-321.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.3...v1.179.3-mr-321.5) (2021-08-25)
## [1.179.13](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.13) (2021-09-15)
## [1.179.44](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.43...v1.179.44) (2021-10-26)

**Note:** Version bump only for package @digisac/root

## [1.179.43](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.42...v1.179.43) (2021-10-26)




## [1.179.3-mr-367.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.8...v1.179.3-mr-367.0) (2021-08-27)

**Note:** Version bump only for package @digisac/root





## [1.179.3-rc.8](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.7...v1.179.3-rc.8) (2021-08-27)

**Note:** Version bump only for package @digisac/root

**Note:** Version bump only for package @digisac/root

## [1.179.42](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.41...v1.179.42) (2021-10-26)

**Note:** Version bump only for package @digisac/root

## [1.179.41](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.40...v1.179.41) (2021-10-26)

**Note:** Version bump only for package @digisac/root

## [1.179.40](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.39...v1.179.40) (2021-10-26)

**Note:** Version bump only for package @digisac/root

## [1.179.39](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.38...v1.179.39) (2021-10-22)

## [1.178.18-mr-321.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-241.15...v1.178.18-mr-321.0) (2021-08-10)
## [1.179.12](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.12) (2021-09-14)
### Bug Fixes

- **migration:** corrige os files que que estão deletados porém associados a contatos e conexões ativas. ([c0929a5](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/c0929a54d9b5ae89dd87114305bd74a8d15eb41e))

## [1.179.38](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.37...v1.179.38) (2021-10-21)

**Note:** Version bump only for package @digisac/root

## [1.179.37](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.36...v1.179.37) (2021-10-21)

**Note:** Version bump only for package @digisac/root

## [1.179.36](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.35...v1.179.36) (2021-10-21)

**Note:** Version bump only for package @digisac/root

## [1.179.11](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.8...v1.179.11) (2021-09-10)
## [1.179.35](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.34...v1.179.35) (2021-10-21)

**Note:** Version bump only for package @digisac/root

## [1.179.34](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.33-mr-488.2...v1.179.34) (2021-10-21)

**Note:** Version bump only for package @digisac/root

## [1.179.33](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.33-mr-499.0...v1.179.33) (2021-10-19)

**Note:** Version bump only for package @digisac/root

## [1.179.3-rc.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.5...v1.179.3-rc.6) (2021-08-26)
## [1.179.10](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.8...v1.179.10) (2021-09-03)
## [1.179.32](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.31...v1.179.32) (2021-10-18)

**Note:** Version bump only for package @digisac/root

## [1.179.31](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.30...v1.179.31) (2021-10-14)

**Note:** Version bump only for package @digisac/root

## [1.179.30](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.29...v1.179.30) (2021-10-14)

**Note:** Version bump only for package @digisac/root

## [1.179.29](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.9...v1.179.29) (2021-10-13)

## [1.179.3-mr-334.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.5...v1.179.3-mr-334.0) (2021-08-26)



## [1.178.21-mr-334.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-rc.3...v1.178.21-mr-334.3) (2021-08-23)



## [1.178.21-mr-334.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.22...v1.178.21-mr-334.2) (2021-08-18)



## [1.178.18-mr-334.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.20-mr-318.2...v1.178.18-mr-334.0) (2021-08-13)
## [1.179.8](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.7...v1.179.8) (2021-09-02)
### Bug Fixes

- corrige script de check-version.sh ([898142a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/898142a1657a2e33ec627628d74c03d2048e6a06))

## [1.179.9](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.28...v1.179.9) (2021-10-13)

### Bug Fixes

- corrige lint em stable ([cd0604c](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/cd0604c429b7d52c1bcc242244fa8467a5c94ab8))

## [1.179.28](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.27...v1.179.28) (2021-10-11)

## [1.179.3-rc.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.4...v1.179.3-rc.5) (2021-08-26)
## [1.179.7](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.5...v1.179.7) (2021-09-02)
## [1.178.23-mr-354.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.22...v1.178.23-mr-354.0) (2021-08-20)

**Note:** Version bump only for package @digisac/root

## [1.179.27](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.27-mr-438.0...v1.179.27) (2021-10-11)

**Note:** Version bump only for package @digisac/root

## [1.179.26](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.13...v1.179.26) (2021-10-04)

**Note:** Version bump only for package @digisac/root

## [1.178.21-mr-344.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.4...v1.178.21-mr-344.2) (2021-08-26)



## [1.178.21-mr-344.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-344.0...v1.178.21-mr-344.1) (2021-08-18)



## [1.178.18-mr-344.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.22...v1.178.18-mr-344.0) (2021-08-18)
## [1.179.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.5...v1.179.6) (2021-09-01)
## [1.179.13](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.25...v1.179.13) (2021-10-04)

**Note:** Version bump only for package @digisac/root

## [1.179.25](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.24...v1.179.25) (2021-10-04)

**Note:** Version bump only for package @digisac/root

## [1.179.24](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.23...v1.179.24) (2021-09-30)

### Bug Fixes

## [1.179.3-rc.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-mr-182.2...v1.179.3-rc.4) (2021-08-26)
## [1.179.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.5) (2021-08-31)
- Update lerna.json version ([5b84d96](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/5b84d96b29677bbaa333d8936c6f958c8793eff1))

## [1.179.23](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.22...v1.179.23) (2021-09-30)

### Bug Fixes

- corrige export de chats ([ecf912c](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/ecf912c5cff9e858505d6d31f07cf7edd93da1e4))

## [1.179.22](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.21...v1.179.22) (2021-09-30)

### Bug Fixes

- lerna version ([adcab35](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/adcab35ac8bc4f81e521606cb469394a2fde7f0d))

## [1.179.14](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.14) (2021-09-30)

### Bug Fixes

- corrige savePermissions ([9397c70](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/9397c702a4d31db16100297dfe64c7f571049ca9))

## [1.179.21](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.21) (2021-09-30)

### Bug Fixes

- corrige savePermissions ([9397c70](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/9397c702a4d31db16100297dfe64c7f571049ca9))

## [1.179.20](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.20) (2021-09-24)

### Bug Fixes

- corrige savePermissions ([9397c70](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/9397c702a4d31db16100297dfe64c7f571049ca9))

## [1.179.19](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.19) (2021-09-23)

### Bug Fixes

- corrige savePermissions ([9397c70](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/9397c702a4d31db16100297dfe64c7f571049ca9))

## [1.179.18](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.18) (2021-09-23)

**Note:** Version bump only for package @digisac/root

## [1.179.17](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.15...v1.179.17) (2021-09-23)

**Note:** Version bump only for package @digisac/root

## [1.179.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.15...v1.179.4) (2021-09-23)

**Note:** Version bump only for package @digisac/root

## [1.178.21-mr-182.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.3...v1.178.21-mr-182.2) (2021-08-26)



## [1.178.21-mr-182.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-mr-182.0...v1.178.21-mr-182.1) (2021-08-17)



## [1.178.21-mr-182.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21...v1.178.21-mr-182.0) (2021-08-17)



## [1.178.18-mr-182.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-182.4...v1.178.18-mr-182.5) (2021-08-05)



## [1.178.18-mr-182.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-269.1...v1.178.18-mr-182.4) (2021-08-05)



## [1.178.18-mr-182.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-307.4...v1.178.18-mr-182.3) (2021-08-04)



## [1.178.18-mr-182.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-rc.9...v1.178.18-mr-182.2) (2021-08-02)



## [1.178.18-mr-182.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-182.0...v1.178.18-mr-182.1) (2021-08-02)



## [1.178.18-mr-182.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-rc.8...v1.178.18-mr-182.0) (2021-08-02)



## [1.178.15-mr-182.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-290.4...v1.178.15-mr-182.3) (2021-07-30)



## [1.178.15-mr-182.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-241.11...v1.178.15-mr-182.2) (2021-07-30)


## [1.179.3-rc.10](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.9...v1.179.3-rc.10) (2021-08-30)

## [1.179.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.3) (2021-08-25)
## [1.178.15-mr-182.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.15-mr-182.0...v1.178.15-mr-182.1) (2021-07-23)



## [1.178.15-mr-182.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.15-mr-245.0...v1.178.15-mr-182.0) (2021-07-23)



## [1.178.12-mr-182.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.14...v1.178.12-mr-182.1) (2021-07-22)



## [1.178.12-mr-182.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.2-mr-269.5...v1.178.12-mr-182.0) (2021-07-20)



## [1.168.1-mr-182.7](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.172.2-rc.8...v1.168.1-mr-182.7) (2021-06-25)



## [1.168.1-mr-182.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-177.6...v1.168.1-mr-182.6) (2021-06-22)



## [1.168.1-mr-182.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-182.4...v1.168.1-mr-182.5) (2021-06-15)



## [1.168.1-mr-182.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-182.3...v1.168.1-mr-182.4) (2021-06-15)



## [1.168.1-mr-182.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-182.2...v1.168.1-mr-182.3) (2021-06-14)



## [1.168.1-mr-182.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-182.1...v1.168.1-mr-182.2) (2021-06-14)



## [1.168.1-mr-182.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-182.0...v1.168.1-mr-182.1) (2021-06-14)



## [1.168.1-mr-182.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.0...v1.168.1-mr-182.0) (2021-06-14)



# 1.168.0 (2021-05-26)
## [1.179.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.4) (2021-08-27)
## [1.179.16](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.15...v1.179.16) (2021-09-23)

**Note:** Version bump only for package @digisac/root

## [1.179.15](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.15) (2021-09-23)

### Bug Fixes

- modifica yarn.lock ([6823fee](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/6823feeb36a9e9be70ae3be0a5a371970d6c8429))
- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))

### Performance Improvements

- melhora regex em build-image.sh ([a0c8967](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/a0c89674bfa6a8ad11651d8cd8f32f130e945f32))

## [1.179.3-rc.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.2...v1.179.3-rc.3) (2021-08-25)
## [1.179.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.3) (2021-08-25)
## [1.179.14](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.14) (2021-09-23)

### Bug Fixes

- modifica yarn.lock ([6823fee](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/6823feeb36a9e9be70ae3be0a5a371970d6c8429))
- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))

### Performance Improvements

- melhora regex em build-image.sh ([a0c8967](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/a0c89674bfa6a8ad11651d8cd8f32f130e945f32))

## [1.179.15](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.15) (2021-09-21)

### Bug Fixes

- modifica yarn.lock ([6823fee](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/6823feeb36a9e9be70ae3be0a5a371970d6c8429))
- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))

### Performance Improvements

- melhora regex em build-image.sh ([a0c8967](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/a0c89674bfa6a8ad11651d8cd8f32f130e945f32))

## [1.179.14](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.14) (2021-09-21)

### Bug Fixes

- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))

### Performance Improvements

- melhora regex em build-image.sh ([a0c8967](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/a0c89674bfa6a8ad11651d8cd8f32f130e945f32))

## [1.179.13](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.13) (2021-09-15)

## [1.157.1-mr-62.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.157.0...v1.157.1-mr-62.0) (2021-04-07)

## [1.179.3-mr-362.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.3...v1.179.3-mr-362.0) (2021-08-25)

**Note:** Version bump only for package @digisac/root

## [1.152.1-mr-56.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.155.1-mr-49.0...v1.152.1-mr-56.0) (2021-03-31)

## [1.155.1-mr-49.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.155.0...v1.155.1-mr-49.0) (2021-03-31)

### Bug Fixes

- **bug:** corrige reset db for teste ([5702fe9](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/5702fe9df967bf2409cabc5729626469cca0e2b0))
- **bug:** remove o elemento adicionado para teste ([917074b](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/917074b8dceb75b84af1bfcc758341dbb511eb4a))
- **general:** adicina msg para verificar alteracao de versao ([daf2a76](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/daf2a760e0f645f179bd160da8070a1d1f0f17bc))
- **route:** esconde temporariamente rota de estatistica de conversa ([81bc771](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/81bc7711612246449ca2a65b5967a94460fd6f79))

## [1.149.1-mr-54.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.149.0...v1.149.1-mr-54.0) (2021-03-31)

### Reverts

- Revert "resolve commit errado" ([faa0d83](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/faa0d83f4bd9758646db510559f4927485c1b1f3))

## [1.132.5-mr-53.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.13...v1.132.5-mr-53.0) (2021-03-31)

### Bug Fixes

- **account:** adiciona novas regras no modelo de bloqueio de conta ([60afcef](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/60afcef9981d432a91374bc26e0e59fa7e18ba78))
- **account:** adiciona url em variavel de ambiente ([1d13709](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/1d13709bac2b4f5de2f3d2d3c53977544c40f445))
- **account:** corrige variavel de ambiente p/ envio do webhook ([a417fc6](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/a417fc6b35b7550324f58e24a6de90f5505a7873))
- **ci:** corrigido problema de build em mr ([5dfc61d](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/5dfc61da69eef77ee686aa29788131d1494fb2de))
- **ci:** nao seta tags desnecessarias ([e986464](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/e986464eb84f5e96ee937bf6cf93f9b1880a1456))
- **constraint:** adiciona fk com metodo RESTRICT ([3d17c39](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/3d17c39558c9365b77fbd5c06a2d6fb4e7e5a8b8))
- **cypress:** corrigido testes de conexao e2e ([5d4d53f](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/5d4d53f0d9b841b1677c6bbbab3c7f34b8910e96))
- **deleteAll:** decodeURIComponent inserido ([3a43bc6](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/3a43bc68e521b3a8043412fcac0dc5f72bc0c89b))
- **env:** adiciona valor padrao no env-example ([760ea3e](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/760ea3e4fc854fcab06633f52e42594198df4d97))
- **schedule:** corrige verificacao de conexao do contato ([63c07f4](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/63c07f4b2f42dc2fa15d2cb0e7cba919c37160af))
- **Statistics:** Alteraçao nas variavel fora de padrao ([a7166ec](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/a7166ec86235167d8c0804d84640588585bfc54c))
- **Statistics:** correçao botao naoAvaliados, filtro iniciar com data atual e de uma semana, tabela apenas apresentar valores que estao no grafico ([974acb4](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/974acb43414d96e6489c589bca22aff300e9b3bc))
- **Statistics:** correçao botao naoAvaliados, filtro iniciar com data atual e de uma semana, tabela apenas apresentar valores que estao no grafico ([27379a4](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/27379a49b87ba2f6526ecb39b265b9fb123cde57))
- **Statistics:** correçao botao naoAvaliados, filtro iniciar com data atual e de uma semana, tabela apenas apresentar valores que estao no grafico ([d438da1](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/d438da13df14336225b7672a56601d18d110c069))
- **Statistics:** Melhorias do codigo ([6d15b3a](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/6d15b3a491179d6c85e1ae47f018d4641c6dacaf))
- **whatsapp:** corrige whatsapp ([9a3d0c7](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/9a3d0c7229b3bd2a05de3db5048473d0a4af8ec2))
- **whatsapp:** corrige whatsapp ([b2d87e2](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/b2d87e28ad2da2fa1db0ee0b8f3e111b2818d2a1))
- adiciona identificador do nome da conexao do contato ([0aa5565](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/0aa55653366973218973f52966c8787b6ca81d11))

## [0.2.12](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.11...v0.2.12) (2021-01-27)

## [1.179.3-rc.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-mr-347.5...v1.179.3-rc.2) (2021-08-25)
## [1.179.3-mr-361.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-mr-361.0...v1.179.3-mr-361.1) (2021-08-25)
### Bug Fixes

- **contact:** adiciona referencia de contact->data ao payload ([7f635fa](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/7f635faaa0c43e4dd0a710cf901d49858fc49fa8))
- **contact:** corrige inclusao de contato com numero de telefone ([e5c2da1](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/e5c2da1eb8759e1d8cb4183d331cac3187ba132d))
- **contact:** corrige referencia a prop ([f5ef209](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/f5ef209f0c31d6167c13ae609e34450e55c5e8b9))
- **shedule:** adiciona icone da respectiva conexao do contato ([b9ffb01](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/b9ffb01114d1f936c1999a4ab27d0caf3e6550f0))

## [0.2.11](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.9...v0.2.11) (2021-01-20)

### Bug Fixes

- **account:** novas regras da rotina de bloqueio de conta ([13289c0](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/13289c01a9aa1946cd3c99b7297e12de07c1c4af))
- **account:** novas regras da rotina de bloqueio de conta ([663710d](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/663710dfdeb95d4640a2fae2f8ee187c6bc35783))
- **contact:** corrige adicionar contato pelo v-card ([26ff70c](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/26ff70c4436802a4a6ec4b4e6edbfdb21723f4c5))
- **webhook:** corrige parse do numero do contato com o comando webhook ([0774f81](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/0774f817687673c10fda5e2a0e649532ea15fed5))

### Features

- adiciona execução sequencial de regras no bot ([9c3d150](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/9c3d15076a55bec1b66eb1f988492ee0a25532bb))
- adiciona execução sequencial de regras no bot ([fd68b3b](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/fd68b3b0e72b93bb6f4022158661a7cfce2c6352))

## [0.2.9](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.8...v0.2.9) (2020-12-30)

### Bug Fixes

- corrige auto fetch ao alterar filtros no historico de chamados ([0463ca7](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/0463ca754eda783da8f38e7b67746221cd2407ed))
- corrige ticket history modal ([95f8b77](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/95f8b777730c35049a924569787c30d58d66f1b3))

## [0.2.8](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.94.0...v0.2.8) (2020-12-21)

### Bug Fixes

- **now:** corrige contatos não saindo apos fechar chamado ([1a20698](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/1a2069849b57f8bb5fca7712a37b83d1fb6378e4))
- **whatsapp:** corrige envio de midia ([2b0a1a6](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/2b0a1a6a80e2b63f40542a8187985de58371a106))

### Features

- add useFetchOneRequestLocal ([00b9df5](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/00b9df5a1add851e14605280829c96d628ea6c3e))
- add useFetchOneRequestLocal to contact requests ([92e6a56](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/92e6a563ce9ca2638c79fa25270749200013e8e9))
- permissão para visualizar my plan ([1ca124a](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/1ca124a733886ad3651f49f97524daa4e9332d8b))
- **whatsapp:** user agent update ([092d6b2](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/092d6b24d4f1c23ddf9e77eecd2830c2205f5134))

### Reverts

- Revert "Merge branch 'feature/newMessageTypes' into 'dev'" ([1bb5bb6](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/1bb5bb69f4b2424f56b547b5f0032d7f036b377c))

# [1.94.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/4c5d1bb298ffc8f030e76ee7e8049342eef35fa6...v1.94.0) (2020-09-30)

### Features

- **whatsapp:** concurrency queue config ([4c5d1bb](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/4c5d1bb298ffc8f030e76ee7e8049342eef35fa6))

## [0.2.13](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.12...v0.2.13) (2021-01-28)

**Note:** Version bump only for package digisac

## [0.2.12](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.11...v0.2.12) (2021-01-27)

### Bug Fixes

- **contact:** adiciona referencia de contact->data ao payload ([7f635fa](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/7f635faaa0c43e4dd0a710cf901d49858fc49fa8))
- **contact:** corrige inclusao de contato com numero de telefone ([e5c2da1](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/e5c2da1eb8759e1d8cb4183d331cac3187ba132d))
- **contact:** corrige referencia a prop ([f5ef209](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/f5ef209f0c31d6167c13ae609e34450e55c5e8b9))

## [0.2.11](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.9...v0.2.11) (2021-01-20)

### Bug Fixes

- **contact:** corrige adicionar contato pelo v-card ([26ff70c](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/26ff70c4436802a4a6ec4b4e6edbfdb21723f4c5))
- **webhook:** corrige parse do numero do contato com o comando webhook ([0774f81](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/0774f817687673c10fda5e2a0e649532ea15fed5))
- corrige auto fetch ao alterar filtros no historico de chamados ([0463ca7](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/0463ca754eda783da8f38e7b67746221cd2407ed))
- corrige ticket history modal ([95f8b77](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/95f8b777730c35049a924569787c30d58d66f1b3))
- **now:** corrige contatos não saindo apos fechar chamado ([1a20698](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/1a2069849b57f8bb5fca7712a37b83d1fb6378e4))

### Features

- permissão para visualizar my plan ([1ca124a](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/1ca124a733886ad3651f49f97524daa4e9332d8b))

## [0.2.10](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.9...v0.2.10) (2021-01-20)

### Bug Fixes

- **contact:** corrige adicionar contato pelo v-card ([26ff70c](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/26ff70c4436802a4a6ec4b4e6edbfdb21723f4c5))
- **webhook:** corrige parse do numero do contato com o comando webhook ([0774f81](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/0774f817687673c10fda5e2a0e649532ea15fed5))
- corrige auto fetch ao alterar filtros no historico de chamados ([0463ca7](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/0463ca754eda783da8f38e7b67746221cd2407ed))
- corrige ticket history modal ([95f8b77](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/95f8b777730c35049a924569787c30d58d66f1b3))
- **now:** corrige contatos não saindo apos fechar chamado ([1a20698](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/1a2069849b57f8bb5fca7712a37b83d1fb6378e4))

### Features

- permissão para visualizar my plan ([1ca124a](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/1ca124a733886ad3651f49f97524daa4e9332d8b))

## [0.2.8](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.7...v0.2.8) (2020-12-21)

**Note:** Version bump only for package digisac

## [0.2.7](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.6...v0.2.7) (2020-12-14)

**Note:** Version bump only for package digisac

## [0.2.6](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.5...v0.2.6) (2020-12-14)

# **Note:** Version bump only for package digisac

### Bug Fixes

- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))
  > > > > > > > master

## [0.2.3](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.2.2...v0.2.3) (2020-10-19)

**Note:** Version bump only for package digisac

## 0.2.1 (2020-10-15)

**Note:** Version bump only for package digisac

# 0.2.0 (2020-10-15)

**Note:** Version bump only for package digisac

# 0.1.0 (2020-10-15)

**Note:** Version bump only for package digisac

## [0.0.2](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v0.0.1...v0.0.2) (2020-10-13)

**Note:** Version bump only for package digisac

## [0.0.1](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.94.0...v0.0.1) (2020-10-13)

### Bug Fixes

- **evaluation:** varias correções e melhorias ([80b8af3](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/80b8af3a66f90a54f9610fa97255aec4ac78db58))
- corrige datetime ([4420cac](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/4420cac3fdddbc035055e0daa525352cc44eb1b5))
- corrige perda de estado da tela de empresa ao sair e volta da pagina ([9ad6d29](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/9ad6d298c9cf9fb4c27ef1b62918ced4b9bc655a))
- digisac.chat domain ([4d37f2b](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/4d37f2b6f132ce712af68884f2bc3463aeb3f3d6))
- digisac.chat domain ([802bef5](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/802bef53cb3a42e37aeab2c05d0bc138a8be12b3))

## [1.79.1-schedule.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.80.1-schedule.0...v1.79.1-schedule.0) (2020-08-31)

## [1.75.1-schedule.2](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.77.1-schedule.1...v1.75.1-schedule.2) (2020-08-31)

### Bug Fixes

- **version:** corrigido numero da versao ([38c69ae](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/38c69aec36db63bd58bb4afc09e8e394a04ce6f8))

### Features

- **wabaCampaign:** corrige erros ([40b615a](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/40b615aa39b7932b2d18585ee601255cc4b75a1d))

## [1.75.1-schedule.1](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.77.1-schedule.0...v1.75.1-schedule.1) (2020-08-25)

### Features

- **whatsappBusiness:** adiciona mensagem hsm na campanha ([e847755](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/e8477554e86183d56e2a54d12cfc3b3ec4677069))
- **whatsappBusiness:** remove logs ([79b3d33](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/79b3d33dd94256ae1dda9b597dfec82bce9eeabd))
- **WhatsappBusiness:** adiciona permissao pra enviar antes de 24 horas ([c35dd2a](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/c35dd2a85c82fd74f0dcde869e8d978acf222e32))

## [1.75.1-schedule.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.73.1-schedule.0...v1.75.1-schedule.0) (2020-08-21)

### Bug Fixes

- corrige build-image.sh latest tag ([190cf3b](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/190cf3b524fff721926043ca0763da8386b209e9))

## [1.75.1-week33.1](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.75.1-week33.0...v1.75.1-week33.1) (2020-08-18)

## [1.75.1-week33.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.70.1-week33.0...v1.75.1-week33.0) (2020-08-18)

## [1.71.1-week33.2](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.68.0...v1.71.1-week33.2) (2020-08-18)

### Features

- **whatsappBusiness:** escurece logo do waba para diferenciar ([df48388](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/df48388ebfd973b1a2bc392ec3fb15681d5d3881))
- **whatsappBusiness:** importa react countdown ([0df14f7](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/0df14f76cb9d53020ae7e7fb55594cb92c1a1b71))
- **whatsappBusiness:** troca coutdown pelo component ([107b02c](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/107b02c2857662f4c3cec45b6e36334303d28432))
- **whatsppBusiness:** adiciona react count-down ([caaffca](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/caaffca47ea3aaeb0bd9833084fa9e2da2365112))

## [1.69.1-week33.2](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.64.0...v1.69.1-week33.2) (2020-08-13)

### Features

- **whatsappBusiness:** adiciona contador da janela de hsm ([50120de](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/50120de4559cda703651dbf249f87902969f73f7))

## [1.70.1-statistics.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.62.0...v1.70.1-statistics.0) (2020-08-12)

## [1.69.1-week33.1](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.61.0...v1.69.1-week33.1) (2020-08-12)

## [1.66.1-week31.4](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.57.1-week31.0...v1.66.1-week31.4) (2020-08-11)

## [1.69.1-statistics.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.69.1-week33.0...v1.69.1-statistics.0) (2020-08-11)

### Features

- **whatsappBusiness:** adicinoa template no exibir hsm ([456aa3b](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/456aa3b6f08f1cee3249f37a3d117831822b1b2e))
- **whatsappBusiness:** adiciona validação padrão nos parametros do hsm ([55489f1](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/55489f110ee247454d49483279e6ff0ad41f4fbc))
- **whatsappBusiness:** apiUrl como undefined ([231e52b](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/231e52b8e046b3307daaba6d88ec622bf7105b91))
- **whatsappBusiness:** corrige hsm index name ([0b6e268](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/0b6e268b579c56f581e1fe3d9a8f42cc3be2595d))
- **whatsappBusiness:** corrige permissoes no front ([e2c7bd8](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/e2c7bd81317000a72c0978e8a21cba68141b0808))
- **whatsappBusiness:** corrige texto do botão de criar hsm ([fc254a8](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/fc254a820e0b03893356f5df70aa6dc7628a1216))
- **whatsappBusiness:** desabilita edição de campos hsm ([b641ec6](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/b641ec69623db14ffc395bfff3a5343c283e095a))
- **whatsappBusiness:** envia hsm sem parametro ([1cb3eea](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/1cb3eea23ff163123cfbc952d8a5c54506782236))
- **whatsappBusiness:** mostra cadastro de HSM apenas quando possível cadastrar conexao ([eaed906](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/eaed90601be65a3cb9debc74c2dbed76a8a86b82))
- **whatsappBusiness:** remove logs ([d4064e4](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/d4064e470c16568245aa598a8f8644bd0fb4633e))
- **whatsppBusiness:** adiciona permissoes hsm ([064cba9](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/064cba95ce4dde0278c402438108225a6a74f5c7))
- **whatsppBusiness:** exibe envio de hsm apenas após atendente possuir o chamado ([60156f9](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/60156f9e8e06beed02ad1735ed9801cb31448cf3))

## [1.69.1-week33.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.59.1-statistics.0...v1.69.1-week33.0) (2020-08-10)

### Features

- **whatsappBusiness:** icone hsm na esquerda, e aparecendo apenas para business ([a07b28f](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/a07b28f7ef30c7fe7e8005df00d1755ead0cc809))
- **whatsappBusiness:** limpa apenas os campos digitaveis ([29b23af](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/29b23afa68c639f764f5ad3ed38979dcea5bcdf6))

## [1.66.1-statistics.2](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.57.1-statistics.3...v1.66.1-statistics.2) (2020-08-06)

### Bug Fixes

- **bot:** corrige nps ([684c8e8](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/684c8e8bf56aa9293eea10197592b98228a00a0a))

## [1.66.1-week31.3](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.59.0...v1.66.1-week31.3) (2020-08-06)

### Features

- **whatsappBusiness:** adiciona template e validações no hsm ([a613c8e](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/a613c8eff95d82331e86de85acafef56124cc5d1))
- **whatsappBusiness:** adiciona verificacoes no envio de hsm ([755c527](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/755c527fbe5a03badfe23cfb69f80be15827e8b1))
- **whatsappBusiness:** ajusta exclusao de hsm ([640fbb6](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/640fbb61841967c3fd1752542caed1b00425d345))
- **whatsappBusiness:** ajusta icone do hsm ([30ff951](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/30ff9516ac4c1faaf9b777f375cf8e28886753e1))
- **whatsappBusiness:** ajusta view do hsm ([b8a31eb](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/b8a31eba25905c863bc1eb6df8c854a178f44d38))
- **whatsappBusiness:** cadastro de hsm usa multiinput ([e6ae8d3](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/e6ae8d3c6dd8dac8712dfa3f48c881cddcaf73ad))
- **whatsappBusiness:** coloca business como ativo ([e0c69bd](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/e0c69bde707c05fe56220bb074944228fb053778))
- **whatsappBusiness:** conexão hsm recebe apenas chave e token agora ([836a568](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/836a568ad63b0d70adf3268a0e500f17091e879e))
- **whatsappBusiness:** corrige trava de 24 horas ([343ddeb](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/343ddebba7dfa8a4119239df63680feac914174f))
- **whatsappBusiness:** esconde botões não utilizados pelo business ([97ee59a](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/97ee59acceb15d1d88e83262b3a003fdc83fefc8))
- **whatsappBusiness:** usa validation correta ([dc46b91](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/dc46b91ebb04987ad1e9f86ee44255ad6e981feb))

## [1.66.1-statistics.1](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.57.1-statistics.0...v1.66.1-statistics.1) (2020-08-04)

## [1.66.1-week31.2](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.57.1-week29.0...v1.66.1-week31.2) (2020-08-04)

## [1.66.1-week29.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.66.1-week31.1...v1.66.1-week29.0) (2020-08-04)

## [1.66.1-week31.1](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.57.0...v1.66.1-week31.1) (2020-08-03)

## [1.66.1-week32.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.66.1-week31.0...v1.66.1-week32.0) (2020-08-03)

### Bug Fixes

- adiciona imports faltando ([b5f0c56](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/b5f0c56d346dcbc4fa82e8537419719a457cb3e1))
- corrige campo de time na empresa quando digitado ([d5cfb83](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/d5cfb830e7c8a77c029059fed30544b740eff69f))
- corrige timezone na exportação de ticket pdf ([12f75ff](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/12f75ffab5750589c547acb7f0511e4901ec3a77))

## [1.66.1-week31.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.66.1-statistics.0...v1.66.1-week31.0) (2020-07-31)

### Bug Fixes

> > > > > > > fix/telas-houston-i18n

#

### Performance Improvements

- melhora regex em build-image.sh ([a0c8967](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/a0c89674bfa6a8ad11651d8cd8f32f130e945f32))
  > > > > > > > master

## [1.179.12](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.12) (2021-09-14)

### Performance Improvements

- melhora regex em build-image.sh ([a0c8967](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/a0c89674bfa6a8ad11651d8cd8f32f130e945f32))

## [1.179.11](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.8...v1.179.11) (2021-09-10)

**Note:** Version bump only for package @digisac/root

## [1.179.10](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.8...v1.179.10) (2021-09-03)

**Note:** Version bump only for package @digisac/root

## [1.179.8](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.7...v1.179.8) (2021-09-02)

**Note:** Version bump only for package @digisac/root




## [1.178.21-mr-347.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-mr-347.2...v1.178.21-mr-347.3) (2021-08-19)


## [1.179.10](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.8...v1.179.10) (2021-09-03)

## [1.178.21-mr-347.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-mr-347.1...v1.178.21-mr-347.2) (2021-08-19)

## [1.179.8](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.7...v1.179.8) (2021-09-02)

**Note:** Version bump only for package @digisac/root

## [1.179.7](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.5...v1.179.7) (2021-09-02)


## [1.178.21-mr-347.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-mr-347.0...v1.178.21-mr-347.1) (2021-08-18)



## [1.178.21-mr-347.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.22...v1.178.21-mr-347.0) (2021-08-18)
## [1.179.3-mr-361.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.3-mr-361.0) (2021-08-25)

**Note:** Version bump only for package @digisac/root





## [1.179.3-rc.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.0...v1.179.3-rc.1) (2021-08-25)


**Note:** Version bump only for package @digisac/root

## [1.179.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.5) (2021-08-31)
### Bug Fixes

* **roles:** corrige falta de lib em RolesForm ([295cb86](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/295cb86da12503cf94cee69136ee90f7babc427b))





## [1.179.3-rc.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-rc.3...v1.179.3-rc.0) (2021-08-24)



## [1.179.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-rc.2...v1.179.1) (2021-08-22)


### Bug Fixes

## [1.179.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.4) (2021-08-27)
* **permissions:** [MN-2444] Correção na query de permissões ([a7b6afa](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/a7b6afa0504ec605b6ee83a37b0f43e80701c8f3))



# [1.179.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-mr-352.0...v1.179.0) (2021-08-20)



## [1.179.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.3) (2021-08-25)
## [1.178.22](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21...v1.178.22) (2021-08-17)



## [1.178.21](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-rc.1...v1.178.21) (2021-08-17)



## [1.179.3-mr-361.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-mr-361.0...v1.179.3-mr-361.1) (2021-08-25)
## [1.178.20-mr-318.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-rc.21...v1.178.20-mr-318.3) (2021-08-14)



## [1.178.20-mr-318.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.20-mr-318.1...v1.178.20-mr-318.2) (2021-08-12)



## [1.179.3-mr-361.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.3-mr-361.0) (2021-08-25)
## [1.178.20-mr-318.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-rc.16...v1.178.20-mr-318.1) (2021-08-12)



## [1.178.20-mr-318.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-rc.13...v1.178.20-mr-318.0) (2021-08-09)

## [1.179.7](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.5...v1.179.7) (2021-09-02)

**Note:** Version bump only for package @digisac/root

## [1.179.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.5...v1.179.6) (2021-09-01)

**Note:** Version bump only for package @digisac/root

## [1.179.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.5) (2021-08-31)

**Note:** Version bump only for package @digisac/root

## [1.179.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.2) (2021-08-23)
## [1.179.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.4) (2021-08-27)

**Note:** Version bump only for package @digisac/root

## [1.179.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.3) (2021-08-25)

**Note:** Version bump only for package @digisac/root

## [1.179.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.0...v1.179.1) (2021-08-22)

### Bug Fixes

- **permissions:** [MN-2444] Correção na query de permissões ([a7b6afa](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/a7b6afa0504ec605b6ee83a37b0f43e80701c8f3))

# [1.179.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.22-mr-276.0...v1.179.0) (2021-08-20)

## [1.178.22](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21...v1.178.22) (2021-08-17)

## [1.179.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.2) (2021-08-23)

**Note:** Version bump only for package @digisac/root

## [1.161.1-mr-76.0](https://gitlab.mandeumzap.com.br/digisac/monorepo/compare/v1.161.0...v1.161.1-mr-76.0) (2021-04-14)

> > > > > > > master

### Features

- **socket:** não receber sockets de mensagens e chamados se estiver com outros atendentes ([4905e80](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/4905e80fc9439339c370102fc3db85422ca1b970))
- **socket:** otimização de socket de mensagens e contatos ([c4dde6d](https://gitlab.mandeumzap.com.br/digisac/monorepo/commit/c4dde6d1f242244d22daeed5206a1ab76db2901a))
