import appRootDir from 'app-root-dir'
import AssetsPlugin from 'assets-webpack-plugin'
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer'
import MiniCssExtractPlugin from 'mini-css-extract-plugin'
import nodeExternals from 'webpack-node-externals'
// import nodeExternals from 'webpack-node-externals'
// import HardSourceWebpackPlugin from 'hard-source-webpack-plugin'
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin'
import path from 'path'
// import TerserPlugin from 'terser-webpack-plugin'
import ReactRefreshWebpackPlugin from '@pmmmwh/react-refresh-webpack-plugin'
import webpack from 'webpack'
import { log } from '../utils'
import { ifElse } from '../utils/logic'
import { clean } from '../utils/arrays'
import withServiceWorker from './withServiceWorker'
import config from '../../config'
import SpeedMeasurePlugin from 'speed-measure-webpack-plugin'
// import SentryPlugin from '@sentry/webpack-plugin'

export default function (buildOptions) {
  const { target, optimize = false } = buildOptions

  const isProd = optimize
  const isDev = !isProd
  const isClient = target === 'client'
  const isServer = target === 'server'
  const isNode = !isClient

  const ifDev = ifElse(isDev)
  const ifProd = ifElse(isProd)
  const ifNode = ifElse(isNode)
  const ifClient = ifElse(isClient)
  const ifDevClient = ifElse(isDev && isClient)
  const ifProdClient = ifElse(isProd && isClient)

  log({
    level: 'info',
    title: 'Webpack',
    message: `Creating ${isProd ? 'an optimised' : 'a development'} bundle configuration for the "${target}"`,
  })

  const bundleConfig = isServer || isClient ? config(['bundles', target]) : config(['additionalNodeBundles', target])

  if (!bundleConfig) {
    throw new Error('No bundle configuration exists for target:', target)
  }

  const postCssLoaderOptions = {
    postcssOptions: {
      plugins: [
        require('tailwindcss'),
        require('postcss-import'),
        require('postcss-preset-env'),
        require('postcss-simple-vars'),
        require('autoprefixer'),
      ],
    },
  }

  let webpackConfig = {
    mode: ifDev('development', 'production'),
    context: appRootDir.get(),
    entry: {
      index: clean([
        ifClient('regenerator-runtime/runtime'),
        ifDevClient(
          () =>
            `webpack-hot-middleware/client?reload=true&path=http://${config('host')}:${config(
              'clientDevServerPort',
            )}/__webpack_hmr`,
        ),
        path.resolve(appRootDir.get(), bundleConfig.srcEntryFile),
      ]),
    },

    output: {
      library: 'Digisac',
      path: path.resolve(appRootDir.get(), bundleConfig.outputPath),
      filename: ifProdClient('[name]-[contenthash].js', '[name].js'),
      chunkFilename: '[name]-[contenthash].js',
      libraryTarget: ifNode('commonjs2', 'var'),
      publicPath: ifDev(
        `http://${config('host')}:${config('clientDevServerPort')}${
          isServer ? config('bundles.server.webPath') : config('bundles.client.webPath')
        }`,
        bundleConfig.webPath,
      ),
      hotUpdateChunkFilename: '[id].[fullhash].hot-update.js',
      hotUpdateMainFilename: '[runtime].[fullhash].hot-update.json',
    },

    target: isClient ? 'web' : 'node',

    ...(isServer && {
      externalsPresets: { node: true }, // <-- here
      externals: [nodeExternals()], // <-- and here
    }),
    cache: {
      type: 'memory',
      cacheUnaffected: true,
    },
    // unsafeCache: true,
    experiments: {
      cacheUnaffected: true,
      backCompat: false,
    },

    node: {
      __dirname: true,
      __filename: true,
    },

    devtool: ifElse(isNode || isDev || config('includeSourceMapsForOptimisedClientBundle'))(
      'cheap-module-source-map',
      'hidden-source-map',
    ),

    performance: ifProdClient({ hints: 'warning' }, false),

    optimization: {
      minimize: ifProdClient(true, false),
      minimizer: ifProdClient([
        // new TerserPlugin({
        //   cache: true,
        //   parallel: true,
        //   sourceMap: config('includeSourceMapsForOptimisedClientBundle'),
        //   terserOptions: {
        //     warnings: false,
        //     ie8: false,
        //   },
        // }),
        new CssMinimizerPlugin(),
      ]),
    },

    resolve: {
      extensions: config('bundleSrcTypes').map((ext) => `.${ext}`),
      alias: {
        modernizr$: path.resolve(appRootDir.get(), './.modernizrrc'),
        // 'react-dom': '@hot-loader/react-dom',
      },
    },

    // externals: clean([
    //   ifNode(() => nodeExternals({
    //     allowlist: clean(['source-map-support/register']).concat(
    //       config('nodeExternalsFileTypeAllowlist') || [],
    //     ),
    //   })),
    // ]),

    plugins: clean([
      // ifNode(
      //   () => new webpack.BannerPlugin({
      //     banner: 'require("source-map-support").install();',
      //     raw: true,
      //     entryOnly: false,
      //   }),
      // ),

      new webpack.EnvironmentPlugin({
        NODE_ENV: isProd ? 'production' : 'development',
        BUILD_FLAG_IS_CLIENT: JSON.stringify(isClient),
        BUILD_FLAG_IS_SERVER: JSON.stringify(isServer),
        BUILD_FLAG_IS_NODE: JSON.stringify(isNode),
        BUILD_FLAG_IS_DEV: JSON.stringify(isDev),
        BUILD_FLAG_DISABLE_SSR: JSON.stringify(config('disableSSR')),
        BUILD_FLAG_DOMAIN: config('domain'),
      }),

      /* ifProd(() => new SentryPlugin({
          release: config('version'),
          validate: true,
          include: "./dist",
          rewrite: true,
          ignore: ["sw.js"]
        })
      ), */

      ifClient(
        () =>
          new AssetsPlugin({
            filename: config('bundleAssetsFileName'),
            path: path.resolve(appRootDir.get(), bundleConfig.outputPath),
          }),
      ),

      // ifDev(() => new webpack.NoEmitOnErrorsPlugin()),
      ifDevClient(() => new webpack.HotModuleReplacementPlugin()),

      ifDevClient(() => new ReactRefreshWebpackPlugin()),

      ifClient(
        () =>
          new MiniCssExtractPlugin({
            filename: '[name]-[contenthash].css',
            chunkFilename: '[name]-[contenthash].css',
          }),
      ),
      //ifElse(isServer)(() => new MiniCssExtractPlugin()),

      // ifDevClient(() => new BundleAnalyzerPlugin({ analyzerMode: 'static' })),
      ifDevClient(() => new SpeedMeasurePlugin()),

      // new HardSourceWebpackPlugin({
      //   configHash: () => `${target}-${isProd ? 'production' : 'development'}`,
      // }),
    ]),

    module: {
      unsafeCache: true,
      strictExportPresence: true,
      rules: [
        {
          oneOf: clean([
            {
              test: /\.(js|jsx|mjs|ts|tsx)$/,
              // `.swcrc` can be used to configure swc
              loader: 'swc-loader',
              options: {
                jsc: {
                  transform: {
                    legacyDecorator: true,
                    decoratorMetadata: true,
                    react: {
                      development: isDev,
                      refresh: isDev && isClient,
                    },
                  },
                },
              },
              include: clean([
                ...bundleConfig.srcPaths.map((srcPath) => path.resolve(appRootDir.get(), srcPath)),
                ifProdClient(path.resolve(appRootDir.get(), 'src/html')),
                path.resolve(__dirname, '../../lib'),
                path.resolve(__dirname, '../../src'),
              ]),
            },

            ifElse(isClient)({
              test: /(\.module.scss|\.module.css)$/,
              exclude: /node_modules/,
              use: [
                //ifElse(isProd)(MiniCssExtractPlugin.loader, 'style-loader'),
                MiniCssExtractPlugin.loader,
                {
                  loader: 'css-loader',
                  options: {
                    importLoaders: 2,
                    modules: {
                      localIdentName: ifDev('[name]_[local]_[hash:base64:5]', '[hash:base64:10]'),
                    },
                  },
                },
                { loader: 'postcss-loader', options: postCssLoaderOptions },
                { loader: 'sass-loader' },
              ],
            }),

            ifElse(isClient)({
              test: /(\.scss|\.sass)$/,
              exclude: [/\.module\.(scss|sass)$/, /node_modules/],
              use: [
                //ifElse(isProd)(MiniCssExtractPlugin.loader, 'style-loader'),
                MiniCssExtractPlugin.loader,
                {
                  loader: 'css-loader',
                  options: {
                    importLoaders: 2,
                    // modules: {
                    //   localIdentName: ifDev('[name]_[local]_[hash:base64:5]', '[hash:base64:10]'),
                    // },
                  },
                },
                { loader: 'postcss-loader', options: postCssLoaderOptions },
                { loader: 'sass-loader' },
              ],
            }),

            ifElse(isClient)({
              test: /\.css$/,
              exclude: [/\.module\.css$/, /node_modules/],
              use: [
                ifElse(isProd)(MiniCssExtractPlugin.loader, 'style-loader'),
                {
                  loader: 'css-loader',
                  options: {
                    importLoaders: 1,
                    // modules: false,
                  },
                },
                { loader: 'postcss-loader', options: postCssLoaderOptions },
              ],
            }),

            {
              test: /\.css$/,
              include: /node_modules/,
              use: [MiniCssExtractPlugin.loader, 'css-loader'],
            },

            ifElse(isServer)({
              test: /(\.scss|\.sass|\.css)$/,
              exclude: /node_modules/,
              use: [
                {
                  loader: 'css-loader',
                  options: {
                    importLoaders: 2,
                    // modules: {
                    //   localIdentName: ifDev('[name]_[local]_[hash:base64:5]', '[hash:base64:10]'),
                    // },
                  },
                },
                { loader: 'postcss-loader', options: postCssLoaderOptions },
                { loader: 'sass-loader' },
              ],
            }),

            {
              test: /\.(bmp|gif|jpeg|jpg|svg|png)$/,
              type: 'asset',
              parser: {
                dataUrlCondition: {
                  maxSize: 10000,
                },
              },
              generator: {
                filename: 'static/[hash][ext][query]',
              },
            },

            ifElse(isClient || isServer)(() => ({
              test: /\.(woff|woff2|eot|ttf|otf|svg|mp3)$/,
              type: 'asset/resource',
              generator: {
                filename: 'static/[hash][ext][query]',
                publicPath: isDev
                  ? `http://${config('host')}:${config('clientDevServerPort')}${config('bundles.client.webPath')}`
                  : `${config('bundles.client.webPath')}`,
              },
            })),
          ]),
        },
      ],
    },
  }

  if (isProd && isClient && config('serviceWorker.enabled')) {
    webpackConfig = withServiceWorker(webpackConfig, bundleConfig)
  }

  return config('plugins.webpackConfig')(webpackConfig, buildOptions)
}
