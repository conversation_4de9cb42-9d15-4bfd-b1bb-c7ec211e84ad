import { TicketTopic } from '../app/types/TicketTopic'
import { getAxios } from '../app/utils/apiResource'
import { type Api, type BaseFilters, type PaginatedApiResponse } from './types'

interface TicketTopicsData {
  name: string
}

interface TicketTopicsFilters extends BaseFilters {
  archivedAt?: 'archived' | 'unarchived'
  name?: string
}

interface TicketTopicsModel extends TicketTopic {}

interface TicketTopicsApi
  extends Api<PaginatedApiResponse<TicketTopicsModel>, TicketTopicsData, TicketTopicsFilters, TicketTopicsModel> {
  archive(id: string, archivedAt: boolean | null): Promise<TicketTopicsModel>
}

const buildQuery = ({ paginate = true, ...filters }: TicketTopicsFilters) => ({
  query: JSON.stringify({
    where: {
      name: filters.name ? { $iLike: `%${filters.name}%` } : undefined,

      archivedAt: filters.archivedAt
        ? { archived: { $ne: null }, unarchived: { $eq: null } }[filters.archivedAt]
        : undefined,
    },
    order: [[filters.sort ?? 'name', filters.order ?? 'ASC']],
    page: paginate ? (filters.page ?? 1) : undefined,
    perPage: paginate ? (filters.perPage ?? 15) : undefined,
    paginate: paginate ?? true,
    customInclude: filters.customInclude ?? [],
  }),
})

const endpoint = '/ticket-topics'

const ticketTopicsApi: TicketTopicsApi = {
  async create(data: TicketTopicsData) {
    const res = await getAxios().post<TicketTopic>(endpoint, data)
    return res.data
  },
  async delete(id: string) {
    await getAxios().delete<void>(`${endpoint}/${id}`)
  },
  async getAll(filters: TicketTopicsFilters) {
    const res = await getAxios().get<PaginatedApiResponse<TicketTopic>>(`${endpoint}`, {
      params: buildQuery(filters),
    })
    return res.data
  },
  async getById(id: string) {
    const res = await getAxios().get<TicketTopicsModel>(`${endpoint}/${id}`)
    return res.data
  },
  async update(id: string, data: TicketTopicsData) {
    const res = await getAxios().put<TicketTopic>(`${endpoint}/${id}`, data)
    return res.data
  },
  async archive(id: string, archivedAt: boolean | null) {
    const res = await getAxios().post<TicketTopicsModel>(`${endpoint}/${id}/archive`, { archive: archivedAt })
    return res.data
  },
}

export { ticketTopicsApi as default, endpoint as ticketTopicsQueryKey, type TicketTopicsData, type TicketTopicsFilters }
