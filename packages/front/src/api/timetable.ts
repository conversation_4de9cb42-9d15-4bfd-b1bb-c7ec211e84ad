import { getAxios } from '../app/utils/apiResource'
import { type Api, type BaseFilters, type PaginatedApiResponse } from './types'

type WeekDay = 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun'

interface WorkPlan {
  start: string
  end: string
  weekDays: WeekDay[]
}
interface TimetableData {
  name: string
  id: string
  workPlan: WorkPlan[]
}

interface TimetableFilters extends BaseFilters, Record<string, any> {}

interface TimetableModel extends TimetableData {}

interface TimetableApi
  extends Api<PaginatedApiResponse<TimetableModel>, TimetableData, TimetableFilters, TimetableModel> {}

const buildQuery = ({ paginate = true, ...filters }: TimetableFilters) => ({
  query: JSON.stringify({
    where: {
      name: filters.name ? { $iLike: `%${filters.name}%` } : undefined,
    },
    order: [[filters.sort ?? 'name', filters.order ?? 'ASC']],
    page: paginate ? (filters.page ?? 1) : undefined,
    perPage: paginate ? (filters.perPage ?? 15) : undefined,
    paginate: paginate ?? true,
    customInclude: filters.customInclude ?? [],
  }),
})

const endpoint = '/timetable'

const timetableApi: TimetableApi = {
  async create(data: TimetableData) {
    const res = await getAxios().post<TimetableModel>(endpoint, data)
    return res.data
  },
  async delete(id: string) {
    await getAxios().delete<void>(`${endpoint}/${id}`)
  },
  async getAll(filters: TimetableFilters) {
    const res = await getAxios().get<PaginatedApiResponse<TimetableModel>>(`${endpoint}`, {
      params: buildQuery(filters),
    })
    return res.data
  },
  async getById(id: string) {
    const res = await getAxios().get<TimetableModel>(`${endpoint}/${id}`)
    return res.data
  },
  async update(id: string, data: TimetableData) {
    const res = await getAxios().put<TimetableModel>(`${endpoint}/${id}`, data)
    return res.data
  },
}
export { timetableApi as default, endpoint as timetableQueryKey, type TimetableData, type TimetableFilters }
