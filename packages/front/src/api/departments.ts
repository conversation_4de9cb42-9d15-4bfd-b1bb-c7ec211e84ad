import { Department } from '../app/types/Department'
import { getAxios } from '../app/utils/apiResource'
import { type Api, type BaseFilters, type PaginatedApiResponse } from './types'

interface DepartmentsData extends Omit<Department, 'departments'> {
  departments: string[]
}

interface DepartmentsFilters extends BaseFilters {
  archivedAt?: 'archived' | 'unarchived'
  name?: string
}

interface DepartmentsModel extends Department {}

interface DepartmentsApi
  extends Api<PaginatedApiResponse<DepartmentsModel>, DepartmentsData, DepartmentsFilters, DepartmentsModel> {
  archive(id: string, archivedAt: boolean | null): Promise<DepartmentsModel>
}

const buildQuery = ({ paginate = true, ...filters }: DepartmentsFilters) => ({
  query: JSON.stringify({
    where: {
      name: filters.name ? { $iLike: `%${filters.name}%` } : undefined,

      archivedAt: filters.archivedAt
        ? { archived: { $ne: null }, unarchived: { $eq: null } }[filters.archivedAt]
        : undefined,
    },
    order: [[filters.sort ?? 'name', filters.order ?? 'ASC']],
    page: paginate ? (filters.page ?? 1) : undefined,
    perPage: paginate ? (filters.perPage ?? 15) : undefined,
    paginate: paginate ?? true,
    customInclude: filters.customInclude ?? [],
  }),
})

const endpoint = '/departments'

const departmentsApi: DepartmentsApi = {
  async create(data: DepartmentsData) {
    const res = await getAxios().post<Department>(endpoint, data)
    return res.data
  },
  async delete(id: string) {
    await getAxios().delete<void>(`${endpoint}/${id}`)
  },
  async getAll(filters: DepartmentsFilters) {
    const res = await getAxios().get<PaginatedApiResponse<Department>>(`${endpoint}`, {
      params: buildQuery(filters),
    })
    return res.data
  },
  async getById(id: string) {
    const res = await getAxios().get<DepartmentsModel>(`${endpoint}/${id}`)
    return res.data
  },
  async update(id: string, data: DepartmentsData) {
    const res = await getAxios().put<Department>(`${endpoint}/${id}`, data)
    return res.data
  },
  async archive(id: string, archivedAt: boolean | null) {
    const res = await getAxios().post<DepartmentsModel>(`${endpoint}/${id}/archive`, { archive: archivedAt })
    return res.data
  },
}

export { departmentsApi as default, endpoint as departmentsQueryKey, type DepartmentsData, type DepartmentsFilters }
