import { Organization } from '../app/types/Organization'
import { getAxios } from '../app/utils/apiResource'
import type { Api, BaseFilters, PaginatedApiResponse } from './types'

interface OrganizationsData extends Organization {}

interface OrganizationsFilters extends BaseFilters {
  sort?: 'name'
  name?: string
}

interface OrganizationsModel extends Organization {}

interface OrganizationsApi
  extends Api<PaginatedApiResponse<OrganizationsModel>, OrganizationsData, OrganizationsFilters, OrganizationsModel> {}

const buildQuery = ({ paginate = true, ...filters }: OrganizationsFilters) => ({
  query: JSON.stringify({
    where: {
      name: filters.name ? { $iLike: `%${filters.name}%` } : undefined,
    },
    order: [[filters.sort ?? 'name', filters.order ?? 'ASC']],
    page: paginate ? (filters.page ?? 1) : undefined,
    perPage: paginate ? (filters.perPage ?? 15) : undefined,
    paginate: paginate ?? true,
  }),
})

const endpoint = '/organizations'

const organizationsApi: OrganizationsApi = {
  async create(data: OrganizationsData) {
    const res = await getAxios().post<Organization>(endpoint, data)
    return res.data
  },
  async delete(id: string) {
    await getAxios().delete<void>(`${endpoint}/${id}`)
  },
  async getAll(filters: OrganizationsFilters) {
    const res = await getAxios().get<PaginatedApiResponse<Organization>>(`${endpoint}`, {
      params: buildQuery(filters),
    })
    return res.data
  },
  async getById(id: string) {
    const res = await getAxios().get<OrganizationsModel>(`${endpoint}/${id}`)
    return res.data
  },
  async update(id: string, data: OrganizationsData) {
    const res = await getAxios().put<Organization>(`${endpoint}/${id}`, data)
    return res.data
  },
}

export {
  organizationsApi as default,
  endpoint as organizationsQueryKey,
  type OrganizationsData,
  type OrganizationsFilters,
  type OrganizationsModel,
}
