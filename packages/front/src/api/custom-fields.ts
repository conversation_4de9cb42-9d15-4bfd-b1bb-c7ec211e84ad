import { CustomField } from '../app/types/CustomFields'
import { getAxios } from '../app/utils/apiResource'
import { type Api, type BaseFilters, type PaginatedApiResponse } from './types'

interface CustomFieldsData extends Omit<CustomField, 'id' | 'createdAt' | 'updatedAt' | 'value' | 'fieldId'> {}

interface CustomFieldsFilters extends BaseFilters {
  name?: string
}

interface CustomFieldsModel extends CustomField {}

interface CustomFieldsApi
  extends Api<PaginatedApiResponse<CustomFieldsModel>, CustomFieldsData, CustomFieldsFilters, CustomFieldsModel> {}

const buildQuery = ({ paginate = true, ...filters }: CustomFieldsFilters) => ({
  query: JSON.stringify({
    where: {
      name: filters.name ? { $iLike: `%${filters.name}%` } : undefined,
    },
    order: [[filters.sort ?? 'name', filters.order ?? 'ASC']],
    page: paginate ? filters.page ?? 1 : undefined,
    perPage: paginate ? filters.perPage ?? 15 : undefined,
    paginate: paginate ?? true,
    customInclude: filters.customInclude ?? [],
  }),
})

const endpoint = '/custom-fields'

const customFieldsApi: CustomFieldsApi = {
  async create(data: CustomFieldsData) {
    const res = await getAxios().post<CustomField>(endpoint, data)
    return res.data
  },
  async delete(id: string) {
    await getAxios().delete<void>(`${endpoint}/${id}`)
  },
  async getAll(filters: CustomFieldsFilters) {
    const res = await getAxios().get<PaginatedApiResponse<CustomField>>(`${endpoint}`, {
      params: buildQuery(filters),
    })
    return res.data
  },
  async getById(id: string) {
    const res = await getAxios().get<CustomFieldsModel>(`${endpoint}/${id}`)
    return res.data
  },
  async update(id: string, data: CustomFieldsData) {
    const res = await getAxios().put<CustomField>(`${endpoint}/${id}`, data)
    return res.data
  },
}

export { customFieldsApi as default, endpoint as CustomFieldsQueryKey, type CustomFieldsData, type CustomFieldsFilters }
