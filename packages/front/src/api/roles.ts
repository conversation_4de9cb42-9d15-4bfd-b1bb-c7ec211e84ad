import { Role } from '../app/types/Role'
import { getAxios } from '../app/utils/apiResource'
import { type Api, type BaseFilters, type PaginatedApiResponse } from './types'

interface RolesData extends Pick<Role, 'displayName' | 'permissions'> {}
interface RolesFilters extends BaseFilters {
  sort?: string
  displayName?: string
  customInclude?: string[]
}

interface RolesModel extends Role {}
interface RolesListResponse extends PaginatedApiResponse<RolesModel> {}
interface RolesApi extends Api<RolesListResponse, RolesData, RolesFilters, RolesModel> {}

const buildSort = (filters: RolesFilters) => {
  const sortOptions = {
    displayName: ['displayName'],
    usersCount: ['usersCount'],
  }

  return [...sortOptions[filters.sort || 'displayName'], filters.order || 'ASC']
}

const buildQuery = ({ paginate = true, ...filters }: RolesFilters) => ({
  query: JSON.stringify({
    where: {
      displayName: filters.displayName ? { $iLike: `%${filters.displayName}%` } : undefined,
    },
    order: [buildSort(filters)],
    page: filters.page ? (filters.page ?? 1) : undefined,
    perPage: filters.perPage ? (filters.perPage ?? 15) : undefined,
    paginate: paginate ?? true,
    include: filters.include ?? [],
    customInclude: filters.customInclude ?? [],
  }),
})

const endpoint = '/roles'

const rolesApi: RolesApi = {
  async create(data: RolesData) {
    const res = await getAxios().post<Role>(endpoint, data)
    return res.data
  },
  async delete(id: string) {
    await getAxios().delete<void>(`${endpoint}/${id}`)
  },
  async getAll(filters: RolesFilters) {
    const res = await getAxios().get<PaginatedApiResponse<Role>>(`${endpoint}`, {
      params: buildQuery(filters),
    })
    return res.data
  },
  async getById(id: string, filters) {
    const res = await getAxios().get<RolesModel>(`${endpoint}/${id}`, {
      params: filters,
    })
    return res.data
  },
  async update(id: string, data: RolesData) {
    const res = await getAxios().put<Role>(`${endpoint}/${id}`, data)
    return res.data
  },
}

export {
  rolesApi as default,
  endpoint as rolesQueryKey,
  type RolesData,
  type RolesFilters,
  type RolesModel,
  type RolesListResponse,
}
