import { Bot } from '../app/types/Bot'
import { getAxios } from '../app/utils/apiResource'
import type { Api, BaseFilters, PaginatedApiResponse } from './types'

interface BotsData extends Bot {}

interface BotsFilters extends BaseFilters {
  sort?: 'name'
  name?: string
  tab: 'flow' | 'legacy'
}

interface BotsModel extends Bot {}

interface BotsApi extends Omit<Api<PaginatedApiResponse<BotsModel>, BotsData, BotsFilters, BotsModel>, 'update'> {}

const buildQuery = ({ paginate = true, ...filters }: BotsFilters) => ({
  query: JSON.stringify({
    where: {
      name: filters.name ? { $iLike: `%${filters.name}%` } : undefined,
      settings: {
        botCreatedVersion: filters.tab === 'legacy' ? { $or: [null, 'v1', 'v2'] } : 'v3',
      },
    },
    order: [[filters.sort ?? 'name', filters.order ?? 'ASC']],
    page: paginate ? filters.page ?? 1 : undefined,
    perPage: paginate ? filters.perPage ?? 15 : undefined,
    paginate: paginate ?? true,
  }),
})

const endpoint = '/bots'

const botsApi: BotsApi = {
  async delete(id: string) {
    await getAxios().delete<void>(`${endpoint}/${id}`)
  },
  async getAll(filters: BotsFilters) {
    const res = await getAxios().get<PaginatedApiResponse<Bot>>(`${endpoint}`, {
      params: buildQuery(filters),
    })
    return res.data
  },
  async getById(id: string) {
    const res = await getAxios().get<Bot>(`${endpoint}/${id}`)
    return res.data
  },
  async create(data: BotsData) {
    const res = await getAxios().post<Bot>(endpoint, data)
    return res.data
  },
}

export { botsApi as default, endpoint as botsQueryKey, type BotsData, type BotsFilters, type BotsModel }
