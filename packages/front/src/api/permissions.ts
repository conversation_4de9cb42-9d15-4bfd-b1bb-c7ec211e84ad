import { getAxios } from '../app/utils/apiResource'
import { Api, BaseFilters, NO_PAGINATED_REQUEST, PaginatedApiResponse } from './types'

interface PermissionsData {}

enum PermissionsTypeEnum {
  AICONSUMPTION = 'aIConsumption',
  ACCEPTANCETERMS = 'acceptanceTerms',
  AUTHHISTORY = 'authHistory',
  BOTS = 'bots',
  CAMPAIGNS = 'campaigns',
  CHAT = 'chat',
  CONTACTS = 'contacts',
  CUSTOMFIELDS = 'customFields',
  DEPARTMENTS = 'departments',
  DISTRIBUTION = 'distribution',
  DOWNLOAD = 'download',
  EVALUATION = 'evaluation',
  GROUPS = 'groups',
  HOLIDAYS = 'holidays',
  HSM = 'hsm',
  MISCELLANEOUS = 'miscellaneous',
  INTEGRATIONS = 'integrations',
  INTERACTIVE_MESSAGES = 'interactive-messages',
  KNOWLEDGEBASE = 'knowledgeBase',
  MESSAGES = 'messages',
  MYACCOUNT = 'myAccount',
  NOTIFICATION = 'notification',
  ORGANIZATIONS = 'organizations',
  OVERVIEW = 'overview',
  PEOPLE = 'people',
  PIPELINES = 'pipelines',
  AUTOMATION = 'automation',
  QUICKREPLIES = 'quickReplies',
  ROLES = 'roles',
  SCHEDULE = 'schedule',
  SERVICES = 'services',
  SETTINGSAPI = 'settingsApi',
  STICKERS = 'stickers',
  TAGS = 'tags',
  TICKETTOPICS = 'ticketTopics',
  TICKETS = 'tickets',
  TIMETABLES = 'timetables',
  USERS = 'users',
}

enum PermissionsByTabEnum {
  SERVICE = 'SERVICE',
  MARKETING = 'MARKETING',
  AI = 'AI',
  MANAGEMENT = 'MANAGEMENT',
}

interface PermissionsModel {
  id: string
  name: string
  type: PermissionsTypeEnum
  description: string
  createdAt: string
  updatedAt: string
  deletedAt: string
}

interface PermissionsFilters extends BaseFilters {}

interface PermissionsApi
  extends Api<PaginatedApiResponse<PermissionsModel>, PermissionsData, PermissionsFilters, PermissionsModel> {}

const buildQuery = ({ paginate = true, ...filters }: PermissionsFilters) => {
  return {
    query: JSON.stringify({
      where: {},
      order: [[filters.sort ?? 'name', filters.order ?? 'ASC']],
      page: paginate ? filters.page ?? 1 : undefined,
      perPage: paginate ? filters.perPage ?? 15 : undefined,
      paginate: paginate ?? true,
    }),
  }
}

const endpoint = '/permissions'

const permissionsApi: PermissionsApi = {
  async create(data: PermissionsData) {
    const res = await getAxios().post<PermissionsModel>(endpoint, data)
    return res.data
  },
  async delete(id: string) {
    await getAxios().delete<void>(`${endpoint}/${id}`)
  },
  async getAll(filters: PermissionsFilters) {
    const res = await getAxios().get<PaginatedApiResponse<PermissionsModel>>(`${endpoint}`, {
      params: buildQuery(filters),
    })
    return res.data
  },
  async getNoPaginatedAll(filters: PermissionsFilters) {
    const res = await getAxios().get<PermissionsModel[]>(`${endpoint}`, {
      params: buildQuery({ ...filters, ...NO_PAGINATED_REQUEST }),
    })
    return res.data
  },
  async getById(id: string) {
    const res = await getAxios().get<PermissionsModel>(`${endpoint}/${id}`)
    return res.data
  },
  async update(id: string, data: PermissionsData) {
    const res = await getAxios().put<PermissionsModel>(`${endpoint}/${id}`, data)
    return res.data
  },
}

export {
  permissionsApi as default,
  endpoint as permissionsQueryKey,
  PermissionsByTabEnum,
  PermissionsTypeEnum,
  type PermissionsData,
  type PermissionsFilters,
  type PermissionsModel,
}
