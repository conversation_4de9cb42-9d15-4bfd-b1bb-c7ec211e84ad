/* eslint-disable no-multi-spaces */

import { createAction } from 'redux-act-light'
import { NAME } from './constants'

export const attemptLogin = createAction(`${NAME}/ATTEMPT_LOGIN`)
export const attemptLoginStarted = createAction(`${NAME}/ATTEMPT_LOGIN_STARTED`)
export const attemptLoginSuccess = createAction(`${NAME}/ATTEMPT_LOGIN_SUCCESS`)
export const attemptLoginFailed = createAction(`${NAME}/ATTEMPT_LOGIN_FAILED`)

export const takeover = createAction(`${NAME}/TAKEOVER`)
export const takeoverStarted = createAction(`${NAME}/TAKEOVER_STARTED`)
export const takeoverSuccess = createAction(`${NAME}/TAKEOVER_SUCCESS`)
export const takeoverFailed = createAction(`${NAME}/TAKEOVER_FAILED`)

export const tookOver = createAction(`${NAME}/TOOK_OVER`)
export const userArchived = createAction(`${NAME}/USER_ARCHIVED`)

export const displayTerms = createAction(`${NAME}/DISPLAY_TERMS`)

export const displayAbsenceModal = createAction(`${NAME}/DISPLAY_ABSENCE_MODAL`)

export const displayIpMessage = createAction(`${NAME}/DISPLAY_IP_MESSSAGE`)

export const displayHourMessage = createAction(`${NAME}/DISPLAY_HOUR_MESSSAGE`)

export const accountExpired = createAction(`${NAME}/ACCOUNT_EXPIRED`)

export const passwordExpired = createAction(`${NAME}/PASSWORD_EXPIRED`)

export const hydrateLogin = createAction(`${NAME}/HYDRATE_LOGIN`)
export const hydrateLoginStarted = createAction(`${NAME}/HYDRATE_LOGIN_STARTED`)
export const hydrateLoginSuccess = createAction(`${NAME}/HYDRATE_LOGIN_SUCCESS`)
export const hydrateLoginFailed = createAction(`${NAME}/HYDRATE_LOGIN_FAILED`)

export const logout = createAction(`${NAME}/LOGOUT`)

export const clean = createAction(`${NAME}/CLEAN`)

export const cleanState = createAction(`${NAME}/CLEAN_STATE`)

export const needsNewAccessToken = createAction(`${NAME}/NEEDS_NEW_ACCESS_TOKEN`)

export const fetchToken = createAction(`${NAME}/FETCH_TOKEN`)
export const fetchTokenStarted = createAction(`${NAME}/FETCH_TOKEN_STARTED`)
export const fetchTokenSuccess = createAction(`${NAME}/FETCH_TOKEN_SUCCESS`)
export const fetchTokenFailed = createAction(`${NAME}/FETCH_TOKEN_FAILED`)

export const refreshToken = createAction(`${NAME}/REFRESH_TOKEN`)
export const refreshTokenStarted = createAction(`${NAME}/REFRESH_TOKEN_STARTED`)
export const refreshTokenSuccess = createAction(`${NAME}/REFRESH_TOKEN_SUCCESS`)
export const refreshTokenFailed = createAction(`${NAME}/REFRESH_TOKEN_FAILED`)

export const fetchUser = createAction(`${NAME}/FETCH_USER`)
export const fetchUserStarted = createAction(`${NAME}/FETCH_USER_STARTED`)
export const fetchUserSuccess = createAction(`${NAME}/FETCH_USER_SUCCESS`)
export const fetchUserFailed = createAction(`${NAME}/FETCH_USER_FAILED`)

export const updateUser = createAction(`${NAME}/UPDATE_USER`)
export const updateUserStarted = createAction(`${NAME}/UPDATE_USER_STARTED`)
export const updateUserSuccess = createAction(`${NAME}/UPDATE_USER_SUCCESS`)
export const updateUserFailed = createAction(`${NAME}/UPDATE_USER_FAILED`)

export const updateUserStatus = createAction(`${NAME}/UPDATE_USER_STATUS`)
export const updateUserStatusStarted = createAction(`${NAME}/UPDATE_USER_STATUS_STARTED`)
export const updateUserStatusSuccess = createAction(`${NAME}/UPDATE_USER_STATUS_SUCCESS`)
export const updateUserStatusFailed = createAction(`${NAME}/UPDATE_USER_STATUS_FAILED`)

export const loggedIn = createAction(`${NAME}/LOGGED_IN`)

export const emitSocketEvent = createAction(`${NAME}/EMIT_SOCKET_EVENT`)
export const updateSocketUiState = createAction(`${NAME}/UPDATE_SOCKET_UI_STATE`)
export const urlRedirect = createAction(`${NAME}/URL_REDIRECT`)
export const setTwoFactorAuthToken = createAction(`${NAME}/SET_TWO_FACTOR_AUTH_TOKEN`)
export const showTwoFactorAuthTokenModal = createAction(`${NAME}/SHOW_TWO_FACTOR_AUTH_TOKEN_MODAL`)
