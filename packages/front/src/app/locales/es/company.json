{"TITLE_COMPANY": "Configuraciones de la empresa", "TITLE_MENU_SAC": "Atención al Cliente", "TITLE_MENU_TIME_OPERATION": "Horario de operación", "LABEL_NAME_COMPANY": "Nombre de la empresa", "LABEL_TIMEZONE": "Zona horaria", "LABEL_TIMETABLE_COMPANY": "Tabla de horarios", "LABEL_IPS_RESTRICTION": "Restricción de IPs", "LABEL_IPS_ACCEPT": "IPs aceptadas", "LABEL_WHATSAPP_BUSINESS_HSM_AVAILABLE": "Plantillas disponibles de WhatsApp Business", "LABEL_SAC_NAME_ATTENDANT_BEGINNING_MESSAGE": "Nombre del operador al inicio de los mensajes", "LABEL_SAC_DISABLE_AUTOMATIC_TRANSFER": "Deshabilitar transferencia automática al departamento/operador predeterminado", "LABEL_SAC_NOTIFY_OPENING_OF_TICKETS": "Notificar apertura de tickets", "LABEL_SAC_NOTIFY_TRANSFER_OF_TICKETS": "Notificar transferencia de tickets", "LABEL_SAC_NOTIFY_NEW_MESSAGE": "Notificar nuevo mensaje", "LABEL_SAC_SHOW_SUPPORT_INFORMATION": "Mostrar información de soporte de {{appName}}", "LABEL_SAC_MAKE_SUBJECT_REQUIRED": "Hacer obligatorio el asunto", "LABEL_VALIDATE_NOTIFICATION_QUEUE": "Mostrar notificaciones por departamento en la aplicación", "MESSAGE_GREATER_THAN_LESS_THAN": "Debe ser mayor que {{min}} y menor que {{max}}", "LABEL_SAC_CALL_DOWNTIME_MINUTES": "Tiempo de inactividad del ticket (minutos)", "LABEL_SAC_PROTOCOL_NUMBER_FORMAT": "Formato del número de protocolo", "LABEL_USER_AWAY_MINUTES_TIME": "Tiempo del operador sin interacción con el sistema (minutos)", "HELPER_USER_AWAY_MINUTES_TIME": "Establecer el límite de tiempo hasta que el operador se considere ausente", "TOOLTIP_TITLE_USER_AWAY_MINUTES_TIME": "¿Qué es el tiempo de ausencia del operador?", "TOOLTIP_TEXT_USER_AWAY_MINUTES_TIME": "Es el intervalo de tiempo, en minutos, sin interacciones del operador con el sistema antes de ser clasificado como ausente.", "MESSAGE_GREATER_THAN_EQUAL_LESS_THAN": "Debe ser mayor o igual a {{min}} y menor o igual a {{max}}", "LABEL_SAC_AVAILABLE_VARIABLES": "Variables disponibles", "LABEL_SAC_DATE_IN_FORMAT": "Fecha en formato", "LABEL_SAC_INCREMENTAL_CALL_COUNTER": "Contador incremental de tickets", "LABEL_SAC_STANDARD_DEPARTMENT_TICKETS": "Departamento predeterminado para tickets", "TOAST_MESSAGE_SAVE": "Cambios guardados", "TITLE_ABSENCE": "Control de ausencias", "ABSENCE_INFO": "Monitoreo de ausencias de operadores durante el horario laboral", "ENABLE_ABSENCE_MANAGEMENT": "Activar control de ausencias", "ADD_REASONS_INFO": "Registrar motivos para pausas", "REASON_NAME_INPUT": "Motivo", "REGISTERED_REASONS": "Motivos registrados", "SELECT_ITEMS_TO_LEAVE": "Seleccione los elementos necesarios para salir del control de pausas", "INPUT_PASSWORD": "Ingresar contraseña", "ABSENCE_SAVE_SUCCESS": "Control de ausencias guardado con éxito", "SELECT_REASON_MODAL": "Seleccione el motivo de ausencia", "INSERT_PASSWORD_MODAL": "Ingrese la contraseña para salir", "REASON_REQUIRED_MODAL": "El motivo es obligatorio", "PASSWORD_REQUIRED_MODAL": "La contraseña es obligatoria", "INVALID_PASSWORD": "Contrase<PERSON>", "INVALID_REASON": "Motivo inválido", "UNLOCK_BUTTON_MODAL": "Desb<PERSON>que<PERSON>", "REASONS_EMPTY": "No se seleccionaron motivos", "EDIT_PERMISSION": "Usuario sin permiso de edición", "LABEL_SAC_DUPLICATE_NAMES": "<PERSON><PERSON><PERSON> no<PERSON> duplica<PERSON>", "SWITCH_IP_RESTRICTION": "Restricción de IP", "TITLE_SAC": "Configuraciones de atención al cliente", "LABEL_PASSWORD_EXPIRATION_ACTIVE": "Expiración de contraseña", "LABEL_PASSWORD_EXPIRATION_TIME": "Tiempo de expiración", "LABEL_CREATE_USERS_WITH_PASSWORD": "Crear usuarios con contraseña", "SEND_LINK": "Enviar enlace por correo electrónico para que el usuario cree la contraseña", "GENERATE_PASS": "Generar contraseña aleatoria automáticamente", "MANUAL_CREATE": "Contraseña creada manualmente", "ADMIN_TYPE_CREATE": "Tipo de creación de contraseña", "SHOW_PASS_FIRST_ACESS": "Requerir cambio de contraseña en el primer acceso", "CONFIG_PASS_USERS": "Configuraciones de contraseñas de usuarios", "COUNT_TICKETS": "Contador incremental de tickets", "FORMAT_DATE": "Formato de fecha", "TWO_FACTOR_AUTHENTICATION": "Autenticación de dos factores (2FA)", "ENABLE_TWO_FACTOR_AUTHENTICATION": "Activar autenticación de dos factores", "MAKE_MANDATORY_FOR_ALL_USERS": "Hacer obligatorio para todos los usuarios", "PASSWORD_EXPIRATION_TOOLTIP": "El tiempo de expiración se aplicará a todos los operadores y usuarios de la plataforma.", "DAYS": "días", "TAGS_DISPLAY_IN_CHAT": "Visualización de etiquetas en el chat", "CONFIGURE_TAGS_DISPLAY": "Configurar la visualización de etiquetas para que aparezcan en la lista de tickets abiertos", "ALLOW_OPERATORS_TOGGLE_TAGS_DISPLAY": "Permitir que los operadores activen o desactiven la visualización de etiquetas en el chat", "SSO_AZURE_AD_LABEL": "Nombre en el botón", "SSO_AZURE_AD_TENANT_ID": "ID del arrendatario", "SSO_AZURE_AD_CLIENT_ID": "ID del secreto del cliente", "SSO_AZURE_AD_CLIENT_SECRET": "Valor del secreto del cliente", "SSO_AZURE_AD_CALLBACK_URL": "URL de callback", "TAB_GENERAL": "General", "TAB_SAC": "Atención al Cliente", "TAB_WORK_PLAN": "Horario de trabajo", "TAB_SECURITY": "Seguridad", "TITLE_GENERAL_COMPANY_SETTINGS": "Configuraciones generales", "AUTO_SMART_SUMMARY": "Resumen automático de IA de las atenciones", "AUTO_GENERATE_SUMMARY_ON_TRANSFER": "Generar resumen al transferir el caso", "AUTO_GENERATE_SUMMARY_ON_CLOSURE": "Generar resumen al cerrar el caso"}