{"TITLE_COMPANY": "Company settings", "TITLE_MENU_SAC": "Customer Service", "TITLE_MENU_TIME_OPERATION": "Operating hours", "LABEL_NAME_COMPANY": "Company name", "LABEL_TIMEZONE": "Time zone", "LABEL_TIMETABLE_COMPANY": "Schedule table", "LABEL_IPS_RESTRICTION": "IP restriction", "LABEL_IPS_ACCEPT": "Accepted IPs", "LABEL_WHATSAPP_BUSINESS_HSM_AVAILABLE": "Available WhatsApp Business templates", "LABEL_SAC_NAME_ATTENDANT_BEGINNING_MESSAGE": "Attendant's name at the beginning of messages", "LABEL_SAC_DISABLE_AUTOMATIC_TRANSFER": "Disable automatic transfer to default department/attendant", "LABEL_SAC_NOTIFY_OPENING_OF_TICKETS": "Notify ticket opening", "LABEL_SAC_NOTIFY_TRANSFER_OF_TICKETS": "Notify ticket transfer", "LABEL_SAC_NOTIFY_NEW_MESSAGE": "Notify new message", "LABEL_SAC_SHOW_SUPPORT_INFORMATION": "Show {{appName}} support information", "LABEL_SAC_MAKE_SUBJECT_REQUIRED": "Make subject mandatory", "LABEL_VALIDATE_NOTIFICATION_QUEUE": "Display department notifications in the app", "LABEL_SAC_CALL_DOWNTIME_MINUTES": "Ticket inactivity time (minutes)", "LABEL_USER_AWAY_MINUTES_TIME": "Operator time without interaction with the system (minutes)", "HELPER_USER_AWAY_MINUTES_TIME": "Set the time limit until the operator is considered away", "TOOLTIP_TITLE_USER_AWAY_MINUTES_TIME": "What is operator away time?", "TOOLTIP_TEXT_USER_AWAY_MINUTES_TIME": "It is the time interval, in minutes, without operator interactions with the system before being classified as away.", "LABEL_SAC_PROTOCOL_NUMBER_FORMAT": "Protocol number format", "MESSAGE_GREATER_THAN_LESS_THAN": "Must be greater than {{min}} and less than {{max}}", "MESSAGE_GREATER_THAN_EQUAL_LESS_THAN": "Must be greater than or equal to {{min}} and less than or equal to {{max}}", "LABEL_SAC_AVAILABLE_VARIABLES": "Available variables", "LABEL_SAC_DATE_IN_FORMAT": "Date in format", "LABEL_SAC_INCREMENTAL_CALL_COUNTER": "Incremental ticket counter", "LABEL_SAC_STANDARD_DEPARTMENT_TICKETS": "Default department for tickets", "TOAST_MESSAGE_SAVE": "Changes saved", "TITLE_ABSENCE": "Absence control", "ABSENCE_INFO": "Monitor operator absences during shifts", "ENABLE_ABSENCE_MANAGEMENT": "Enable absence control", "ADD_REASONS_INFO": "Register reasons for break usage", "REASON_NAME_INPUT": "Reason", "REGISTERED_REASONS": "Registered reasons", "SELECT_ITEMS_TO_LEAVE": "Select which items are needed to exit break control", "INPUT_PASSWORD": "Enter password", "ABSENCE_SAVE_SUCCESS": "Absence control saved successfully", "SELECT_REASON_MODAL": "Select the reason for absence", "INSERT_PASSWORD_MODAL": "Enter password to exit", "REASON_REQUIRED_MODAL": "Reason is required", "PASSWORD_REQUIRED_MODAL": "Password is required", "INVALID_PASSWORD": "Incorrect password", "INVALID_REASON": "Invalid reason", "UNLOCK_BUTTON_MODAL": "Unlock", "REASONS_EMPTY": "No reasons selected", "EDIT_PERMISSION": "User lacks edit permission", "LABEL_SAC_DUPLICATE_NAMES": "Allow duplicate names", "SWITCH_IP_RESTRICTION": "IP restriction", "TITLE_SAC": "Customer service settings", "LABEL_PASSWORD_EXPIRATION_ACTIVE": "Password expiration", "LABEL_PASSWORD_EXPIRATION_TIME": "Expiration time", "LABEL_CREATE_USERS_WITH_PASSWORD": "Create users with password", "SEND_LINK": "Send email link for user to create password", "GENERATE_PASS": "Automatically generate random password", "MANUAL_CREATE": "Manually created password", "ADMIN_TYPE_CREATE": "Password creation type", "SHOW_PASS_FIRST_ACESS": "Require user to change password on first login", "CONFIG_PASS_USERS": "User password settings", "COUNT_TICKETS": "Incremental ticket counter", "FORMAT_DATE": "Date format", "TWO_FACTOR_AUTHENTICATION": "Two-factor authentication (2FA)", "ENABLE_TWO_FACTOR_AUTHENTICATION": "Enable two-factor authentication", "MAKE_MANDATORY_FOR_ALL_USERS": "Make mandatory for all users", "PASSWORD_EXPIRATION_TOOLTIP": "The expiration period will apply to all operators and platform users.", "DAYS": "days", "TAGS_DISPLAY_IN_CHAT": "Tags display in chat", "CONFIGURE_TAGS_DISPLAY": "Configure tag display to appear in the list of open tickets", "ALLOW_OPERATORS_TOGGLE_TAGS_DISPLAY": "Allow operators to enable or disable tag display in chat", "SSO_AZURE_AD_LABEL": "Button name", "SSO_AZURE_AD_TENANT_ID": "Tenant ID", "SSO_AZURE_AD_CLIENT_ID": "Client secret ID", "SSO_AZURE_AD_CLIENT_SECRET": "Client secret value", "SSO_AZURE_AD_CALLBACK_URL": "Callback URL", "TAB_GENERAL": "General", "TAB_SAC": "Customer Service", "TAB_WORK_PLAN": "Work schedule", "TAB_SECURITY": "Security", "TITLE_GENERAL_COMPANY_SETTINGS": "General settings", "AUTO_SMART_SUMMARY": "AI-generated automatic summary of interactions", "AUTO_GENERATE_SUMMARY_ON_TRANSFER": "Generate summary on ticket transfer", "AUTO_GENERATE_SUMMARY_ON_CLOSURE": "Generate summary on ticket closure"}