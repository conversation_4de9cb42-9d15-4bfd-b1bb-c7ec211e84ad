import styled from 'styled-components'
import { DropdownToggle, ButtonDropdown, DropdownItem, DropdownMenu, UncontrolledDropdown } from 'reactstrap'
import { lighten } from 'polished'
import { LightColor, MineShaftColor, GrayColor, PrimaryColor, TextColor, DisabledColor, BorderColor } from './colors'

interface TableHeadProps {
  columns?: number
  gridColumns?: string
  disableGap?: boolean
}

interface TableRowProps {
  cells?: number
  gridColumns?: string
  background?: string
  firstChildPadding?: string
  disableGap?: boolean
}

interface TableCellProps {
  capitalize?: boolean
  color?: string
  actions?: boolean
  nowrap?: boolean
  disableOverflow?: boolean
  paddingLeftNone?: boolean
}

interface TableProps {
  mtNone?: boolean
}

interface CardFiltersProps {
  mtNone?: boolean
  borderTopNone?: boolean
  borderBottomNone?: boolean
  borderRadiusNone?: boolean
}

interface GroupInputFilterProps {
  hiddenIcon?: boolean
}

export const Table = styled.div<TableProps>`
  font-family: 'Inter', sans-serif;
  margin-top: ${(props) => (props.mtNone ? '0' : '2rem')};
`

export const TableHead = styled.div<TableHeadProps>`
  background: ${LightColor};
  padding: 1rem 0;
  gap: ${(props) => (props.disableGap ? '0px' : '1rem')};
  border: 1px solid rgba(82, 101, 140, 0.15);
  display: grid;
  grid-template-columns: ${(props) =>
    props.gridColumns ? props.gridColumns : props.columns && `repeat(${props.columns}, 1fr)`};
  /* grid-column-gap: 2rem; */
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;

  span:first-child {
    padding-left: 1rem;
  }
`

export const TableColumn = styled.span`
  color: ${(props) => (props.color ? props.color : MineShaftColor)};
  font-weight: 600;
  display: flex;
`

export const TableBody = styled.div`
  border: 1px solid rgba(82, 101, 140, 0.15);
  border-top: 0px;
  background: white;

  div:last-child {
    border-bottom: 0px;
  }

  tr {
    td {
      padding: 20px;
      text-align: center;
    }
  }
`

export const TableRow = styled.div<TableRowProps>`
  display: grid;
  grid-template-columns: ${(props) =>
    props.gridColumns ? props.gridColumns : props.cells && `repeat(${props.cells}, 1fr)`};
  background: ${(props) => (props.background ? props.background : 'none')};
  border-bottom: 1px solid rgba(82, 101, 140, 0.15);
  gap: ${(props) => (props.disableGap ? '0px' : '1rem')};

  div:first-child {
    padding-left: ${(props) => (props.firstChildPadding ? props.firstChildPadding : '1rem')};
  }
`

export const TableCell = styled.div<TableCellProps>`
  padding: ${(props) => (props.actions ? '1rem 1.5rem 1rem 1rem' : '1rem 0')};
  display: ${(props) => (props.actions ? 'flex' : 'block')};
  flex-wrap: ${(props) => (props.nowrap ? 'nowrap' : 'wrap')};
  align-items: center;
  color: ${(props) => (props.color ? props.color : GrayColor)};
  text-transform: ${(props) => (props.capitalize ? 'capitalize' : 'initial')};
  justify-self: ${(props) => (props.actions ? 'end' : 'initial')};
  overflow: ${(props) => (props.actions ? 'initial' : props.disableOverflow ? 'initial' : 'hidden')};
  margin: auto 0;

  text-overflow: ellipsis;
  white-space: nowrap;

  -webkit-line-clamp: 2; // max nb lines to show
  -webkit-box-orient: vertical;

  @media screen and (max-width: 1300px) {
    font-size: 93.75%;
  }
`

export const TableOptions = styled(DropdownToggle)`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease-in-out;
  padding: 5px;
  background: none;
  border: none;
  box-shadow: none;

  &:focus {
    box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2);
  }

  &:hover {
    border-color: transparent;
    background: ${lighten(0.5, PrimaryColor)};
  }

  .icon-options {
    transform: rotate(90deg);
    cursor: pointer;
  }
`

export const ButtonDropdownActions = styled(ButtonDropdown)``

export const DropdownMenuActions = styled(DropdownMenu)`
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  border: none;

  .dropdown-item {
    display: flex;
    align-items: center;
    color: ${TextColor};
    cursor: pointer;
    background: none !important;
    &:focus {
      background: none !important;
    }
    svg {
      margin-right: 5px;
    }
  }
`

export const DropdownItemActions = styled(DropdownItem)`
  color: #3a3a3a;
  font-weight: 700;
`

export const ButtonRefresh = styled.div`
  background: #95a7cc;
  padding: 10px;
  border-radius: 50%;
  width: 45px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  color: white;
  cursor: pointer;
`

export const DropdownMassActions = styled(UncontrolledDropdown)`
  margin-right: 0.6rem;
`

export const DropdownToggleMassActions = styled(DropdownToggle)`
  padding: 10px 25px;
  border-radius: 32px;
  background: ${DisabledColor};
  font-size: 14px;
  border: none;
  color: #fff;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: 0.2s ease-in-out;
  outline: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  svg {
    margin-left: 0.5rem;
  }
`

export const DropdownMenuMassActions = styled(DropdownMenu)`
  top: 10px !important;
`

export const DropdownItemMassActions = styled(DropdownItem)``

export const CardFilters = styled.div<CardFiltersProps>`
  background: white;
  border-radius: ${(props) => (props.borderRadiusNone ? '0px' : '5px')};
  border: 1px solid ${BorderColor};
  border-top: ${(props) => props.borderTopNone && 'none'};
  border-bottom: ${(props) => props.borderBottomNone && 'none'};
  padding: 2rem;
  margin-top: ${(props) => (props.mtNone ? '0px' : '2rem')};
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0px 5px 15px 0px rgba(82, 101, 140, 0.15);
    border: 1px solid ${PrimaryColor};
  }

  input {
    padding-left: 2.5rem;
    border-radius: 25px;
    border: 1px solid rgba(82, 101, 140, 0.15);
  }
`

export const GroupInputFilter = styled.div<GroupInputFilterProps>`
  display: flex;
  position: relative;
  flex-direction: column;

  svg {
    position: absolute;
    left: 12px;
    top: 5px;
  }

  input {
    padding-left: ${(props) => (props.hiddenIcon ? '1rem' : '2.5rem')};
    border-radius: 25px;
    border: 1px solid rgba(82, 101, 140, 0.15);
    &:focus {
      box-shadow: none;
      border: 1px solid ${PrimaryColor};
    }
  }
`
