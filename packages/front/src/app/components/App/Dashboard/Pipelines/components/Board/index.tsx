import React, { useState, useEffect, ReactNode } from 'react'
import { Link, useHistory } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { Row, Col } from 'reactstrap'
import moment from 'moment'
import toast from '../../../../../../utils/toast'
import {
  Con<PERSON>er,
  Header,
  HeaderActions,
  Wrapper,
  TitlePipeline,
  BadgeArchived,
  ButtonIconLocal,
  TextTitle,
  ButtonFilter,
  SelectedFilterDiv,
} from './styles'
import { ButtonIcon, DivOrder, DivOptions } from '../../PipelinesIndex/styles'

import {
  IconFilterOutline,
  IconArrowDown,
  IconOptions,
  IconUserPlus,
  IconColumnsAdd,
  IconOrder,
  IconSearch,
  IconXCircle,
  IconExportPipeline,
  IconGitMerge,
} from '../../../../../common/unconnected/IconDigisac'
import Filters from '../../PipelinesShow/Filters'
import IfUserCan from '../../../../../common/connected/IfUserCan'
import CreatePipeline from '../../CreatePipeline'
import DeletePipeline from '../../DeletePipeline'
import InputGroup from '../../../../../common/unconnected/InputGroup'
import Permission from './Permission'
import * as S from '../../PipelinesIndex/styles'
import { useRequest } from '../../../../../../hooks/useRequest'
import service from '../../../../../../resources/pipelines/api'
import ExportPipeline from '../../Export'
import { hasPermission } from '../../../../../common/connected/IfUserCan/IfUserCan'
import { useToast } from '../../../../../../hooks/useToast'
import { usePipeline } from '../../PipelineContext'

interface BoardProps {
  children: ReactNode
}

const Board: React.FC<BoardProps> = ({ children }) => {
  const {
    pipeline,
    filters,
    fetchPipeline,
    showDivStage,
    showDivAutomation,
    setShowDivStage,
    setShowDivAutomation,
    handleFilterChange,
    handleSortChange,
    user,
    isFiltersShowing,
    setIsFiltersShowing,
  } = usePipeline()

  const { t } = useTranslation(['pipelines', 'common'])
  const [showPermissions, setShowPermissions] = useState(false)
  const [isOrderShowing, setIsOrderShowing] = useState(false)
  const [isOptionsShowing, setIsOptionsShowing] = useState(false)
  const [isExportShowing, setIsExportShowing] = useState(false)
  const [selectedFilters, setSelectedFilters] = useState({})
  const [isEditPipelineModalOpen, setIsEditPipelineModalOpen] = useState(false)
  const [isArchivePipelineModalOpen, setIsArchivePipelineModalOpen] = useState(false)
  const [isExportPipelineModalOpen, setIsExportPipelineModalOpen] = useState(false)
  const [showDivSelectedFilter, setShowDivSelectedFilter] = useState(0)

  const history = useHistory()

  const [{ response }, unarchivePipeline] = useRequest(service.archivePipeline)

  const handleCloseEditPipelineModal = () => setIsEditPipelineModalOpen(false)
  const handleCloseArchivePipelineModal = () => setIsArchivePipelineModalOpen(false)
  const handleCloseExportPipelineModal = () => setIsExportPipelineModalOpen(false)

  const [{ response: duplicateResponse }, duplicate] = useRequest(service.duplicate, {})

  const { toast: newToast } = useToast()

  useEffect(() => {
    setSelectedFilters(filters)
  }, [filters])

  useEffect(() => {
    if (isFiltersShowing || showDivStage) {
      setIsOrderShowing(false)
      setIsOptionsShowing(false)
      setIsExportShowing(false)
    }
  }, [isFiltersShowing, showDivStage])

  useEffect(() => {
    if (isOrderShowing) {
      setIsOptionsShowing(false)
      setIsExportShowing(false)
    }
  }, [isOrderShowing])

  useEffect(() => {
    if (isOptionsShowing) {
      setIsOrderShowing(false)
      setIsExportShowing(false)
    }
  }, [isOptionsShowing])

  useEffect(() => {
    if (isExportShowing) {
      setIsOrderShowing(false)
      setIsOptionsShowing(false)
    }
  }, [isExportShowing])

  const changeFilter = (key, value) => {
    handleFilterChange(key, value)
  }

  const removeFilterKey = (key, value) => {
    handleFilterChange(key, value)
    setSelectedFilters({
      ...selectedFilters,
      [key]: value?.name || value,
      ...(key === 'status' &&
        value === null && {
          wonFrom: null,
          wonUntil: null,
        }),
    })
  }

  const getDescription = (filter, index) => {
    const filterName = Object.keys(selectedFilters)?.[index]?.toUpperCase()
    const names = {
      SERVICE: t('FILTER_CONNECTION'),
      OWNER: t('FILTER_RESPONSIBLE'),
      STATUS: 'Status',
      CREATEDFROM: t('FILTER_CREATED_FROM'),
      CREATEDUNTIL: t('FILTER_CREATED_UNTIL'),
      WONFROM: t('FILTER_WON_FROM'),
      WONUNTIL: t('FILTER_WON_UNTIL'),
      NAMETITLE: t('FILTER_NAME_TITLE'),
    }
    const formattedFilter = ['CREATEDFROM', 'CREATEDUNTIL', 'WONFROM', 'WONUNTIL'].includes(filterName)
      ? moment(filter).format('DD/MM/YYYY')
      : filter?.name ?? filter
    return `${names?.[filterName]}: ${formattedFilter}`
  }

  const unarchive = async () => {
    await unarchivePipeline(pipeline?.id, { archivedAt: null })
    fetchPipeline()
  }

  useEffect(() => {
    if (response) {
      toast.success(t('FUNNEL_UNARCHIVED'))
    }
  }, [response])

  useEffect(() => {
    const handleDocumentClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      const button = document.getElementById('pipeline-button-actions')

      if (isOptionsShowing && button && !button.contains(target)) {
        setIsOptionsShowing(false)
      }
    }

    document.addEventListener('click', handleDocumentClick)

    return () => {
      document.removeEventListener('click', handleDocumentClick)
    }
  }, [isOptionsShowing])

  useEffect(() => {
    const handleDocumentClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      const button = document.getElementById('pipeline-show-order')

      if (isOrderShowing && button && !button.contains(target)) {
        setIsOrderShowing(false)
      }
    }

    document.addEventListener('click', handleDocumentClick)

    return () => {
      document.removeEventListener('click', handleDocumentClick)
    }
  }, [isOrderShowing])

  useEffect(() => {
    const handleDocumentClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      const button = document.getElementById('pipeline-button-export')

      if (isExportShowing && button && !button.contains(target)) {
        setIsExportShowing(false)
      }
    }

    document.addEventListener('click', handleDocumentClick)

    return () => {
      document.removeEventListener('click', handleDocumentClick)
    }
  }, [isExportShowing])

  const sortChange = (value: Array<any>, div: number) => {
    if (showDivSelectedFilter === div) {
      handleSortChange([['order', 'ASC']])
      setShowDivSelectedFilter(0)
      return
    }

    handleSortChange(value)
    setShowDivSelectedFilter(div)
  }

  const searchByTitle = (value: string) => {
    changeFilter('nameTitle', value)
  }

  useEffect(() => {
    if (duplicateResponse) {
      newToast({
        variant: 'success',
        title: t('DUPLICATE_PIPELINE_SUCCESS_TITLE'),
        description: t('DUPLICATE_PIPELINE_SUCCESS_DESCRIPTION'),
      })
      history.push(`/pipelines/show/${duplicateResponse?.id}`)
    }
  }, [duplicateResponse, history, t])

  return (
    <>
      <S.Breadcrumb>
        <Link to="/pipelines" data-testid="pipeline-link-breadcrumb">
          {t('TITLE')}
        </Link>
        <span> / </span>
        <span>{pipeline?.name}</span>
      </S.Breadcrumb>
      {isFiltersShowing && <Filters />}
      <Wrapper>
        <Header>
          <TitlePipeline title={pipeline?.name}>
            <ButtonIcon data-testid="pipeline-button-previous" onClick={() => history.push('/pipelines')}>
              <IconArrowDown
                fill="#320B7D"
                width="16"
                height="16"
                style={{
                  transform: 'rotate(90deg)',
                  marginRight: '10px',
                }}
              />
            </ButtonIcon>
            <TextTitle>{pipeline?.name}</TextTitle>
            <BadgeArchived>{pipeline?.archivedAt ? t('common:LABEL_ARCHIVED') : ''}</BadgeArchived>
          </TitlePipeline>
          <HeaderActions style={{ alignItems: 'start' }}>
            <InputGroup
              data-testid="pipeline-search-by-client"
              icon={<IconSearch fill="#6E7A89" height="20" viewBox="0 -5 32 32" />}
              placeholder={t('SEARCH_BY_CLIENT_TITLE')}
              value={filters.nameTitle}
              onChange={(e) => searchByTitle(e.target.value)}
            />

            <ButtonIconLocal
              id="pipeline-show-filter"
              onClick={() => setIsFiltersShowing(!isFiltersShowing)}
              isSelected={isFiltersShowing}
              data-testid="pipeline-button-show-filter"
            >
              <div className="tooltipUncontrolled">{t('BOARD_TOOLTIP_FILTER')}</div>
              <IconFilterOutline fill="#324B7D" height="18" />
              <S.FilterBadge pill>
                {Object.values(selectedFilters)?.filter((filter) => filter)?.length > 0 &&
                  Object.values(selectedFilters)?.filter((filter) => filter)?.length}
              </S.FilterBadge>
            </ButtonIconLocal>

            <ButtonIconLocal
              id="pipeline-show-order"
              onClick={() => setIsOrderShowing(!isOrderShowing)}
              isSelected={isOrderShowing}
              data-testid="pipeline-button-show-order"
            >
              <div className="tooltipUncontrolled">{t('BOARD_TOOLTIP_ORDERING')}</div>
              <IconOrder fill={isOrderShowing ? '#000' : '#324B7D'} />
            </ButtonIconLocal>

            {isOrderShowing && (
              <DivOrder style={{ marginLeft: '370px', marginTop: '55px' }}>
                <span className="title">{t('ORDER_BY')}</span>
                <ButtonFilter
                  data-testid="pipeline-order-newest"
                  onClick={() => sortChange([['createdAt', 'DESC']], 1)}
                >
                  {t('ORDER_BY_NEWEST')}
                  {showDivSelectedFilter === 1 && <SelectedFilterDiv />}
                </ButtonFilter>
                <ButtonFilter data-testid="pipeline-order-oldest" onClick={() => sortChange([['createdAt', 'ASC']], 2)}>
                  {t('ORDER_BY_OLDEST')}
                  {showDivSelectedFilter === 2 && <SelectedFilterDiv />}
                </ButtonFilter>
                <ButtonFilter
                  data-testid="pipeline-order-less-value"
                  onClick={() => sortChange([['totalValue', 'ASC']], 3)}
                >
                  {t('ORDER_BY_LESS_VALUE')}
                  {showDivSelectedFilter === 3 && <SelectedFilterDiv />}
                </ButtonFilter>
                <ButtonFilter
                  data-testid="pipeline-order-large-value"
                  onClick={() => sortChange([['totalValue', 'DESC']], 4)}
                >
                  {t('ORDER_BY_LARGE_VALUE')}
                  {showDivSelectedFilter === 4 && <SelectedFilterDiv />}
                </ButtonFilter>
                <ButtonFilter
                  data-testid="pipeline-order-alphabetics-a-z"
                  onClick={() => sortChange([['contact.name', 'ASC']], 5)}
                >
                  {t('ORDER_BY_ALPHABETICS_A_Z')}
                  {showDivSelectedFilter === 5 && <SelectedFilterDiv />}
                </ButtonFilter>
                <ButtonFilter
                  data-testid="pipeline-order-alphabetics-z-a"
                  onClick={() => sortChange([['contact.name', 'DESC']], 6)}
                >
                  {t('ORDER_BY_ALPHABETICS_Z_A')}
                  {showDivSelectedFilter === 6 && <SelectedFilterDiv />}
                </ButtonFilter>
              </DivOrder>
            )}
            {!pipeline?.archivedAt && hasPermission(user.permissions, 'pipelines.create') && (
              <ButtonIconLocal
                onClick={() => setShowDivStage(!showDivStage)}
                data-testid="pipeline-button-create-stage"
              >
                <IconColumnsAdd fill="#324B7D" width="20" height="20" />
                <div className="tooltipUncontrolled">{t('BOARD_TOOLTIP_CREATE_STAGE')}</div>
              </ButtonIconLocal>
            )}
            {!pipeline?.archivedAt && hasPermission(user.permissions, 'automation.view') && (
              <ButtonIconLocal
                id="pipeline-show-automation"
                onClick={() => setShowDivAutomation(!showDivAutomation)}
                data-testid="pipeline-button-show-automation"
              >
                <IconGitMerge fill="#324B7D" width="14" height="14" />
                <div className="tooltipUncontrolled">{t('BOARD_TOOLTIP_CONFIG_AUTOMATION')}</div>
                <S.BadgeForMenuHorizontal>{t('common:NEW')}</S.BadgeForMenuHorizontal>
              </ButtonIconLocal>
            )}

            <ButtonIconLocal
              id="pipeline-show-permission"
              onClick={() => setShowPermissions(!showPermissions)}
              data-testid="pipeline-button-show-permission"
            >
              <IconUserPlus fill="#324B7D" width="20" height="20" />
              <div className="tooltipUncontrolled">{t('BOARD_TOOLTIP_PIPELINE')}</div>
            </ButtonIconLocal>

            <ButtonIconLocal
              onClick={() => setIsExportPipelineModalOpen(!isExportPipelineModalOpen)}
              isSelected={isExportShowing}
              data-testid="pipeline-button-export"
              id="pipeline-button-export"
            >
              <IconExportPipeline fill="#324B7D" width="24" height="24" viewBox="-3 -3 20 20" />
              <div className="tooltipUncontrolled">{t('BOARD_TOOLTIP_EXPORT')}</div>
            </ButtonIconLocal>
            {isExportShowing && (
              <DivOptions>
                {
                  <IfUserCan permission="pipelines.view">
                    <button
                      data-testid="pipeline-button-export-modal"
                      onClick={() => setIsExportPipelineModalOpen(true)}
                    >
                      {t('EXPORT_PIPELINE')}
                    </button>
                  </IfUserCan>
                }
              </DivOptions>
            )}
            {(hasPermission(user.permissions, 'pipelines.update') ||
              hasPermission(user.permissions, 'pipelines.create') ||
              hasPermission(user.permissions, 'pipelines.destroy')) && (
              <ButtonIconLocal
                onClick={() => setIsOptionsShowing(!isOptionsShowing)}
                isSelected={isOptionsShowing}
                data-testid="pipeline-button-actions"
                id="pipeline-button-actions"
              >
                <div style={{ transform: `rotate(90deg)` }}>
                  <IconOptions className="icon-options" fill="#324B7D" />
                </div>
                <div className="tooltipUncontrolled">{t('BOARD_TOOLTIP_MORE_ACTIONS')}</div>
              </ButtonIconLocal>
            )}
          </HeaderActions>

          {isOptionsShowing && (
            <DivOptions>
              {!pipeline?.archivedAt && (
                <IfUserCan permission="pipelines.update">
                  <button
                    id="pipeline-button-edit-pipeline"
                    data-testid="pipeline-button-edit-pipeline"
                    onClick={() => setIsEditPipelineModalOpen(true)}
                  >
                    {t('EDIT_PIPELINE')}
                  </button>
                </IfUserCan>
              )}
              <IfUserCan permission="pipelines.create">
                <button
                  id="pipeline-button-duplicate-pipeline"
                  data-testid="pipeline-button-duplicate-pipeline"
                  onClick={() => duplicate(pipeline?.id)}
                >
                  {t('DUPLICATE_PIPELINE')}
                </button>
              </IfUserCan>
              <IfUserCan permission="pipelines.destroy">
                {!pipeline?.archivedAt ? (
                  <button
                    id="pipeline-button-archive"
                    data-testid="pipeline-button-archive"
                    onClick={() => setIsArchivePipelineModalOpen(true)}
                    style={{ color: 'red' }}
                  >
                    {t('ARCHIVE_PIPELINE')}
                  </button>
                ) : (
                  <button
                    id="pipeline-button-unarchive"
                    data-testid="pipeline-button-unarchive"
                    onClick={unarchive}
                    style={{ color: '#586171' }}
                  >
                    {t('UNARCHIVE_PIPELINE')}
                  </button>
                )}
              </IfUserCan>
            </DivOptions>
          )}
        </Header>
        <Row>
          <Col className="mt-1" sm="6">
            {Object.values(selectedFilters)?.filter((filter) => filter)?.length > 0 && (
              <S.SelectedFilters>
                <span>{t('FILTER_BY')}</span>
                {Object.values(selectedFilters).map(
                  (filter, index) =>
                    filter && (
                      <div className="filter">
                        {getDescription(filter, index)}
                        <button
                          data-testid={`pipeline-button-remove-filter-${Object.keys(selectedFilters)?.[index]}`}
                          onClick={() => removeFilterKey(Object.keys(selectedFilters)?.[index], null)}
                        >
                          <IconXCircle />
                        </button>
                      </div>
                    ),
                )}
              </S.SelectedFilters>
            )}
          </Col>
        </Row>
        <Container>{children}</Container>
      </Wrapper>
      <IfUserCan permission="pipelines.update">
        <CreatePipeline
          isOpen={isEditPipelineModalOpen}
          onClose={handleCloseEditPipelineModal}
          pipeline={pipeline}
          refresh={fetchPipeline}
        />
      </IfUserCan>
      <IfUserCan permission="pipelines.destroy">
        <DeletePipeline
          isOpen={isArchivePipelineModalOpen}
          onClose={handleCloseArchivePipelineModal}
          id={pipeline?.id}
          refresh={fetchPipeline}
        />
      </IfUserCan>
      <IfUserCan permission="pipelines.view">
        <ExportPipeline isOpen={isExportPipelineModalOpen} onClose={handleCloseExportPipelineModal} id={pipeline?.id} />
      </IfUserCan>
      <Permission setShowPermissions={setShowPermissions} showPermissions={showPermissions} />
    </>
  )
}

export default Board
