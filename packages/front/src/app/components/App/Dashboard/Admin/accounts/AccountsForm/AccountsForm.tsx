import React, { Component } from 'react'
import PropTypes from 'prop-types'
import Helmet from 'react-helmet'
import isDate from 'lodash/isDate'
import { Button, Card, Row, Col, Form, FormGroup, Input, Label, ModalBody, ModalHeader } from 'reactstrap'
import reformed from 'react-reformed'
import isBefore from 'date-fns/isBefore'
import SweetAlert from 'react-bootstrap-sweetalert'
import { withTranslation } from 'react-i18next'
import moment from 'moment'
import { required } from '../../../../../../utils/validator/validators'
import InputGroup, { InputGroupWrapper } from '../../../../../common/unconnected/InputGroup'
import LoadingButton from '../../../../../common/unconnected/LoadingButton'
import Switch from '../../../../../common/unconnected/Switch'
import withValidation from '../../../../../common/unconnected/withValidation'
import DateSelect from '../../../../../common/unconnected/Datetime'
import { TYPE_OPTIONS, ACTIVE_TYPES } from '../../../services/ServicesConstants'
import * as accountsFlags from '../AccountsFlags'
import { ModalDigisac, ModalFooter } from '../../../../styles/common'
import ButtonClose from '../../../../../common/unconnected/ButtonClose'
import convertSecondsToTimeString from '../../../../../../utils/convertSecondsToTimeString'

class AccountsForm extends Component {
  constructor(props) {
    super(props)

    this.state = {
      isModalShowing: true,
      flags: [],
      isUserEmailRequired: true,
      isModalError: false,
      isBlockListActive: false,
      validExpiresAt: false,
    }

    this.handleSubmit = this.handleSubmit.bind(this)
    this.close = this.close.bind(this)
  }

  UNSAFE_componentWillMount() {
    const { setModel, account, model, t } = this.props

    setModel({
      name: model.name || '',
      isCampaignActive: model.isCampaignActive || false,
      isActive: model.isActive || true,
      managerNumber: ((account && account.data) || {}).managerNumber || '',
      users: ((account && account.plan) || {}).users || 3,
      whatsapp: (((account && account?.plan) || {}).services || {}).whatsapp || '0',
      'whatsapp-business': (((account && account.plan) || {}).services || {})['whatsapp-business'] || '0',
      'whatsapp-remote': (((account && account.plan) || {}).services || {})['whatsapp-remote'] || '0',
      webchat: (((account && account.plan) || {}).services || {}).webchat || '0',
      telegram: (((account && account.plan) || {}).services || {}).telegram || '0',
      'sms-wavy': (((account && account.plan) || {}).services || {})['sms-wavy'] || '0',
      email: (((account && account.plan) || {}).services || {}).email || '0',
      'facebook-messenger': (((account && account.plan) || {}).services || {})['facebook-messenger'] || '0',
      instagram: (((account && account.plan) || {}).services || {}).instagram || '0',
      'google-business-message': (((account && account.plan) || {}).services || {})['google-business-message'] || 0,
      'reclame-aqui': (((account && account.plan) || {}).services || {})['reclame-aqui'] || 0,
      expiresAt: (account && account.expiresAt) || null,
      settings: (account && account.settings) || {
        enableHsmStats: model?.settings?.enableHsmStats || false,
        isUserEmailRequired: true,
        isBlockListActive: false,
        campaign: {
          'sms-wavy': model?.settings?.campaign['sms-wavy'] || true,
          whatsapp: model?.settings?.campaign?.whatsapp || false,
          instagram: model?.settings?.campaign.instagram || false,
          'whatsapp-business': model?.settings?.campaign['whatsapp-business'] || false,
          'facebook-messenger': model?.settings?.campaign['facebook-messenger'] || false,
        },
        flags: { 'bots-v3': true },
        idHub360: account?.settings?.idHub360 || '',
      },
      hsmLimit: account?.plan?.hsmLimit,
      hsmUsedLimit: account?.plan?.hsmUsedLimit,
      promptAiTransfer: model.promptAiTransfer || null,
      promptAiFinalize: model.promptAiFinalize || null,
      ...account,
      plan: {
        ...account?.plan,
        ai: {
          ...account?.plan?.ai,
          transcription: account?.plan?.ai?.transcription / 60,
        },
      },
      promptAiCsat: model.promptAiCsat ?? account?.promptAiCsat ?? t('LABEL_PROMPT_DEFAULT_AI_CSAT'),
    })

    this.fetch()
    this.getJsonFlags()
  }

  UNSAFE_componentWillReceiveProps(newProps) {
    // did not had a account but now has
    const { account, setModel, isLoading } = this.props
    if (!account && newProps.account) {
      setModel(newProps.account)
    }

    // was loading, ended loading and didn't error'ed
    if (isLoading && !newProps.isLoading && !newProps.error) {
      this.close()
    }
  }

  getJsonFlags() {
    this.setState({ flags: accountsFlags.getJsonFlags()?.flags || {} })
  }

  handleRenderLabelFlag(flagValue) {
    const { t } = this.props

    switch (flagValue) {
      case 'bots-v2':
        return t('CREATE_ACCOUNT_LABEL_BOT_FLOW_V2')
      case 'bots-v3':
        return t('CREATE_ACCOUNT_LABEL_BOT_FLOW_V3')
      case 'enable-bots-v3-ai-node':
        return t('ENABLE_BOTS_V3_AI_NODE')
      case 'distribution':
        return t('CREATE_ACCOUNT_LABEL_CALL_DISTRIBUTION')
      case 'by-user-and-by-department-tabs':
        return t('CREATE_ACCOUNT_LABEL_TABS_PER_ATTENDANT')
      case 'internal-chat':
        return t('CREATE_ACCOUNT_LABEL_INTERNAL_CHAT')
      case 'absence-management':
        return t('CREATE_ACCOUNT_LABEL_ABSENCE_MANAGEMENT')
      case 'invalid-webhooks-inactivator':
        return t('CREATE_ACCOUNT_LABEL_INVALID_WEBHOOKS_INACTIVATOR')
      case 'disable-hsm-limit':
        return t('CREATE_ACCOUNT_LABEL_DISABLE_HSM_LIMIT')
      case 'enable-audio-transcription':
        return t('ENABLE_AUDIO_TRANSCRIPTION')
      case 'enable-smart-summary':
        return t('ENABLE_SMART_SUMMARY')
      case 'search-messages':
        return t('CREATE_ACCOUNT_LABEL_SEARCH_MESSAGES')
      case 'enable-sales-funnel':
        return t('ENABLE_SALES_FUNNEL')
      case 'use-block-message-rules-by-service':
        return t('USE_BLOCK_MESSAGE_RULES_BY_SERVICE')
      case 'enable-magic-text':
        return t('ENABLE_MAGIC_TEXT')
      case 'enable-smart-csat-score':
        return t('CREATE_ACCOUNT_LABEL_IA_CSAT')
      case 'enable-audio-waveform':
        return t('CREATE_ACCOUNT_LABEL_ENABLE_AUDIO_WAVEFORM')
      case 'enable-copilot':
        return t('ENABLE_COPILOT')
    }
  }

  fetch() {
    const {
      fetchOneAccount,
      match: {
        params: { id },
      },
    } = this.props

    if (id) fetchOneAccount({ id })
  }

  close() {
    const { history, previousMatch } = this.props

    this.setState({
      isModalShowing: false,
    })

    setTimeout(
      () =>
        history.push({
          pathname: previousMatch.url,
          state: { refetch: true },
        }),
      300,
    )
  }

  handleSubmit(e) {
    e.preventDefault()

    const { saveForm, model, validation } = this.props

    if (parseInt(model.users) <= 0) {
      this.setState({ isModalError: !this.state.isModalError })
      return
    }

    const requiredValidation = [required, 'Campo obrigatório.']

    validation.setRules({
      name: [requiredValidation],
    })

    validation.validateAll().then((valid) => {
      if (!valid || model.name?.trim() === '') return

      if (model.isCampaignActive) {
        const checksToggle = []
        for (const i in model.settings.campaign) {
          checksToggle.push(model.settings.campaign[i])
        }
        if (!checksToggle.includes(true)) {
          model.isCampaignActive = false
        }
      }

      if (model?.plan?.ai?.transcription) {
        model.plan.ai.transcription *= 60
      }

      saveForm({ account: model })
    })

    if (model.name?.trim() !== '') {
      setTimeout(() => {
        window.location.reload()
      }, 2000)
    }
  }

  isEditing() {
    const { account } = this.props

    return account && account.id
  }

  getTitle() {
    const { t } = this.props
    return `${this.isEditing() ? t('CREATE_ACCOUNT_LABEL_EDITING') : t('CREATE_ACCOUNT_LABEL_CREATING')} ${t(
      'CREATE_ACCOUNT_LABEL_ACCOUNT_MANY',
    )}`
  }

  handleSetExpiresAt(value, input) {
    const { model, setModel } = this.props
    if (isDate(value) && this.isEditing()) {
      if (
        isBefore(
          Date.parse(moment(new Date(value)).format('YYYY-MM-DD HH:mm:ss')),
          Date.parse(moment(new Date(model.createdAt)).format('YYYY-MM-DD HH:mm:ss')),
        )
      ) {
        this.setState({ validExpiresAt: true })
      } else {
        this.setState({ validExpiresAt: false })
      }
    }

    if (input.id === 'renewAiDate') {
      return setModel({
        ...model,
        plan: {
          ...model.plan,
          renewDate: value,
        },
      })
    }
    return input.setProperty(input.id, value)
  }

  render() {
    const { isModalShowing } = this.state
    const { bindInput, validation, isLoading, error, model, setProperty, setModel, t } = this.props

    return (
      <div>
        <Helmet title={this.getTitle()} />
        <Form onSubmit={this.handleSubmit}>
          <ModalDigisac isOpen={isModalShowing} data-testid="formAccount" toggle={this.close}>
            <ModalHeader toggle={this.close}>
              {this.getTitle()}
              <ButtonClose onClick={this.close} />
            </ModalHeader>
            <ModalBody>
              <InputGroup
                id="name"
                label={t('CREATE_ACCOUNT_LABEL_NAME')}
                data-testid="nameAccount"
                {...{ bindInput, validation }}
              />

              <InputGroupWrapper
                id="expiresAt"
                label={t('CREATE_ACCOUNT_LABEL_EXPIRES_IN')}
                type="textarea"
                {...{
                  bindInput,
                  validation,
                  model,
                  setProperty,
                }}
                render={(input) => (
                  <DateSelect
                    id={input.id}
                    value={input.model[input.id]}
                    onChange={(value) => this.handleSetExpiresAt(value, input)}
                  />
                )}
              />
              {this.state.validExpiresAt && (
                <div className="invalid-feedback mt-0">{t('FILTERS_STATS_MESSAGE_INTERVAL_DATES')}</div>
              )}
              <Label>{t('CREATE_ACCOUNT_LABEL_PLAN')}</Label>
              <Card body className="p-3">
                <div className="row">
                  <FormGroup className="col-md-6">
                    <Label htmlFor="columnUsersInput">{t('CREATE_ACCOUNT_LABEL_USERS')}</Label>
                    <Input
                      id="columnUsersInput"
                      type="number"
                      data-testid="inputUsersAccount"
                      min="1"
                      {...bindInput('users')}
                    />{' '}
                  </FormGroup>
                </div>
                <div className="row">
                  {TYPE_OPTIONS.filter((s) => ACTIVE_TYPES.includes(s.id)).map(({ id, name }) => (
                    <FormGroup key={id} className="col-md-6">
                      <Label htmlFor={`columnServicesInput${id}`}>{name}</Label>
                      <Input
                        id={`columnServicesInput${id}`}
                        type="number"
                        min={0}
                        name={id}
                        data-testid="inputServicesAccount"
                        {...bindInput(id)}
                      />{' '}
                    </FormGroup>
                  ))}
                </div>
                <div className="row">
                  <FormGroup key="magic-text" className="col-md-6">
                    <Label htmlFor="magic-text">Texto mágico</Label>
                    <Input
                      id="magic-text"
                      disabled={true}
                      type="number"
                      min={0}
                      name="magic-text"
                      data-testid="inputServicesAccount"
                      value={model?.plan?.ai?.['magic-text']}
                    />{' '}
                  </FormGroup>

                  <FormGroup key="summary" className="col-md-6">
                    <Label htmlFor="summary">Resumo inteligente</Label>
                    <Input
                      id="summary"
                      disabled={true}
                      type="number"
                      min={0}
                      name="summary"
                      data-testid="inputServicesAccount"
                      value={model?.plan?.ai?.summary}
                    />{' '}
                  </FormGroup>

                  <FormGroup key="copilot" className="col-md-6">
                    <Label htmlFor="copilot">Copilot</Label>
                    <Input
                      id="copilot"
                      disabled={true}
                      type="number"
                      min={0}
                      name="copilot"
                      data-testid="inputServicesAccount"
                      value={model?.plan?.ai?.copilot}
                    />{' '}
                  </FormGroup>

                  <FormGroup key="transcription" className="col-md-6">
                    <Label htmlFor="transcription">Transcrição de áudios</Label>
                    <Input
                      id="transcription"
                      disabled={true}
                      type="text"
                      min={0}
                      name="transcription"
                      data-testid="inputServicesAccount"
                      value={convertSecondsToTimeString(Number(model?.plan?.ai?.transcription) * 60)}
                    />{' '}
                  </FormGroup>

                  <FormGroup key="csat" className="col-md-6">
                    <Label htmlFor="csat">IA CSAT</Label>
                    <Input
                      id="csat"
                      disabled={true}
                      type="number"
                      min={0}
                      name="csat"
                      data-testid="inputCsatAccount"
                      value={model?.plan?.ai?.csat}
                    />{' '}
                  </FormGroup>

                  <FormGroup key="agent" className="col-md-6">
                    <Label htmlFor="agent">Agente Inteligente</Label>
                    <Input
                      id="agent"
                      disabled={true}
                      type="number"
                      min={0}
                      name="agent"
                      data-testid="inputAgentAccount"
                      value={model?.plan?.ai?.agent}
                    />{' '}
                  </FormGroup>
                </div>

                <InputGroupWrapper
                  id="renewAiDate"
                  label={t('AI_PLAN_RENEWAL_DATE_LABEL')}
                  type="textarea"
                  {...{
                    bindInput,
                    validation,
                    model,
                    setProperty,
                  }}
                  render={(input) => (
                    <DateSelect id={input.id} disabled={true} value={moment(model?.plan?.renewDate)} />
                  )}
                />
                <InputGroup
                  id="hsmLimit"
                  label={t('LABEL_WHATSAPP_BUSINESS_HSM_LIMIT')}
                  type="number"
                  min="0"
                  data-testid="inputHSMLimitAccount"
                  {...{ bindInput, validation }}
                />
              </Card>
              <FormGroup className="mt-3">
                <InputGroup
                  id="managerNumber"
                  label={t('CREATE_ACCOUNT_LABEL_PHONE_PLACEHOLDER').replace('...', '')}
                  data-testid="managerNumberInput"
                  {...{ bindInput, validation }}
                />
              </FormGroup>

              <FormGroup>
                <InputGroup
                  id="idHub360"
                  label="Id Hub360"
                  data-testid="idHub360Input"
                  value={model?.settings?.idHub360}
                  onChange={(e) =>
                    setModel({
                      ...model,
                      settings: { ...model.settings, idHub360: e.target.value },
                    })
                  }
                />
              </FormGroup>

              <InputGroupWrapper
                id="isActive"
                noLabel
                {...{
                  bindInput,
                  validation,
                  model,
                  setProperty,
                  setModel,
                }}
                render={(input) => (
                  <Switch
                    id={input.id}
                    label={t('CREATE_ACCOUNT_LABEL_ENABLE_ACCOUNT')}
                    data-testid="isAccountActive"
                    checked={!!input.model[input.id]}
                    onChange={(e) => {
                      const { checked } = e?.target
                      return input.setModel({
                        ...model,
                        isActive: checked,
                      })
                    }}
                  />
                )}
              />

              <InputGroupWrapper
                id="enableHsmStats"
                noLabel
                {...{
                  bindInput,
                  validation,
                  model,
                  setProperty,
                  setModel,
                }}
                render={(input) => (
                  <Switch
                    id={input.id}
                    label={t('CREATE_ACCOUNT_LABEL_ENABLE_HSM_STATISTICS')}
                    data-testid="enableHsmStats"
                    checked={!!input?.model?.settings?.enableHsmStats}
                    onChange={(e) => {
                      const { checked } = e?.target
                      return input.setModel({
                        ...model,
                        settings: {
                          ...model.settings,
                          enableHsmStats: checked,
                        },
                      })
                    }}
                  />
                )}
              />
              <InputGroupWrapper
                id="isUserEmailRequired"
                noLabel
                {...{
                  bindInput,
                  validation,
                  model,
                  setProperty,
                  setModel,
                }}
                render={(input) => (
                  <Switch
                    id={input.id}
                    label={t('LABEL_VALIDATE_USER_EMAIL')}
                    data-testid="emailRequiredAccount"
                    checked={!!input?.model?.settings?.isUserEmailRequired}
                    onChange={(e) => {
                      const { checked } = e?.target
                      return input.setModel({
                        ...model,
                        settings: {
                          ...model.settings,
                          isUserEmailRequired: checked,
                        },
                      })
                    }}
                  />
                )}
              />

              <InputGroupWrapper
                id="isBlockListActive"
                noLabel
                {...{
                  bindInput,
                  validation,
                  model,
                  setProperty,
                  setModel,
                }}
                render={(input) => (
                  <Switch
                    id={input.id}
                    label={t('CREATE_ACCOUNT_LABEL_ENABLE_BLACKLIST')}
                    data-testid="blackListActive"
                    checked={!!input?.model?.settings?.isBlockListActive}
                    onChange={(e) => {
                      const { checked } = e?.target
                      return input.setModel({
                        ...model,
                        settings: {
                          ...model.settings,
                          isBlockListActive: checked,
                        },
                      })
                    }}
                  />
                )}
              />

              <InputGroupWrapper
                id="isCampaignActive"
                noLabel
                {...{
                  bindInput,
                  validation,
                  model,
                  setProperty,
                  setModel,
                }}
                render={(input) => (
                  <Switch
                    id={input.id}
                    label={t('CREATE_ACCOUNT_LABEL_ENABLE_CAMPAIGN')}
                    data-testid="switchAccount"
                    checked={!!input.model[input.id]}
                    onChange={(e) => {
                      const { checked } = e?.target
                      return input.setModel({
                        ...model,
                        isCampaignActive: checked,
                      })
                    }}
                  />
                )}
              />
              {model?.isCampaignActive ? (
                <InputGroupWrapper>
                  <Label>{t('CREATE_ACCOUNT_LABEL_SERVICE_CAMPAIGN')}</Label>
                  <Card body className="p-3">
                    <div className="row">
                      <Col>
                        {model &&
                          model?.settings &&
                          Object.entries(model.settings?.campaign).map(
                            ([key, idx]) =>
                              key !== 'auto-pause-mode' && (
                                <InputGroupWrapper
                                  id={key}
                                  key={key}
                                  noLabel
                                  {...{
                                    bindInput,
                                    validation,
                                    model,
                                    setProperty,
                                    setModel,
                                  }}
                                  render={(input) => (
                                    <Switch
                                      id={input.id + key}
                                      label={key}
                                      data-testid={input.id}
                                      checked={!!input.model.settings.campaign[input.id]}
                                      onChange={(e) => {
                                        const { checked } = e?.target
                                        return input.setModel({
                                          ...model,
                                          settings: {
                                            ...model.settings,
                                            campaign: {
                                              ...model.settings?.campaign,
                                              [key]: checked,
                                            },
                                          },
                                        })
                                      }}
                                    />
                                  )}
                                />
                              ),
                          )}
                      </Col>
                    </div>
                  </Card>
                </InputGroupWrapper>
              ) : (
                ''
              )}
              <InputGroupWrapper>
                <Label>{t('CREATE_ACCOUNT_LABEL_FEATURES_FLAGS')}</Label>
                <Card body className="p-3">
                  <div className="row">
                    <Col>
                      {this.state.flags &&
                        this.state.flags.map((flag, index) => (
                          <Row key={index} className={`p-1 ${flag.value === 'internal-chat' ? 'mt-3' : ''}`}>
                            <InputGroupWrapper
                              id={flag.value}
                              noLabel
                              {...{
                                bindInput,
                                validation,
                                model,
                                setProperty,
                                setModel,
                              }}
                              render={(input) => {
                                const flags = input.model.settings?.flags || {}
                                const flagEnabled = !!flags[flag.value]
                                const isBotsV3 = flag.value === 'bots-v3'
                                const isEnableNode = flag.value === 'enable-bots-v3-ai-node'
                                const isSmartSummary = flag.value === 'enable-smart-summary'
                                const isAICSAT = flag.value === 'enable-smart-csat-score'

                                return (
                                  <>
                                    <Switch
                                      id={flag.value}
                                      label={this.handleRenderLabelFlag(flag.value) || flag.name}
                                      data-testid={flag.value}
                                      checked={flagEnabled}
                                      disabled={isEnableNode && !flags['bots-v3']}
                                      onChange={(e) => {
                                        const { checked } = e?.target
                                        if (isBotsV3) {
                                          const updatedFlags = {
                                            ...flags,
                                            'bots-v3': checked,
                                            ...(checked ? {} : { 'enable-bots-v3-ai-node': false }),
                                          }
                                          return input.setModel({
                                            ...model,
                                            settings: {
                                              ...model.settings,
                                              flags: updatedFlags,
                                            },
                                          })
                                        }
                                        return input.setModel({
                                          ...model,
                                          settings: {
                                            ...model.settings,
                                            flags: {
                                              ...model.settings?.flags,
                                              [flag.value]: checked,
                                            },
                                          },
                                        })
                                      }}
                                    />
                                    {isSmartSummary && flagEnabled && (
                                      <>
                                        <div style={{ marginTop: '20px' }}>
                                          <InputGroup
                                            id="promptAiTransfer"
                                            label={t('LABEL_PROMPT_TRANSFER')}
                                            data-testid="promptAiTransferInput"
                                            value={model?.promptAiTransfer ?? t('LABEL_PROMPT_DEFAULT_TRANSFER')}
                                            onChange={(e) => {
                                              const value = e.target.value.trim().length > 0 ? e.target.value : ''
                                              setModel({
                                                ...model,
                                                promptAiTransfer: value,
                                              })
                                            }}
                                            style={{ width: '400px' }}
                                          />
                                          <InputGroup
                                            id="promptAiFinalize"
                                            label={t('LABEL_PROMPT_FINALIZE')}
                                            data-testid="promptAiFinalizeInput"
                                            value={model?.promptAiFinalize ?? t('LABEL_PROMPT_DEFAULT_FINALIZE')}
                                            onChange={(e) => {
                                              const value = e.target.value.trim().length > 0 ? e.target.value : ''
                                              setModel({
                                                ...model,
                                                promptAiFinalize: value,
                                              })
                                            }}
                                            style={{ width: '400px' }}
                                          />
                                        </div>
                                      </>
                                    )}
                                    {flagEnabled && isAICSAT && (
                                      <>
                                        <div style={{ marginTop: '20px' }}>
                                          <InputGroup
                                            id="prompt-ai-csat"
                                            label={t('LABEL_PROMPT_AI_CSAT')}
                                            data-testid="prompt-ai-csat-input"
                                            value={model?.promptAiCsat ?? t('LABEL_PROMPT_DEFAULT_AI_CSAT')}
                                            onChange={(e) => {
                                              const value = e.target.value ?? ''
                                              setModel({
                                                ...model,
                                                promptAiCsat: value,
                                              })
                                            }}
                                            style={{ width: '400px' }}
                                          />
                                        </div>
                                      </>
                                    )}
                                  </>
                                )
                              }}
                            />
                          </Row>
                        ))}
                    </Col>
                  </div>
                </Card>
              </InputGroupWrapper>
            </ModalBody>
            <ModalFooter>
              <Button className="cancel" data-testid="cancelAccount" type="button" onClick={this.close}>
                {t('common:FORM_ACTION_CANCEL')}
              </Button>

              <LoadingButton
                className="confirm"
                type="submit"
                data-testid="submitAccount"
                disabled={isLoading || this.state.validExpiresAt}
                onClick={this.handleSubmit}
                isLoading={isLoading}
              >
                {t('common:FORM_ACTION_SAVE')}
              </LoadingButton>
            </ModalFooter>
          </ModalDigisac>
        </Form>
        {this.state.isModalError && (
          <SweetAlert
            error={this.state.isModalError}
            title={t('common:THIS_ACTION_COULD_NOT_BE_PERFORMED')}
            onConfirm={() => this.setState({ isModalError: !this.state.isModalError })}
          >
            {t('FORM_ACTION_BLOCK_QTD_USER')}
          </SweetAlert>
        )}
      </div>
    )
  }
}

AccountsForm.propTypes = {
  account: PropTypes.object,
  model: PropTypes.object,
  validation: PropTypes.object,
  bindInput: PropTypes.func,
  isLoading: PropTypes.bool,
  setProperty: PropTypes.func,
  saveForm: PropTypes.func,
  history: PropTypes.object,
  previousMatch: PropTypes.object,
  fetchOneAccount: PropTypes.func,
  match: PropTypes.object,
  setModel: PropTypes.func,
}

export default withTranslation(['accountPage', 'common'])(reformed()(withValidation()(AccountsForm)))
