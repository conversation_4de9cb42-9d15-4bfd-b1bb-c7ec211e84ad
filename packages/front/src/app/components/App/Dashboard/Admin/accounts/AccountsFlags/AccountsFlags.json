{"flags": [{"name": "Construtor de bot - Fluxo Antigo (v2)", "value": "bots-v2"}, {"name": "Construtor de bot - Fluxo Novo (v3 - Em teste)", "value": "bots-v3"}, {"name": "Habilitar bloco de Agente Inteligente no bot v3", "value": "enable-bots-v3-ai-node"}, {"name": "Habilitar Distribuição Automática de Chamados", "value": "distribution"}, {"name": "Abas por atendente e por departamento nas estatísticas de atendimento", "value": "by-user-and-by-department-tabs"}, {"name": "Chat <PERSON>", "value": "internal-chat"}, {"name": "Controle de ausência", "value": "absence-management"}, {"name": "Inativador de webhooks inválidos", "value": "invalid-webhooks-inactivator"}, {"name": "Desabilitar limite no envio de templates", "value": "disable-hsm-limit"}, {"name": "Habilitar transcrições de áudio", "value": "enable-audio-transcription"}, {"name": "Habilitar resumo inteligente", "value": "enable-smart-summary"}, {"name": "Habilitar Api IA Interna", "value": "enable-others-ai"}, {"name": "Habilitar busca de mensagens", "value": "search-messages"}, {"name": "Habilitar funil de vendas", "value": "enable-sales-funnel"}, {"name": "Habilitar Bloqueio de interação fora do horário por DDD", "value": "use-block-message-rules-by-service"}, {"name": "Habilitar texto mágico", "value": "enable-magic-text"}, {"name": "Habilitar CSAT via IA", "value": "enable-smart-csat-score"}, {"name": "Habilitar formato de onda em mensagens de áudio", "value": "enable-audio-waveform"}, {"name": "Habilitar copiloto", "value": "enable-copilot"}, {"name": "Habilitar SSO", "value": "enable-sso"}]}