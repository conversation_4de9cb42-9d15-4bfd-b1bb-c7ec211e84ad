import React, { use<PERSON>allback, useEffect, useState } from 'react'
import Helmet from 'react-helmet'
import find from 'lodash/find'
import pick from 'lodash/pick'
import get from 'lodash/get'
import SweetAlert from 'react-bootstrap-sweetalert'
import { withRouter, useParams } from 'react-router-dom'
import { useMappedState } from 'redux-react-hook'
import { Form, ModalBody, ModalHeader } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { Info } from 'lucide-react'
import { required } from '../../../../../utils/validator/validators'
import toast from '../../../../../utils/toast'
import { isAllBlankSpace } from '../../../../../utils/isAllBlankSpace'
import InputGroup, { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import DepartmentsSelect from '../../../../common/connected/DepartmentsSelect/DepartmentsSelectContainer'
import Select from '../../../../common/unconnected/Select'
import { hasPermission } from '../../../../common/connected/IfUserCan/IfUserCan'
import { selectors as authSelectors } from '../../../../../modules/auth'
import useFormController from '../../../../../hooks/useFormController'
import { useCreateService, useFetchOneService, useUpdateService } from '../../../../../resources/service/requests'
import ModelType from './ModelType'
import Switches from './Switches'
import { TYPE_OPTIONS, EMAIL_PROVIDERS } from '../constants'
import { ModalDigisac, GroupInput } from '../../../styles/common'
import { IconDepartment } from '../../../../common/unconnected/IconDigisac'
import { PrimaryColor } from '../../../styles/colors'
import { IconsService } from '../IconsService'
import Switch from '../../../../common/unconnected/Switch/Switch'
import { Button } from '../../../../common/unconnected/ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../../../common/unconnected/ui/tooltip'
import { hasServiceBotLimitations } from '../../../../../utils/bot/hasServiceBotLimitations'

export const formatToApi = (service) => ({
  ...pick(service, [
    'id',
    'name',
    'readReceipts',
    'markComposingBeforeSend',
    'shouldOpenTicketForGroups',
    'blockMessageRulesActive',
    'unblockByReceiveMessage',
    'lowLevel',
    'username',
    'userKey',
    'passKey',
    'pageSync',
    'imagesSync',
    'lastRequest',
    'token',
    'nameWebchat',
    'filePerfilWebchat',
    'phoneWhatsapp',
    'nameTelegram',
    'googleId',
    'accountKey',
    'archivedAt',
    'cloudApi',
  ]),
  hub360PartnerId:
    (get(service, 'providerType')?.id ?? get(service, 'providerType')) === '360Dialog'
      ? (service.hub360PartnerId ?? '5urmEBPA')
      : null,
  tokenSandBox360: service.useSandBox360 && service.tokenSandBox360 ? service.tokenSandBox360 : null,
  providerType: get(service, 'providerType')?.id || get(service, 'providerType'),
  apiUrl: undefined,
  archivedAt: service.archivedAt,
  botId: service.bot && service.bot.id,
  defaultDepartmentId: service.defaultDepartment && service.defaultDepartment.id,
  type: service.type && service.type.id,
  token: service.token,
  appName: service?.appName,
  apiKey: service?.apiKey,
  phone: service?.phone,
  driverId: service.driverId,
  pageId: service.pageId,
  serviceType: get(service, 'service.id'),
  email: {
    includeSpamTrash: service.includeSpamTrash,
    service: service.service && service.service.id,
    smtp: {
      host: service.smtpHost,
      port: service.smtpPort,
      ignoreTLS: service.ignoreTLS,
      requireTLS: service.requireTLS,
    },
    imap: {
      host: service.imapHost,
      port: service.imapPort,
    },
    auth: {
      user: service.emailUser,
      pass: service.emailPassword || undefined,
    },
    ...(service.service?.id === 'Hotmail' && {
      hotmail: {
        clientId: service.clientId,
        clientSecret: service.clientSecret,
      },
    }),
    ...(service.service?.id === 'GSuite' && {
      gsuite: {
        privateKey: service.privateKey ? get(service, 'privateKey', '').replace(/\\n/g, '\n') : undefined,
        serviceClient: service.clientId,
        service: service.serviceEmail,
      },
    }),
  },
  webchat: {
    name: service.nameWebchat,
    phone: service.phoneWhatsapp,
    telegram: service.nameTelegram,
    isOpenForm: service.isOpenForm,
    isOpenChat: service.isOpenChat,
    file: service.filePerfilWebchat,
  },
  googleBusiness: {
    fileName: service.fileName,
    jsonFileData: service.jsonFileData,
    sync: service.sync,
  },
  reclameAqui: {
    user: service.userKey,
    password: service.passKey,
    pageSync: service.pageSync,
    imagesSync: service.imagesSync,
    lastRequest: service.lastRequest,
  },
  microsoft: service.microsoft,
})

export const formatFromApi = (service) => ({
  name: '',
  bot: service && service.bot,
  defaultDepartment: service && service.defaultDepartment,
  username: null,
  token: null,
  readReceipts: false,
  blockMessageRulesActive: false,
  unblockByReceiveMessage: false,
  markComposingBeforeSend: false,
  shouldOpenTicketForGroups: false,
  archivedAt: null,
  type: find(TYPE_OPTIONS, (o) => service && o.id === service.type) || TYPE_OPTIONS[0],
  ...pick(service, ['name', 'bot', 'defaultDepartment', 'username', 'token', 'driverId', 'apiUrl']),
  ...pick((service || {}).settings, [
    'readReceipts',
    'markComposingBeforeSend',
    'shouldOpenTicketForGroups',
    'blockMessageRulesActive',
    'unblockByReceiveMessage',
    'tokenSandBox360',
    'lowLevel',
    'cloudApi',
  ]),
  useSandBox360: !!(service || {})?.settings?.tokenSandBox360,
  smtpHost: get(service, 'data.smtp.host'),
  requireTLS: get(service, 'data.smtp.requireTLS'),
  ignoreTLS: get(service, 'data.smtp.ignoreTLS'),
  passKey: get(service, 'data.reclameAqui.password'),
  userKey: get(service, 'data.reclameAqui.user'),
  pageSync: get(service, 'data.reclameAqui.pageSync'),
  imagesSync: get(service, 'data.reclameAqui.imagesSync'),
  lastRequest: get(service, 'data.reclameAqui.lastRequest'),
  smtpPort: get(service, 'data.smtp.port'),
  imapHost: get(service, 'data.imap.host'),
  imapPort: get(service, 'data.imap.port'),
  emailUser: get(service, 'data.auth.user'),
  includeSpamTrash: get(service, 'data.includeSpamTrash'),
  service: find(EMAIL_PROVIDERS, (e) => e.id === get(service, 'data.service', '')),
  nameWebchat: get(service, 'data.webchat.name'),
  fileName: get(service, 'data.googleBusiness.fileName'),
  sync: get(service, 'data.googleBusiness.sync'),
  phoneWhatsapp: get(service, 'data.webchat.phone'),
  nameTelegram: get(service, 'data.webchat.telegram'),
  isOpenForm: get(service, 'data.webchat.isOpenForm'),
  isOpenChat: get(service, 'data.webchat.isOpenChat'),
  accountKey: get(service, 'data.accountKey'),
  appName: get(service, 'data.appName'),
  apiKey: get(service, 'data.apiKey'),
  phone: get(service, 'data.phone'),
  providerType: get(service, 'data.providerType'),
  hub360PartnerId: get(service, 'data.hub360PartnerId'),
})

const initialModel = {
  name: '',
  service: null,
  bot: null,
  defaultDepartment: null,
  readReceipts: false,
  username: null,
  userKey: '',
  passKey: '',
  token: null,
  driverId: null,
  nameWebchat: null,
  filePerfilWebchat: {},
  phoneWhatsapp: null,
  nameTelegram: null,
  isOpenForm: null,
  isOpenChat: null,
  archivedAt: null,
  markComposingBeforeSend: false,
  shouldOpenTicketForGroups: false,
  blockMessageRulesActive: false,
  unblockByReceiveMessage: false,
  jsonFileData: null,
  fileName: '',
  requireTLS: false,
  ignoreTLS: false,
}

const useController = ({ match, history, serviceType }) => {
  const { id } = match.params
  const { t } = useTranslation('common', 'services')

  const requiredValidation = [required, t('REQUIRED_FIELD')]
  const blankSpaceValidation = [isAllBlankSpace, t('common:INVALID_FIELD')]

  const validationRules = {
    name: [requiredValidation, blankSpaceValidation],
    type: [requiredValidation],
    ...(['webchat'].includes(serviceType) && {
      nameWebchat: [requiredValidation, blankSpaceValidation],
    }),
    apiKey: [
      [
        (value) =>
          value.inputData.type?.id !== 'whatsapp-business' || value.inputData.providerType?.id !== 'gupshup'
            ? true
            : id || value.inputData.apiKey,
        t('REQUIRED_FIELD'),
      ],
    ],
    appName: [
      [
        (value) =>
          value.inputData.type?.id !== 'whatsapp-business' || value.inputData.providerType?.id !== 'gupshup'
            ? true
            : id || value.inputData.appName,
        t('REQUIRED_FIELD'),
      ],
    ],
    phone: [
      [
        (value) =>
          value.inputData.type?.id !== 'whatsapp-business' || value.inputData.providerType?.id !== 'gupshup'
            ? true
            : id || value.inputData.phone,
        t('REQUIRED_FIELD'),
      ],
    ],
    ...(['webchat'].includes(serviceType) && {
      nameWebchat: [requiredValidation, blankSpaceValidation],
    }),
  }

  const onExit = useCallback(() => {
    setTimeout(() => history.push('/services', { refresh: true }), 300)
  }, [history])

  const user = useMappedState(authSelectors.getUser)

  const account = useMappedState(authSelectors.getUserAccount)

  const controller = useFormController({
    id,
    initialModel,
    validationRules,
    formatToApi,
    formatFromApi,
    onExit,
    query: {
      include: ['defaultDepartment', { model: 'bot', include: ['currentBotVersion'] }],
    },
    useCreateOne: useCreateService,
    useUpdateOne: useUpdateService,
    useFetchOne: useFetchOneService,
  })

  return {
    account,
    user,
    ...controller,
  }
}

const ServicesForm = ({ match, history }) => {
  const { type } = useParams()

  const [serviceType, setServiceType] = useState(type)

  const {
    submit,
    exit,
    isModalOpen,
    isEditing,
    isAlertShowing,
    closeAlert,
    isLoading,
    error,
    account,
    user,
    model,
    setModel,
    bindInput,
    setProperty,
    validation,
  } = useController({ match, history, serviceType })

  const { t } = useTranslation(['servicesPage', 'usersPage', 'common'])

  useEffect(() => {
    if (type) {
      const selectedType = TYPE_OPTIONS.find((item) => item.id === type)
      setProperty('type', selectedType)
    }
  }, [])

  useEffect(() => {
    if (model.type?.id) {
      setServiceType(model.type?.id)
    }
  }, [model.type])

  const title = `${isEditing ? t('common:LABEL_EDITING') : t('common:LABEL_CREATING')} ${t('TITLE_SERVICE')}`

  const errorCode = error?.response?.status

  const getErrorMessage = (error) => {
    if (error.response?.status === 402) {
      return t('usersPage:CREATE_USER_PLAN_LIMIT_REACHED')
    }

    if (error?.response?.status === 409) {
      return t('MODAL_DUPLICATE_NAME')
    }
    return t('LABEL_MODAL_ERROR')
  }

  const ticketsEnabled = !!(user && user.account.settings.ticketsEnabled)

  const useBlockMessageRulesByService = !!user?.account?.settings?.flags?.['use-block-message-rules-by-service']

  const options = TYPE_OPTIONS.filter((item) => user.account.plan.services[`${item.id}`])

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (model.type?.id === 'webchat') {
      return history.push('/services/create/webchat')
    }

    validation.setTouched('name')
    validation.setTouched('token')

    if (model.type?.id === 'webchat') {
      validation.setTouched('nameWebchat')
    }

    const isValid = await validation.validateAll()

    if (!isValid) return

    if (!navigator.onLine) {
      return toast.warn(t('common:MESSAGE_ERROR_NETWORK'))
    }

    return submit()
  }

  const redirectToServices = () => history.push('/services')

  // caso a conta não tenha permissão para criar o tipo de conexão
  if (account.plan.services[type] === 0) {
    return (
      <SweetAlert type="warning" error title={t('common:MESSAGE_ATTENTION')} onConfirm={redirectToServices}>
        {t('TEXT_MODAL_DONT_HAVE_PERMISSION')}
      </SweetAlert>
    )
  }

  return (
    <>
      <Helmet title={title} />

      <Form onSubmit={handleSubmit} data-testid="create-accounts">
        <ModalDigisac isOpen={isModalOpen} toggle={exit} autoFocus={false}>
          <ModalHeader toggle={exit}>{title}</ModalHeader>
          <ModalBody>
            <InputGroupWrapper
              id="type"
              label={t('LABEL_MODAL_TYPE')}
              render={({ id }) => (
                <GroupInput withIcon>
                  <Select
                    icon={IconsService(model?.type?.id, '22', '22')}
                    className="filter-type"
                    id={id}
                    onlyValue
                    value={model.type}
                    onChange={(value) => setProperty('type', value)}
                    options={options}
                    isDisabled={!!isEditing}
                  />
                </GroupInput>
              )}
            />

            {model.type?.id !== 'webchat' && (
              <>
                <InputGroup
                  id="name"
                  label={t('LABEL_MODAL_NAME')}
                  {...{ bindInput, validation }}
                  data-testid="connections-input-name"
                />

                {ticketsEnabled && (
                  <InputGroupWrapper
                    id="defaultDepartment"
                    label={t('LABEL_MODAL_STANDARD_DEPARTMENT')}
                    {...{
                      bindInput,
                      validation,
                      model,
                      setProperty,
                    }}
                    render={(input) => (
                      <GroupInput withIcon>
                        <DepartmentsSelect
                          icon={<IconDepartment fill={PrimaryColor} width="25" height="25" />}
                          className="default-department-select"
                          stateId="serviceFormDefaultDepartmentSelect"
                          id={input.id}
                          value={input.model[input.id]}
                          onChange={(value) => input.setProperty(input.id, value)}
                          onBlur={() => input.validation.setTouched(input.id)}
                          hideArchived
                        />
                      </GroupInput>
                    )}
                  />
                )}

                <ModelType
                  type={model && model.type && model.type.id}
                  isEditing={isEditing}
                  bindInput={bindInput}
                  validation={validation}
                  {...{
                    bindInput,
                    validation,
                    model,
                    setModel,
                    setProperty,
                    isEditing,
                  }}
                />

                <InputGroupWrapper
                  id="shouldOpenTicketForGroups"
                  noLabel
                  {...{
                    bindInput,
                    validation,
                    model,
                    setProperty,
                  }}
                  render={(input) => (
                    <Switches
                      condition={['whatsapp', 'telegram'].includes(model.type?.id)}
                      bindInput={bindInput}
                      validation={validation}
                      model={model}
                      setProperty={setProperty}
                      user={user}
                    />
                  )}
                />

                {user.isSuperAdmin &&
                  model?.providerType &&
                  (model?.providerType === '360Dialog' || model?.providerType.id === '360Dialog') && (
                    <InputGroupWrapper
                      key="useSandBox360"
                      id="useSandBox360"
                      noLabel
                      {...{
                        bindInput,
                        validation,
                        model,
                        setProperty,
                      }}
                      render={(input) => (
                        <Switch
                          id={input.id}
                          label={t('LABEL_SWITCH_USE_SANDBOX')}
                          checked={input.model.useSandBox360}
                          onChange={(e) => input.setProperty(input.id, e.target.checked)}
                        />
                      )}
                    />
                  )}

                {['whatsapp-business', 'whatsapp', 'sms-wavy'].includes(model.type?.id) &&
                  useBlockMessageRulesByService && (
                    <InputGroupWrapper
                      key="blockMessageRulesActive"
                      id="blockMessageRulesActive"
                      noLabel
                      {...{
                        bindInput,
                        validation,
                        model,
                        setProperty,
                      }}
                      render={(input) => (
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <Switch
                            id={input.id}
                            checked={input.model.blockMessageRulesActive}
                            onChange={(e) => {
                              input.setProperty(input.id, e.target.checked)

                              if (!e.target.checked) {
                                setProperty('unblockByReceiveMessage', false)
                              }
                            }}
                          />
                          <label htmlFor={input.id} style={{ marginLeft: '8px', marginBottom: '4px' }}>
                            {t('LABEL_SWITCH_BLOCK_MESSAGE_RULES_ACTIVE')}

                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Info
                                    style={{
                                      color: '#324B7D',
                                      marginLeft: '4px',
                                      marginBottom: '6px',
                                    }}
                                    size={14}
                                  />
                                </TooltipTrigger>
                                <TooltipContent>{t('LABEL_TOOLTIP_BLOCK_MESSAGE_RULES_ACTIVE')}</TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </label>
                        </div>
                      )}
                    />
                  )}

                {['whatsapp-business', 'whatsapp', 'sms-wavy'].includes(model.type?.id) &&
                  useBlockMessageRulesByService &&
                  model.blockMessageRulesActive && (
                    <InputGroupWrapper
                      key="unblockByReceiveMessage"
                      id="unblockByReceiveMessage"
                      noLabel
                      {...{
                        bindInput,
                        validation,
                        model,
                        setProperty,
                      }}
                      render={(input) => (
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <Switch
                            id={input.id}
                            checked={input.model.unblockByReceiveMessage}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                          <label htmlFor={input.id} style={{ marginLeft: '8px', marginBottom: '4px' }}>
                            {t('LABEL_SWITCH_UNBLOCK_BY_RECEIVE_MESSAGE')}
                          </label>
                        </div>
                      )}
                    />
                  )}
              </>
            )}
          </ModalBody>
          <div style={{ display: 'flex', justifyContent: 'center', gap: '16px', padding: '16px' }}>
            <Button
              type="button"
              variant="outline"
              onClick={exit}
              style={{ width: '100%' }}
              data-testid="connections-button-cancel"
            >
              {t('common:FORM_ACTION_CANCEL')}
            </Button>

            <Button
              type="submit"
              onClick={handleSubmit}
              disabled={
                isLoading ||
                !hasPermission(user.permissions, ['services.create', 'services.update'], true) ||
                !!hasServiceBotLimitations(model?.type?.id, model?.bot?.settings?.limitations)
              }
              style={{ width: '100%' }}
              data-testid="connections-button-save"
            >
              {model.type?.id === 'webchat' ? t('common:FORM_ACTION_CONTINUE') : t('common:FORM_ACTION_SAVE')}
            </Button>
          </div>
        </ModalDigisac>
      </Form>

      {isAlertShowing && (
        <SweetAlert
          type={errorCode === 500 ? 'error' : 'warning'}
          error
          title={t('common:MESSAGE_ATTENTION')}
          onConfirm={closeAlert}
        >
          {getErrorMessage(error)}
        </SweetAlert>
      )}
    </>
  )
}

export default withRouter(ServicesForm)
