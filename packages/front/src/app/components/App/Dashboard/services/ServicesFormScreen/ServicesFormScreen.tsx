import React, { useEffect, useState } from 'react'
import Helmet from 'react-helmet'
import { Link, useParams } from 'react-router-dom'
import pick from 'lodash/pick'
import get from 'lodash/get'
import { Form } from 'reactstrap'
import Button from '../../../../common/unconnected/Button'
import { TextColor, PrimaryColor } from '../../../styles/colors'
import InputGroup, { InputGroupWrapper } from '../../../../common/unconnected/InputGroup'
import DepartmentsSelect from '../../../../common/connected/DepartmentsSelect'
import Select from '../../../../common/unconnected/Select'
import { GroupInput } from '../../../styles/common'
import SweetAlert from 'react-bootstrap-sweetalert'
import { useMappedState } from 'redux-react-hook'
import { useTranslation } from 'react-i18next'
import { required } from '../../../../../utils/validator/validators'
import toast from '../../../../../utils/toast'
import { isAllBlankSpace } from '../../../../../utils/isAllBlankSpace'
import { selectors as authSelectors } from '../../../../../modules/auth'
import useFormController from '../../../../../hooks/useFormController'
import { useCreateService, useFetchOneService, useUpdateService } from '../../../../../resources/service/requests'
import { IconDepartment } from '../../../../common/unconnected/IconDigisac'
import { IconsService } from '../IconsService'
import { TYPE_OPTIONS } from '../constants'
import * as S from './styles'
import { RenderBotField } from '../ServicesForm/ModelType'
import { WebChatPreview } from './components/WebChatPreview'
import ServicesFormConfig from './ServicesFormConfig'
import ServicesCustomForm from './ServicesCustomForm'

export const formatToApi = (service) => ({
  ...pick(service, ['id', 'name', 'nameWebchat']),
  botId: service.bot && service.bot.id,
  defaultDepartmentId: service.defaultDepartment && service.defaultDepartment.id,
  type: 'webchat',
  archivedAt: service.archivedAt,
  webchat: {
    name: service.nameWebchat,
    phone: service.phoneWhatsapp,
    telegram: service.nameTelegram,
    isOpenForm: false,
    desktopChatOpen: service.desktopChatOpen,
    mobileChatOpen: service.mobileChatOpen,
    getClientData: service.getClientData,
    closeTicketByClient: service.closeTicketByClient,
    file: service.filePerfilWebchat,
    googleId: service.googleId,
    phoneClient: service.phone,
    phoneClientRequired: service.phoneRequired,
    customField: { ...pick(service?.customField, ['id', 'name']) },
    customFieldRequired: service?.customFieldRequired,
    clientName: true,
    clientNameRequired: true,
    custom: {
      webchatBalloon: service?.webchatBalloon,
      webchatBalloonText: service?.webchatBalloonText,
      webchatColor: service?.webchatColor,
      logo: service?.logo?.id,
      profilePhoto: service?.profilePhoto?.id,
      webchatIcon: service?.webchatIcon?.id,
      typeIcon: service?.typeIcon,
    },
  },
})

export const formatFromApi = (service) => ({
  ...pick(service, ['name', 'bot', 'defaultDepartment', 'customField', 'logo', 'profilePhoto', 'webchatIcon']),
  archivedAt: null,
  nameWebchat: get(service, 'data.webchat.name'),
  phoneWhatsapp: get(service, 'data.webchat.phone'),
  nameTelegram: get(service, 'data.webchat.telegram'),
  googleId: get(service, 'data.webchat.googleId'),
  desktopChatOpen: get(service, 'data.webchat.desktopChatOpen'),
  mobileChatOpen: get(service, 'data.webchat.mobileChatOpen'),
  phone: get(service, 'data.webchat.phoneClient'),
  phoneRequired: get(service, 'data.webchat.phoneClientRequired'),
  customFieldRequired: get(service, 'data.webchat.customFieldRequired'),
  checkCustomField: get(service, 'data.webchat.customField.id') ? true : false,
  getClientData: get(service, 'data.webchat.getClientData') || false,
  closeTicketByClient: get(service, 'data.webchat.closeTicketByClient') || false,
  webchatBalloon: get(service, 'data.webchat.custom.webchatBalloon') || false,
  webchatBalloonText: get(service, 'data.webchat.custom.webchatBalloonText'),
  webchatColor: get(service, 'data.webchat.custom.webchatColor') || '#455778',
  typeIcon: get(service, 'data.webchat.custom.typeIcon'),
})

const initialModel = {
  name: '',
  service: null,
  bot: null,
  customField: {},
  getClientData: false,
  closeTicketByClient: false,
  archivedAt: null,
  defaultDepartment: null,
  nameWebchat: null,
  filePerfilWebchat: {},
  phoneWhatsapp: null,
  nameTelegram: null,
  webchatBalloon: false,
  webchatBalloonText: '',
  webchatColor: '#455778',
  logo: null,
  profilePhoto: null,
  webchatIcon: null,
  typeIcon: 'whatsapp',
}

const useController = ({ match, history }) => {
  const { id } = match.params
  const { t } = useTranslation('common')

  const requiredValidation = [required, t('REQUIRED_FIELD')]
  const blankSpaceValidation = [isAllBlankSpace, t('INVALID_FIELD')]

  const validationRules = {
    name: [requiredValidation, blankSpaceValidation],
    nameWebchat: [requiredValidation, blankSpaceValidation],
  }

  const user = useMappedState(authSelectors.getUser)

  const account = useMappedState(authSelectors.getUserAccount)

  const onExit = () => {
    history.push('/services', { refresh: true })
  }

  const controller = useFormController({
    id,
    initialModel,
    validationRules,
    formatToApi,
    formatFromApi,
    onExit,
    query: {
      include: ['defaultDepartment', 'bot'],
    },
    useCreateOne: useCreateService,
    useUpdateOne: useUpdateService,
    useFetchOne: useFetchOneService,
  })

  return {
    account,
    user,
    ...controller,
  }
}

export const ServicesFormScreen = ({ match, history }) => {
  const { type } = useParams()

  const {
    submit,
    isLoading,
    isEditing,
    error,
    account,
    user,
    model,
    closeAlert,
    isAlertShowing,
    setModel,
    bindInput,
    setProperty,
    validation,
  } = useController({ match, history })

  const { t } = useTranslation(['servicesPage', 'usersPage', 'common'])

  const [openModalGetOut, setOpenModalGetOut] = useState(false)
  const [path, setPath] = useState('')

  const errorCode = error?.response?.status

  const getErrorMessage = (errorMessage) => {
    if (errorMessage.response?.status === 402) {
      return t('usersPage:CREATE_USER_PLAN_LIMIT_REACHED')
    }

    if (errorMessage?.response?.status === 409) {
      return t('MODAL_DUPLICATE_NAME')
    }
    return t('LABEL_MODAL_ERROR')
  }

  useEffect(() => {
    if (type) {
      const selectedType = TYPE_OPTIONS.find((item) => item.id === type)
      setProperty('type', selectedType)
    }
  }, [])

  const title = `${isEditing ? t('common:LABEL_EDITING') : t('common:LABEL_CREATING')} ${t('TITLE_SERVICE')}`

  const options = TYPE_OPTIONS.filter((item) => user.account.plan.services[`${item.id}`])

  const handleSubmit = async (e) => {
    e.preventDefault()

    validation.setTouched('name')
    validation.setTouched('nameWebchat')

    const isValid = await validation.validateAll()

    if (!isValid) return

    if (!navigator.onLine) {
      return toast.warn(t('common:MESSAGE_ERROR_NETWORK'))
    }

    if (model?.logo?.id && model?.logo?.urlToUpload) {
      await fetch(model?.logo?.urlToUpload, {
        method: 'PUT',
        body: model?.logo?.blob,
        headers: {
          'Content-Type': model?.logo?.mimetype,
        },
      })
    }

    if (model?.webchatIcon?.id && model?.webchatIcon?.urlToUpload) {
      console.log(model?.webchatIcon)

      await fetch(model?.webchatIcon?.urlToUpload, {
        method: 'PUT',
        body: model?.webchatIcon?.blob,
        headers: {
          'Content-Type': model?.webchatIcon?.mimetype,
        },
      })
    }

    if (model?.profilePhoto?.id && model?.profilePhoto?.urlToUpload) {
      await fetch(model?.profilePhoto?.urlToUpload, {
        method: 'PUT',
        body: model?.profilePhoto?.blob,
        headers: {
          'Content-Type': model?.profilePhoto?.mimetype,
        },
      })
    }

    return submit()
  }

  const redirectToServices = () => history.push('/services')

  // caso a conta não tenha permissão para criar o tipo de conexão
  if (account.plan.services[type] === 0) {
    return (
      <SweetAlert type="warning" error title={t('common:MESSAGE_ATTENTION')} onConfirm={redirectToServices}>
        {t('TEXT_MODAL_DONT_HAVE_PERMISSION')}
      </SweetAlert>
    )
  }

  const serviceType = { id: 'webchat', name: 'Webchat' }

  useEffect(() => {
    const unblock = history.block((location) => {
      if (isLoading) {
        return true
      }
      setPath(location.pathname)
      if (openModalGetOut) {
        return true
      }
      setOpenModalGetOut(true)
      return false
    })

    return (openModalGetOut, setPath, isSubmitting) => {
      unblock(openModalGetOut, setPath, isSubmitting)
    }
  }, [openModalGetOut, isLoading])

  return (
    <>
      <Helmet title={title} />
      <S.Container>
        <S.ContentWrapper>
          <S.Breadcrumb>
            <Link to="/services">{t('TITLE_SERVICES')}</Link>
            <span> / </span>
            <span>{title}</span>
          </S.Breadcrumb>
          <S.TitleSection>{title}</S.TitleSection>
          <S.Row>
            <S.FormSection>
              <S.CardService data-testid="general_config">
                <S.CardTitle>{t('WEBCHAT_GENERAL_CONFIG')}</S.CardTitle>
                <Form>
                  <S.CardInfo>
                    <S.Row gap={32}>
                      <S.Col>
                        <InputGroupWrapper
                          id="type"
                          label={t('LABEL_MODAL_TYPE')}
                          required
                          render={({ id }) => (
                            <GroupInput withIcon>
                              <Select
                                icon={IconsService(serviceType.id)}
                                className="filter-type"
                                id={id}
                                onlyValue
                                value={serviceType}
                                onChange={(value) => setProperty('type', value)}
                                options={options}
                                isDisabled={true}
                              />
                            </GroupInput>
                          )}
                        />
                      </S.Col>
                      <S.Col>
                        <InputGroup
                          id="name"
                          label={t('LABEL_NAME_WEBCHAT')}
                          data-testid="service-name"
                          required
                          {...{
                            bindInput,
                            validation,
                          }}
                          onChange={(e) => setModel({ ...model, name: e.target.value })}
                        />
                      </S.Col>
                    </S.Row>
                    <S.Row gap={32}>
                      <S.Col>
                        <InputGroupWrapper
                          id="defaultDepartment"
                          label={t('LABEL_MODAL_STANDARD_DEPARTMENT')}
                          data-testid="default-department-service-input"
                          {...{
                            bindInput,
                            validation,
                            model,
                            setProperty,
                          }}
                          render={(input) => (
                            <GroupInput>
                              <DepartmentsSelect
                                icon={<IconDepartment fill={TextColor} width="27" height="27" />}
                                stateId="defaultDepartment"
                                id={input.id}
                                value={input.model[input.id]}
                                onChange={(value) => {
                                  if (value !== null && !value.archivedAt) {
                                    return input.setProperty('defaultDepartment', value)
                                  }
                                  return input.setProperty('defaultDepartment', null)
                                }}
                                onBlur={() => input.validation.setTouched(input.id)}
                                hideArchived
                                data-testid="defaultDepartment"
                              />
                            </GroupInput>
                          )}
                        />
                      </S.Col>
                      <S.Col>
                        <InputGroup
                          id="nameWebchat"
                          data-testid="connections-input-name_webchat_plugin"
                          label={t('LABEL_WEBCHAT_NAME_PLUGIN')}
                          textPopover={t('POPOVER_WEBCHAT_NAME_PLUGIN')}
                          infoPopover={true}
                          required
                          {...{ bindInput, validation }}
                          onChange={(e) => setModel({ ...model, nameWebchat: e.target.value })}
                        />
                      </S.Col>
                    </S.Row>
                    <S.Row gap={32}>
                      <S.Col>
                        <InputGroup
                          id="phoneWhatsapp"
                          label={t('LABEL_WEBCHAT_WHATSAPP_PLUGIN')}
                          textPopover={t('POPOVER_WEBCHAT_WHATSAPP_PLUGIN')}
                          infoPopover={true}
                          data-testid="connections-input-plugin_whatsapp"
                          onChange={(event) => setProperty('phoneWhatsapp', event.target.value.replace(/\D/g, ''))}
                          {...{ bindInput, validation }}
                        />
                      </S.Col>
                      <S.Col>
                        <InputGroup
                          id="nameTelegram"
                          label={t('LABEL_WEBCHAT_TELEGRAM')}
                          textPopover={t('POPOVER_WEBCHAT_TELEGRAM')}
                          infoPopover={true}
                          data-testid="connections-input-plugin_telegram"
                          {...{ bindInput, validation }}
                        />
                      </S.Col>
                    </S.Row>
                    <S.Row gap={32}>
                      <S.Col>
                        <InputGroup
                          id="googleId"
                          label={t('TAG_GOOGLE')}
                          textPopover={t('TAG_GOOGLE_DESCRIPTION')}
                          infoPopover={true}
                          data-testid="connections-input-plugin_google"
                          {...{ bindInput, validation }}
                        />
                      </S.Col>
                      <S.Col>
                        {RenderBotField({ bindInput, validation, model, setProperty, translation: t, serviceType })}
                      </S.Col>
                    </S.Row>
                  </S.CardInfo>
                </Form>
              </S.CardService>
              <ServicesFormConfig
                model={model}
                bindInput={bindInput}
                setProperty={setProperty}
                validation={validation}
              />
              <ServicesCustomForm
                setModel={setModel}
                model={model}
                bindInput={bindInput}
                setProperty={setProperty}
                validation={validation}
                openModalGetOut={openModalGetOut}
                setOpenModalGetOut={setOpenModalGetOut}
                path={path}
              />
              <S.ButtonSection>
                <Button
                  data-testid="cancel-service-button"
                  type="button"
                  background="#95A7CC"
                  size="sm"
                  onClick={() => history.push('/services')}
                >
                  {t('common:FORM_ACTION_CANCEL')}
                </Button>
                <Button
                  data-testid="save-service-button"
                  type="button"
                  background={PrimaryColor}
                  size="sm"
                  disabled={isLoading}
                  onClick={handleSubmit}
                >
                  {t('common:FORM_ACTION_SAVE')}
                </Button>
              </S.ButtonSection>
            </S.FormSection>
            <S.PreviewSection>
              <WebChatPreview webchat={model} />
            </S.PreviewSection>
          </S.Row>
        </S.ContentWrapper>
      </S.Container>
      {isAlertShowing && (
        <SweetAlert
          type={errorCode === 500 ? 'error' : 'warning'}
          error
          title={t('common:MESSAGE_ATTENTION')}
          onConfirm={closeAlert}
        >
          {getErrorMessage(error)}
        </SweetAlert>
      )}
    </>
  )
}
