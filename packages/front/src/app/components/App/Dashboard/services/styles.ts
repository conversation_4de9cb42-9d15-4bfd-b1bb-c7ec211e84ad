import styled from 'styled-components'
import { Link } from 'react-router-dom'
import { shade } from 'polished'
import { ConnectButton } from '360dialog-connect-button'
import { BorderColor, PrimaryColor, SecondaryColor, DangerColor } from '../../styles/colors'
import { fontFamily } from '../../styles/common'
import { flex } from '../../../App/styles/common'

interface CardServiceProps {
  isArchived: boolean
}

interface CardServiceFooterProps {
  isDialog: boolean
}

interface LabelStatusProps {
  isConnected: boolean
}
interface ButtonConnection {
  colorConection?: string
}

export const Connections = styled.div`
  padding-bottom: 60px;
  .pagination-content {
    border: 1px solid ${BorderColor} !important;
    border-radius: 5px !important;
    margin-top: 2rem;
  }
`

export const NewConnections = styled.div`
  display: flex;
  flex-direction: column;
  margin: 2rem 0 1rem 0;
`

export const NewConnectionsHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;

  span {
    font-size: 18px;
    color: ${SecondaryColor};
    display: block;
    margin: 0px 5px;
  }

  div {
    position: relative;
    cursor: help;
    &:hover {
      .tooltipUncontrolled {
        opacity: 1;
        z-index: 999;
      }
    }

    .tooltipUncontrolled {
      color: #fff;
      position: absolute;
      background: ${SecondaryColor};
      text-align: center;
      padding: 5px 10px;
      border-radius: 5px;
      bottom: -28px;
      font-family: ${fontFamily};
      transition: 0.25s ease-in-out;
      opacity: 0;
      left: 50%;
      transform: translateX(-5%);
      bottom: -38px;
      white-space: nowrap;
      z-index: -1;

      &::before {
        content: '';
        transition: 0.2s ease-in-out;
        width: 10px;
        height: 10px;
        background: ${SecondaryColor};
        display: block;
        position: absolute;
        left: 5%;
        transform: translateX(-50%) rotate(45deg);
        top: -4px;
        border-top-left-radius: 2px;
      }
    }
  }
`

export const NewConnectionWrapper = styled.div`
  display: flex;
  gap: 15px;
`

export const ButtonConnection = styled(Link)<ButtonConnection>`
  border-radius: 8px;
  border: 1px solid ${BorderColor};
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 100px;
  cursor: pointer;
  transition: 0.2s ease-in-out;
  text-decoration: none !important;

  &:hover {
    border: 1px solid ${PrimaryColor};

    box-shadow: 0px 4px 4px rgba(82, 101, 140, 0.15);
    span {
      color: ${(props) => props.colorConection};
    }
    svg {
      fill: ${(props) => props.colorConection};
    }
  }

  svg {
    transition: 0.2s ease-in-out;
    fill: ${PrimaryColor};
  }

  span {
    transition: 0.2s ease-in-out;
    font-size: 12px;
    color: ${PrimaryColor};
    margin-top: 8px;
  }
`

export const WABACardService = styled.div`
  border: 1px solid ${BorderColor};
  background: #e7e7e726;
  border-radius: 16px;
  overflow: hidden;
`

export const WABACardServiceHeader = styled.div`
  border-bottom: 1px solid ${BorderColor};
  background: #f2f7fc;
  padding-top: 0.3rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
`

export const WABACardServiceContent = styled.div`
  background-color: #ffffff;
  bottom-border: 1px solid ${BorderColor};
  padding: 1rem;

  .service-waba-label-status {
    font-size: 12px;
    color: #586171;
    line-height: 150%;
    padding-bottom: 0.75rem;
  }

  .service-waba-label-details {
    font-weight: 500;
    padding: 16px 0 8px 0;
  }

  .service-waba-connection-health {
    font-weight: 500;
    padding-bottom: 0.5rem;
  }
`

export const WABACardServiceButton = styled.button`
  margin-top: 24px;
  margin-bottom: 24px;

  &&& {
    height: 44px;
    width: 400px;
    border-radius: 32px;
    border: 1px solid #324b7d;
    background-color: transparent;

    display: flex;
    justify-content: center;
    align-items: center;
    align-self: right;

    color: #324b7d;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;

    outline: transparent;
  }
`

export const ButtonClose = styled.div`
  width: 35px;
  height: 35px;
  background: transparent;
  border-radius: 50%;
  cursor: pointer;
  ${flex};
  transition: 0.2s ease-in-out;
  position: absolute;
  right: 20px;
  top: 20px;

  &:hover {
    opacity: 0.9;
  }
`

export const CardService = styled.div<CardServiceProps>`
  border: 1px solid ${BorderColor};
  background: ${(props) => props.isArchived && '#e7e7e726'};
  border-radius: 8px;
  padding: 1rem;
`

export const CardServiceName = styled.div`
  display: flex;
  align-items: center;
  color: ${SecondaryColor};
  font-weight: 600;
  padding: 0.5rem;
  span {
    margin-right: 0.8rem;
  }
`

export const CardServiceNameLimiter = styled.span`
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  white-space: normal;
`

export const CardServiceFooter = styled.div<CardServiceFooterProps>`
  bottom: 0;
  position: absolute;
  border-top: 1px solid ${BorderColor};
  width: calc(100% - 2rem);
  display: flex;
  justify-content: space-between;
  left: 0px;
  margin-left: 16px;
  padding: 8px;
  padding-bottom: ${(props) => (props.isDialog ? 24 : 0)}px;
`

export const ServiceCardStatus = styled.div`
  padding: 0.3rem;
  padding-top: 0px;
`

export const LabelStatus = styled.span<LabelStatusProps>`
  color: ${(props) => (props.isConnected ? '#1fc76d' : DangerColor)};
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 1rem;
  display: block;
`

export const StartConnection = styled.div`
  background: ${PrimaryColor};
  border-radius: 32px;
  color: white;
  text-align: center;
  padding: 15px;
  cursor: pointer;
  transition: 0.2s ease-in-out;

  &:hover {
    background: ${shade(0.2, PrimaryColor)};
  }
`

export const LabelInactive = styled.span`
  color: grey;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 1rem;
  opacity: 0.7;
  display: block;
`

export const ButtonChooseFile = styled.div`
  background: ${PrimaryColor};
  border-radius: 32px;
  width: max-content;
  transition: opacity 0.2s ease-in-out;
  margin: 5px 0px;
  &:hover {
    opacity: 0.9;
  }
  label {
    color: white !important;
    margin: 0px;
    padding: 5px 20px;
    cursor: pointer;
  }

  input {
    width: 0px;
    height: 0px;
  }
`
export const StartConnectionButton = styled(ConnectButton)`
  outline: none;
  background: ${PrimaryColor};
  width: 100%;
  border: none;
  border-radius: 32px;
  color: white;
  text-align: center;
  padding: 15px;
  margin: 16px 0 0;
  cursor: pointer;
  transition: 0.2s ease-in-out;
  &:hover {
    background: ${shade(0.2, PrimaryColor)};
  }
`

export const DivBadge = styled.div`
  .badge {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.75rem !important;
    border-radius: 50px;
    height: 1.5rem;
    padding: 0.25rem 0.5rem !important;
    font-weight: 500;
    gap: 0.25rem;
    width: fit-content;
  }

  .badge-success-waba {
    background-color: #cdffc7 !important;
    color: #0b690b !important;
  }

  .badge-warning-waba {
    background-color: #fcf9c5;
    color: #814f12;
  }

  .badge-error-waba {
    background-color: #fee2e2;
    color: #981c1c;
  }
`
