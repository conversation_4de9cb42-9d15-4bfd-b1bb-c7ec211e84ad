import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { debounce, identity, pickBy } from 'lodash'
import { useAddMembers, useCreateGroup, useFetchManyContactsLocal } from '../../../../../resources/contact/requests'
import Avatar from '../../../../common/connected/Avatar/Avatar'
import CardLoading from '../../../../common/unconnected/LoadingSpinner'
import ContactName from '../../../../common/unconnected/ContactName'
import ServicesSelect from '../../../../common/connected/ServicesSelect'
import TablePagination from '../../../../common/unconnected/TablePagination'
import useEventCallback from '../../../../../hooks/useEventCallback'
import useIndexController from '../../../../../hooks/crud/useIndexController'
import { Link } from 'react-router-dom'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../../common/unconnected/ui/dialog'
import { Input } from '../../../../common/unconnected/ui/input'
import { Button } from '../../../../common/unconnected/ui/button'
import { Badge } from '../../../../common/unconnected/ui/badge'
import { Label } from '../../../../common/unconnected/ui/label'
import { Checkbox } from '../../../../common/unconnected/ui/checkbox'
import { useToast } from '../../../../../hooks/useToast'
import { Loader2, Plus, Search, UserCheck, Users, Vibrate, XCircle } from 'lucide-react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../../common/unconnected/ui/table'
import IfUserCan from '../../../../common/connected/IfUserCan/IfUserCanContainer'
import { hasPermission } from '../../../../common/connected/IfUserCan/IfUserCan'
import config from '../../../../../../../config'

const clean = (obj) => pickBy(obj, identity)

const useSelection = ({ contacts, filters }) => {
  const [state, setState] = useState({
    selectedIds: {},
  })

  const { selectedIds } = state

  const handleSingleCheckboxSelection = useEventCallback((id, checked) => {
    const contact = contacts.find((c) => c.id === id)

    setState((prevState) => ({
      ...prevState,
      selectedIds: {
        ...prevState.selectedIds,
        [id]: {
          checked,
          number: contact?.data?.number || id,
          name: contact?.name,
          internalName: contact?.internalName,
        },
      },
    }))
  })

  const isAnyContactSelected = useMemo(
    () => Object.entries(selectedIds).filter(([key, value]) => value.checked).length > 0,
    [selectedIds],
  )

  return {
    selectedIds,
    handleSingleCheckboxSelection,
    isAnyContactSelected,
    setState,
  }
}

const getError = (failReason, t) => {
  const errorsMap = {
    'The contact is already in the group.': t('ERROR_CONTACT_ALREADY_IN_THE_GROUP'),
    'Not a group admin.': t('ERROR_NOT_A_GROUP_ADMIN'),
    'The contact blocked me.': t('ERROR_THE_CONTACT_BLOCKED_ME'),
    'The contact is invalid.': t('ERROR_THE_CONTACT_IS_INVALID'),
    'Contact not in address book.': t('ERROR_THE_CONTACT_NOT_IN_ADDRESS_BOOK'),
  }

  return errorsMap[failReason] || t('ERROR_UNKNOWN')
}

const buildQuery = ({ filters, localPagination }) => {
  return {
    query: JSON.stringify({
      where: clean({
        visible: {
          $eq: true,
        },
        isGroup: {
          $eq: false,
        },
        idFromService: {
          $notLike: '%@lid%',
        },
        archivedAt: {
          $eq: null,
        },
        serviceId: filters.serviceId,
        ...(!!filters.search && {
          $or: [
            { name: { $iLike: `%${filters.search}%` } },
            { alternativeName: { $iLike: `%${filters.search}%` } },
            { internalName: { $iLike: `%${filters.search}%` } },
            { 'data.number': { $iLike: `%${filters.search}%` } },
          ],
        }),
      }),
      include: ['avatar'],
      order: [['name', 'ASC']],
      page: localPagination.page,
      perPage: localPagination.perPage,
    }),
  }
}

interface CreateGroupDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  handleContactClick: (contact: any) => void
  setFilters: (filters: any) => void
  user: any
}

export function CreateGroupDialog({
  open,
  onOpenChange,
  handleContactClick,
  setFilters: setFiltersContactList,
  user,
}: CreateGroupDialogProps) {
  const appName = config('whitelabel.appName')
  const { t } = useTranslation(['chatPage', 'common'])
  const [{ isLoading: isLoadingCreateGroup }, execCreateGroup] = useCreateGroup()
  const [{ isLoading: isLoadingAddMembers }, execAddMembers] = useAddMembers()
  const { toast } = useToast()
  const [groupName, setGroupName] = useState('')
  const [failures, setFailures] = useState<
    {
      number: string
      success: boolean
      failReason: 'The contact is already in the group.' | 'The contact is invalid.' | 'The contact blocked me.'
    }[]
  >([])
  const [success, setSuccess] = useState<
    {
      number: string
      sentInviteMessage: boolean
      success: string
    }[]
  >([])

  const initialPagination = {
    page: 1,
    perPage: 5,
  }

  const {
    models: contacts,
    pagination,
    isLoading,
    fetch,
    isFiltersShowing,
    toggleFilters,
    filters,
    setFilters,
    handleFilterChange,
    localPagination,
    handleLocalPaginationChange,
  } = useIndexController({
    buildQuery,
    initialFilters: {
      search: '',
      serviceId: '',
    },
    initialPagination,
    useFetchMany: useFetchManyContactsLocal,
  })

  const { selectedIds, handleSingleCheckboxSelection, isAnyContactSelected, setState } = useSelection({
    contacts,
    filters,
  })

  const handleChange = useCallback(
    (e) => {
      setFilters((prevState) => ({ ...prevState, search: e.target.value, debounced: true }))
    },
    [setFilters],
  )

  const handleCreateGroup = async () => {
    let failuresAddMembers = []
    let successAddMembers = []

    try {
      const responseCreateGroup = await execCreateGroup({
        serviceId: filters.serviceId,
        internalName: groupName,
        isGroup: true,
      })

      if (hasPermission(user.permissions, 'groups.add.members')) {
        const members = Object.entries(selectedIds)
          .filter(([k, v]) => !!v.checked)
          .map(([k, v]) => ({
            contactId: k === v.number ? null : k,
            number: v.number,
          }))

        const responseAddMembers = await execAddMembers(responseCreateGroup.id, members)

        failuresAddMembers = responseAddMembers.filter((i) => !i.success)
        successAddMembers = responseAddMembers.filter((i) => !!i.success)

        setFailures(failuresAddMembers)
        setSuccess(successAddMembers)
      }

      setFiltersContactList({
        view: 'ALL',
      })
      handleContactClick(responseCreateGroup)

      toast({
        title: t('GROUP_CREATED_SUCCESS'),
        variant: 'success',
      })

      if (failuresAddMembers.length === 0 && successAddMembers.length === 0) {
        onOpenChange(false)
      }
    } catch (error) {
      toast({
        title: t('GROUP_CREATION_ERROR'),
        variant: 'destructive',
      })
    }
  }
  useEffect(() => {
    setFilters({
      search: '',
      serviceId: '',
    })
    setState({
      selectedIds: {},
    })
    setSuccess([])
    setFailures([])
    handleLocalPaginationChange('page', 1)
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        style={{
          width: '100%',
          maxWidth: '744px',
          overflow: 'hidden',
        }}
      >
        {failures.length > 0 || success.length > 0 ? (
          <>
            <DialogHeader
              style={{
                marginBottom: '24px',
              }}
            >
              <UserCheck
                color="#3C66B9"
                style={{
                  width: '40px',
                  height: '40px',
                }}
              />
              <DialogTitle>{t('chatPage:NEW_MEMBERS_SUMMARY')}</DialogTitle>
              <DialogDescription>{t('chatPage:MEMBER_ADDITION_WARNING')}</DialogDescription>
            </DialogHeader>
            <main>
              <div
                style={{
                  border: '1px solid #D7DBE0',
                  borderRadius: '8px',
                  marginBottom: '24px',
                  maxHeight: '50vh',
                  overflow: 'auto',
                }}
              >
                <Table>
                  <TableHeader data-testid="member-group-status-header">
                    <TableRow>
                      <TableHead>{t('common:PHONE')}</TableHead>
                      <TableHead>{t('common:STATUS')}</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody data-testid="member-group-status-body">
                    {failures.map((r, i) => (
                      <TableRow>
                        <TableCell>{r.number}</TableCell>
                        <TableCell>
                          {r.failReason === 'The contact is already in the group.' && (
                            <Badge variant="error-soft">{t('ERROR_CONTACT_ALREADY_IN_THE_GROUP')}</Badge>
                          )}
                          {r.failReason === 'The contact is invalid.' && (
                            <Badge variant="error-soft">{t('ERROR_THE_CONTACT_IS_INVALID')}</Badge>
                          )}
                          {r.failReason === 'The contact blocked me.' && (
                            <Badge variant="error-soft">{t('ERROR_THE_CONTACT_BLOCKED_ME')}</Badge>
                          )}
                          {r.failReason !== 'The contact is already in the group.' &&
                            r.failReason !== 'The contact is invalid.' &&
                            r.failReason !== 'The contact blocked me.' && (
                              <Badge variant="error-soft">{t('chatPage:FAIL')}</Badge>
                            )}
                        </TableCell>
                      </TableRow>
                    ))}
                    {success.map((r, i) => (
                      <TableRow>
                        <TableCell>{r.number}</TableCell>
                        <TableCell>
                          {r.sentInviteMessage ? (
                            <Badge variant="info-soft">{t('chatPage:SENT_INVITE')}</Badge>
                          ) : (
                            <Badge variant="success-soft">{t('chatPage:ADDED')}</Badge>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </main>
            <DialogFooter
              style={{
                width: '100%',
                display: 'flex',
                gap: '16px',
              }}
            >
              {/* {failures.length > 0 && (
                <Button
                  variant="outline"
                  style={{
                    width: '100%',
                  }}
                >
                  Tentar novamente
                </Button>
              )} */}

              <DialogClose asChild>
                <Button
                  style={{
                    // width: failures.length > 0 ? '100%' : '240px',
                    width: '240px',
                  }}
                >
                  Continuar
                </Button>
              </DialogClose>
            </DialogFooter>
          </>
        ) : (
          <>
            <DialogHeader
              style={{
                marginBottom: '40px',
              }}
            >
              <DialogTitle>{t('chatPage:CREATE_GROUP')}</DialogTitle>
            </DialogHeader>
            <main>
              <section
                style={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '16px',
                  marginBottom: '24px',
                }}
              >
                <div style={{ width: '100%' }}>
                  <Label
                    style={{
                      marginBottom: '4px',
                    }}
                    data-testid="label-group_name"
                  >
                    {t('chatPage:GROUP_NAME', { app: appName })}*
                  </Label>
                  <Input
                    data-testid="input-group_name"
                    placeholder={t('common:PLACEHOLDER_INPUT')}
                    onChange={(e) => setGroupName(e.target.value)}
                  />
                </div>
                <div style={{ width: '100%' }} data-testid="div-group_connections">
                  <Label
                    style={{
                      marginBottom: '4px',
                    }}
                    data-testid="label-connections"
                  >
                    {t('chatPage:LABEL_CONNECTIONS')}*
                  </Label>
                  <ServicesSelect
                    icon={<Vibrate />}
                    onChange={(value) => {
                      if (value) {
                        setFilters((prevState) => ({ ...prevState, serviceId: value.id }))
                      } else {
                        setFilters((prevState) => ({ ...prevState, serviceId: '' }))
                      }
                    }}
                    extraQuery={{
                      where: {
                        type: 'whatsapp',
                      },
                    }}
                  />
                </div>
              </section>
              <IfUserCan permission="groups.add.members">
                {filters.serviceId ? (
                  <>
                    <Input
                      icon={<Search />}
                      placeholder={t('chatPage:SEARCH_BY_NAME_OR_NUMBER')}
                      value={filters.search}
                      onChange={handleChange}
                      className="pl-5"
                      data-testid="member-input-search_by_name_or_number"
                    />

                    {isAnyContactSelected && (
                      <div
                        style={{
                          display: 'flex',
                          gap: '8px',
                          alignItems: 'center',
                          flexWrap: 'wrap',
                          marginTop: '16px',
                        }}
                      >
                        {Object.entries(selectedIds)
                          .filter((i) => !!i[1].checked)
                          .map((i) => (
                            <Badge
                              style={{
                                display: 'flex',
                                gap: '4px',
                                alignItems: 'center',
                              }}
                            >
                              <span>{i[1].internalName || i[1].name || i[1].number}</span>
                              <Button
                                variant="ghost"
                                style={{
                                  padding: '0',
                                  margin: '0',
                                  width: '16px',
                                  height: '16px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}
                                onClick={() => handleSingleCheckboxSelection(i[0], false)}
                              >
                                <XCircle
                                  style={{
                                    width: '16px',
                                    height: '16px',
                                    cursor: 'pointer',
                                  }}
                                />
                              </Button>
                            </Badge>
                          ))}
                      </div>
                    )}
                    <div
                      style={{
                        border: '1px solid #D7DBE0',
                        borderRadius: '8px 8px 0 0',
                        marginTop: '16px',
                      }}
                    >
                      <Table>
                        <TableHeader data-testid="member-card-header">
                          <TableRow>
                            <TableHead />
                            <TableHead>{t('common:LABEL_NAME')}</TableHead>
                            <TableHead>{t('common:LABEL_NUMBER')}</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody data-testid="member-table">
                          {contacts &&
                            contacts.map((contact, key) => (
                              <TableRow key={contact.id}>
                                <TableCell>
                                  <Checkbox
                                    id={`name-${key}`}
                                    data-testid="checkbox-select_contact"
                                    checked={!!selectedIds[contact.id]?.checked}
                                    onCheckedChange={(value) => {
                                      handleSingleCheckboxSelection(contact.id, value)
                                    }}
                                  />
                                </TableCell>
                                <TableCell>
                                  <Avatar
                                    contact={contact}
                                    className="mr-2"
                                    style={{ minWidth: 28, maxWidth: 28, minHeight: 28, maxHeight: 28 }}
                                  />
                                  <Link to={`/contacts/${contact.id}`}>
                                    <ContactName contact={{ ...contact, name: contact.name || contact.data?.number }} />
                                  </Link>
                                </TableCell>
                                <TableCell>{contact.data.number}</TableCell>
                              </TableRow>
                            ))}
                          {!contacts.length && [12, 13].includes(filters.search?.length) && (
                            <TableRow key={filters.search}>
                              <TableCell>
                                <Checkbox
                                  id={`name-${filters.search}`}
                                  checked={!!selectedIds[filters.search]?.checked}
                                  onCheckedChange={(value) => {
                                    handleSingleCheckboxSelection(filters.search, value)
                                  }}
                                />
                              </TableCell>
                              <TableCell>
                                <Avatar
                                  className="mr-2"
                                  style={{ minWidth: 28, maxWidth: 28, minHeight: 28, maxHeight: 28 }}
                                />
                                {filters.search}&nbsp;-&nbsp;<b>{t('UNREGISTERED_CONTACT')}</b>
                              </TableCell>
                              <TableCell>{filters.search}</TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>
                    <TablePagination
                      simplePagination
                      pagination={pagination}
                      localPagination={localPagination}
                      handlePaginationChange={handleLocalPaginationChange}
                      data-testid="pagination-members"
                    />
                  </>
                ) : (
                  <section
                    style={{
                      backgroundColor: '#F7F8F8',
                      borderRadius: '8px',
                      height: '200px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '8px',
                      flexDirection: 'column',
                      marginBottom: '24px',
                    }}
                  >
                    <Users />
                    <span>{t('chatPage:SELECT_SERVICE_TO_LOAD_CONTACTS')}</span>
                  </section>
                )}
              </IfUserCan>
            </main>
            <DialogFooter
              style={{
                width: '100%',
                display: 'flex',
                gap: '16px',
                marginTop: '24px',
              }}
            >
              <DialogClose asChild>
                <Button
                  style={{
                    width: '100%',
                  }}
                  variant="outline"
                >
                  {t('common:FORM_ACTION_CANCEL')}
                </Button>
              </DialogClose>

              <Button
                data-testid="button-create-group"
                style={{
                  width: '100%',
                }}
                onClick={handleCreateGroup}
                disabled={
                  isLoading ||
                  isLoadingCreateGroup ||
                  isLoadingAddMembers ||
                  (hasPermission(user.permissions, 'groups.add.members') && !isAnyContactSelected) ||
                  filters.serviceId === '' ||
                  groupName === ''
                }
              >
                <Plus style={{ marginRight: '4px' }} />
                {t('common:FORM_ACTION_CREATE')}
              </Button>
            </DialogFooter>
          </>
        )}
        <CardLoading isLoading={isLoadingCreateGroup || isLoadingAddMembers} />
      </DialogContent>
    </Dialog>
  )
}
