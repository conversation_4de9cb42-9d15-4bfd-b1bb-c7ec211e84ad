import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Button as ReactButton } from 'reactstrap'
import { But<PERSON> } from './../../../../../../common/unconnected/ui/button'
import { useTranslation } from 'react-i18next'
import { IconClose, IconSpellCheck, IconEdit3 } from '../../../../../../common/unconnected/IconDigisac/index'
import * as S from './style'

import ImproveText from './ImproveText'
import WaitApi from './WaitApi'
import CorrectionText from './CorrectionText'
import messageApi from '../../../../../../../resources/message/api'
import { useRequest } from '../../../../../../../hooks/useRequest'
import toast from '../../../../../../../utils/toast'
import ErrorNoCredits from './ErrorNoCredits'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from '../../../../../../common/unconnected/ui/tabs'
import Copilot from './Copilot'
import { isEmpty } from 'lodash'

function MagicText({
  handleCloseModal,
  handleQuickReplyAiClick,
  message,
  ticket,
  messageText,
  user,
  service,
  serviceType = 'magic-text',
}) {
  const { t } = useTranslation(['chatPage'])
  const [typeOperation, setTypeOperation] = useState('')
  const [isCorrectionText, setIsCorrectionText] = useState(false)
  const [isImproveText, setIsImproveText] = useState(false)
  const [selectedValue, setSelectedValue] = useState('')
  const [generateText, setGenerateText] = useState(false)
  const [waitApi, setWaitApi] = useState(false)
  const [textAi, setTextAi] = useState('')
  const [accountFlags, setAccountFlags] = useState('')
  const [accountId, setAccountId] = useState('')
  const [isNewSuggestion, setIsNewSuggestion] = useState(false)
  const [showNoCreditsMagicText, setShowNoCreditsMagicText] = useState(false)
  const [showNoCreditsCopilot, setShowNoCreditsCopilot] = useState(false)
  const modalRef = useRef(null)
  const [{ response }, magicText] = useRequest(messageApi.magicText)

  const handleApplyAi = () => {
    handleQuickReplyAiClick({
      text: textAi,
    })
    handleCloseModal()
  }

  useEffect(() => {
    if (user.account) {
      setAccountFlags(user.account.settings?.flags || {})
      setAccountId(user.account.id || '')
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  useEffect(() => {
    if (response) {
      if (response.data === 'MAGIC_TEXT_LIMIT_REACHED') {
        setIsCorrectionText(false)
        setIsNewSuggestion(false)
        setIsImproveText(false)
        setWaitApi(false)
        setShowNoCreditsMagicText(true)
      } else {
        setTextAi(response.data?.answer)
        setGenerateText(true)
        setIsNewSuggestion(false)
      }
    }
  }, [response])

  const handleType = (data) => {
    setShowNoCreditsMagicText(false)

    if (data === 'correct') {
      setIsNewSuggestion(true)
      setIsCorrectionText(true)
      setIsImproveText(false)
      setWaitApi(true)
      setGenerateText(false)
      magicText({ message: messageText, tone: 'correct', accountId, serviceId: service?.id, userId: user?.id })
    } else {
      setIsNewSuggestion(true)
      setIsImproveText(true)
      setIsCorrectionText(false)
      setWaitApi(false)
      setGenerateText(false)
    }
    setTypeOperation(data)
  }

  const handleRadioChange = (event) => {
    setSelectedValue(event.target.value)
  }

  const handleGenerateMessage = useCallback(() => {
    if (selectedValue.length == 0) {
      return toast.warn(t('WARN_SELECT_TONE'))
    }
    setIsNewSuggestion(true)
    setWaitApi(true)
    setTypeOperation('ImproveText')
    magicText({ message: messageText, tone: selectedValue, accountId, serviceId: service?.id, userId: user?.id })
  }, [selectedValue, messageText, accountId, service, magicText, t, setIsNewSuggestion, setWaitApi, setTypeOperation])

  const handleReturn = () => {
    setIsImproveText(false)
    setIsCorrectionText(false)
    setWaitApi(false)
    setGenerateText(false)
    setShowNoCreditsMagicText(false)
  }

  const handleClickOutside = (event) => {
    if (modalRef.current && !modalRef.current.contains(event.target)) {
      handleCloseModal()
    }
  }

  return (
    <>
      {!(accountFlags || {})['enable-magic-text'] && !(accountFlags || {})['enable-copilot'] && null}
      <S.ModalMagicTextDiv>
        <S.ModalMagicTextContent ref={modalRef}>
          <Tabs defaultValue={serviceType}>
            <S.ModalMagicTextHeader>
              <TabsList style={{ marginBottom: '-2px' }}>
                {(accountFlags || {})['enable-magic-text'] && messageText.length > 0 && (
                  <TabsTrigger value="magic-text">{t('TITLE_MAGIC_TEXT_1')}</TabsTrigger>
                )}
                {(accountFlags || {})['enable-copilot'] && (
                  <TabsTrigger value="copilot">{t('LABEL_COPILOT')}</TabsTrigger>
                )}
              </TabsList>
              <ReactButton
                id="button_close_modal_magic"
                style={{ padding: '0px' }}
                color="link"
                onClick={handleCloseModal}
              >
                <IconClose width="32" height="32" fill="#6E7A89" />
              </ReactButton>
            </S.ModalMagicTextHeader>
            <S.ModalMagicTextBody>
              <TabsContent value="magic-text">
                {!isCorrectionText && !isImproveText && !showNoCreditsMagicText && (
                  <div>
                    <div style={{ display: 'flex', flexDirection: 'column', padding: '16px' }}>
                      <span className="pTitle">{t('LABEL_MAGIC_TEXT_1')}</span>
                    </div>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', padding: '12px 16px' }}>
                      <Button
                        id="button_magic_option_correct"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleType('correct')}
                        style={{ alignSelf: 'flex-start' }}
                      >
                        <IconSpellCheck width="12" height="12" fill="#6E7A89" />
                        <span style={{ padding: '0px 8px' }}>{t('BUTTON_MAGIC_OPTION_CORRECT')}</span>
                      </Button>
                      <Button
                        id="button_magic_option_improve"
                        variant="ghost"
                        type="button"
                        size="sm"
                        style={{ alignSelf: 'flex-start' }}
                        onClick={() => handleType('ImproveText')}
                      >
                        <IconEdit3 width="12" height="12" fill="#6E7A89" />
                        <span style={{ padding: '0px 8px' }}>{t('BUTTON_MAGIC_OPTION_IMPROVE')}</span>
                      </Button>
                    </div>
                  </div>
                )}

                {isImproveText && !waitApi && (
                  <>
                    <ImproveText
                      handleRadioChange={handleRadioChange}
                      selectedValue={selectedValue}
                      handleGenerateMessage={handleGenerateMessage}
                    />
                  </>
                )}

                {!isImproveText && isCorrectionText && (
                  <CorrectionText
                    handleApplyAi={handleApplyAi}
                    handleType={handleType}
                    typeOperation={typeOperation}
                    handleGenerateMessage={handleGenerateMessage}
                    textAi={textAi}
                    textOriginal={messageText}
                    isNewSuggestion={isNewSuggestion}
                  />
                )}

                {waitApi && !generateText && !isCorrectionText && <WaitApi handleReturn={handleReturn} />}

                {isImproveText && waitApi && generateText && (
                  <CorrectionText
                    handleApplyAi={handleApplyAi}
                    handleType={handleType}
                    typeOperation={typeOperation}
                    handleGenerateMessage={handleGenerateMessage}
                    textAi={textAi}
                    textOriginal={messageText}
                    isNewSuggestion={isNewSuggestion}
                  />
                )}

                {showNoCreditsMagicText && <ErrorNoCredits user={user} />}
              </TabsContent>
              <TabsContent value="copilot">
                {!showNoCreditsCopilot && (
                  <Copilot
                    handleQuickReplyAiClick={handleQuickReplyAiClick}
                    handleCloseModal={handleCloseModal}
                    message={message}
                    ticket={ticket}
                    fromChat={!isEmpty(message)}
                    handleNoCredits={() => setShowNoCreditsCopilot(true)}
                    accountFlags={accountFlags}
                    serviceType={service?.type}
                  />
                )}
                {showNoCreditsCopilot && <ErrorNoCredits user={user} />}
              </TabsContent>
            </S.ModalMagicTextBody>
          </Tabs>
        </S.ModalMagicTextContent>
      </S.ModalMagicTextDiv>
    </>
  )
}

export default MagicText
