import React, { useEffect, useState } from 'react'

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogClose,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../../../../common/unconnected/ui/dialog'
import { Label } from '../../../../../common/unconnected/ui/label'
import { Input } from '../../../../../common/unconnected/ui/input'
import { Checkbox } from '../../../../../common/unconnected/ui/checkbox'
import { Textarea } from '../../../../../common/unconnected/ui/textarea'
import { useToast } from '../../../../../../hooks/useToast'
import { Button } from '../../../../../common/unconnected/ui/button'
import { Edit2 } from 'lucide-react'
import { TextColor } from '../../../../styles/colors'
import { useTranslation } from 'react-i18next'
import * as Yup from 'yup'
import { useFormik } from 'formik'
import { useRequest } from '../../../../../../hooks/useRequest'
import contactApi from '../../../../../../resources/contact/api'
import CardLoading from '../../../../../common/unconnected/LoadingSpinner'
import config from '../../../../../../../../config'

const EditGroupInfoFormSchema = Yup.object().shape({
  internalName: Yup.string().required('Nome do grupo é obrigatório'),
  groupDescription: Yup.string(),
  onlyAdminCanSendMessage: Yup.boolean(),
  onlyAdminCanEditProperties: Yup.boolean(),
  setAdminApproval: Yup.boolean(),
  setMemberAddMode: Yup.boolean(),
})
type EditGroupInfoForm = Yup.InferType<typeof EditGroupInfoFormSchema>

interface EditGroupInfoDialogProps {
  currentContact: {
    id: string
    internalName: string
    name: string
    service: {
      name: string
    }
    data: {
      isOnGroup: boolean
    }
    groupConfig?: {
      description?: string
      inviteUrl: string
      onlyAdminCanSend: boolean
      onlyAdminCanEditProperties: boolean
      temporaryMessage: number
      memberAddMode: boolean
      membershipApprovalMode: boolean
    }
  }
  refreshContact: () => void
}

export function EditGroupInfoDialog({ currentContact, refreshContact }: EditGroupInfoDialogProps) {
  const appName = config('whitelabel.appName')
  const { t } = useTranslation(['chatPage', 'common'])
  const [isOpen, setIsOpen] = useState(false)
  const [contactUpdateResult, contactUpdateById] = useRequest(contactApi.updateById)
  const { toast } = useToast()
  const [initialValues, setInitialValues] = useState<EditGroupInfoForm>({
    internalName: '',
    groupDescription: '',
    onlyAdminCanSendMessage: false,
    onlyAdminCanEditProperties: false,
    setAdminApproval: false,
    setMemberAddMode: false,
  })

  const formik = useFormik<EditGroupInfoForm>({
    initialValues: initialValues,
    enableReinitialize: true,
    validationSchema: EditGroupInfoFormSchema,
    onSubmit: async (values) => {
      try {
        const data: EditGroupInfoForm = {
          internalName: values.internalName,
          onlyAdminCanSendMessage: !values.onlyAdminCanSendMessage,
          onlyAdminCanEditProperties: !values.onlyAdminCanEditProperties,
          setAdminApproval: values.setAdminApproval,
          setMemberAddMode: values.setMemberAddMode,
        }
        const newData = Object.keys(initialValues).reduce((acc, key) => {
          if (data[key as keyof EditGroupInfoForm] !== initialValues[key as keyof EditGroupInfoForm]) {
            ;(acc as any)[key as keyof EditGroupInfoForm] = data[key as keyof EditGroupInfoForm]
          }
          return acc
        }, {} as Partial<EditGroupInfoForm>)

        if (values.groupDescription) {
          newData.groupDescription = values.groupDescription
        }

        await contactUpdateById(currentContact.id, newData)

        toast({
          title: t('chatPage:GROUP_DATA_UPDATED_SUCCESS'),
          variant: 'success',
        })
        refreshContact()
        setIsOpen(false)
      } catch (error) {
        toast({
          title: t('chatPage:GROUP_DATA_UPDATE_ERROR'),
          variant: 'destructive',
        })
      }
    },
  })

  useEffect(() => {
    if (!currentContact.groupConfig)
      setInitialValues({
        internalName: currentContact.internalName || currentContact.name,
      })
    else
      setInitialValues({
        internalName: currentContact.internalName || currentContact.name,
        groupDescription: currentContact.groupConfig.description || '',
        onlyAdminCanSendMessage: !currentContact.groupConfig.onlyAdminCanSend,
        onlyAdminCanEditProperties: !currentContact.groupConfig.onlyAdminCanEditProperties,
        setAdminApproval: currentContact.groupConfig.membershipApprovalMode,
        setMemberAddMode: currentContact.groupConfig.memberAddMode,
      })
  }, [currentContact])

  useEffect(() => {
    formik.resetForm()
  }, [isOpen])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button size="icon" data-testid="button-update_group" variant="ghost" disabled={!currentContact.data.isOnGroup}>
          <Edit2 color={TextColor} />
        </Button>
      </DialogTrigger>
      <DialogContent>
        <form onSubmit={formik.handleSubmit}>
          <DialogHeader
            style={{
              marginBottom: '40px',
            }}
          >
            <DialogTitle>{t('chatPage:EDIT_GROUP_INFO')}</DialogTitle>
          </DialogHeader>
          <main
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '24px',
              marginBottom: '24px',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '4px',
              }}
            >
              <Label>{t('chatPage:GROUP_NAME', { app: appName })}*</Label>
              <Input
                placeholder="Digite aqui"
                value={formik.values.internalName}
                onChange={formik.handleChange('internalName')}
                data-testid="input-edit_group_name"
              />
              {formik.errors.internalName && (
                <p
                  style={{
                    color: 'red',
                    fontSize: '12px',
                    margin: 0,
                  }}
                >
                  {formik.errors.internalName}
                </p>
              )}
            </div>
            {currentContact.groupConfig && (
              <>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '4px',
                  }}
                >
                  <Label>{t('chatPage:CONNECTION')}*</Label>
                  <Input disabled value={currentContact.service.name} data-testid="input-edit_group_conection" />
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '4px',
                  }}
                >
                  <Label>{t('chatPage:GROUP_DESCRIPTION')}</Label>
                  <Textarea
                    placeholder="Digite aqui"
                    value={formik.values.groupDescription}
                    onChange={formik.handleChange('groupDescription')}
                    data-testid="input-edit_group_description"
                  />
                </div>
                <section
                  style={{
                    display: 'flex',
                    justifyContent: 'flex-start',
                    flexDirection: 'column',
                    gap: '8px',
                    backgroundColor: '#F7F8F8',
                    borderRadius: '8px',
                    padding: '12px',
                  }}
                >
                  <p
                    style={{
                      margin: 0,
                    }}
                  >
                    {t('chatPage:GROUP_MEMBERS_CAN')}
                  </p>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <Checkbox
                      id="edit-group-info"
                      checked={formik.values.onlyAdminCanEditProperties}
                      onCheckedChange={(value) => formik.setFieldValue('onlyAdminCanEditProperties', value)}
                      data-testid="checkbox-edit_members_can_edit_properties"
                    />
                    <Label
                      htmlFor="edit-group-info"
                      style={{
                        fontSize: '16px',
                        fontWeight: 300,
                      }}
                    >
                      {t('chatPage:EDIT_GROUP_INFO_PERMISSION')}
                    </Label>
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <Checkbox
                      id="send-messages"
                      checked={formik.values.onlyAdminCanSendMessage}
                      onCheckedChange={(value) => formik.setFieldValue('onlyAdminCanSendMessage', value)}
                      data-testid="checkbox-edit_members_can_send_messages"
                    />
                    <Label
                      htmlFor="send-messages"
                      style={{
                        fontSize: '16px',
                        fontWeight: 300,
                      }}
                    >
                      {t('chatPage:SEND_MESSAGES')}
                    </Label>
                  </div>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <Checkbox
                      id="add-members"
                      checked={formik.values.setMemberAddMode}
                      onCheckedChange={(value) => formik.setFieldValue('setMemberAddMode', value)}
                      data-testid="checkbox-edit_members_can_add_members"
                    />
                    <Label
                      htmlFor="add-members"
                      style={{
                        fontSize: '16px',
                        fontWeight: 300,
                      }}
                    >
                      {t('chatPage:ADD_OTHER_MEMBERS')}
                    </Label>
                  </div>
                </section>
                <section
                  style={{
                    display: 'flex',
                    justifyContent: 'flex-start',
                    flexDirection: 'column',
                    gap: '8px',
                    backgroundColor: '#F7F8F8',
                    borderRadius: '8px',
                    padding: '12px',
                  }}
                >
                  <p
                    style={{
                      margin: 0,
                    }}
                  >
                    {t('chatPage:ADMINS_CAN')}
                  </p>
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <Checkbox
                      id="approve-members"
                      checked={formik.values.setAdminApproval}
                      onCheckedChange={(value) => formik.setFieldValue('setAdminApproval', value)}
                      data-testid="checkbox-edit_members_can_approve_members"
                    />
                    <Label
                      htmlFor="approve-members"
                      style={{
                        fontSize: '16px',
                        fontWeight: 300,
                      }}
                    >
                      {t('chatPage:APPROVE_NEW_MEMBERS')}
                    </Label>
                  </div>
                </section>
              </>
            )}
          </main>
          <DialogFooter>
            <DialogClose asChild>
              <Button
                style={{
                  width: '100%',
                }}
                variant="outline"
              >
                {t('common:FORM_ACTION_CANCEL')}
              </Button>
            </DialogClose>
            <Button
              type="submit"
              style={{
                width: '100%',
              }}
            >
              {t('common:FORM_ACTION_SAVE')}
            </Button>
          </DialogFooter>
        </form>
        <CardLoading isLoading={contactUpdateResult.isLoading} />
      </DialogContent>
    </Dialog>
  )
}
