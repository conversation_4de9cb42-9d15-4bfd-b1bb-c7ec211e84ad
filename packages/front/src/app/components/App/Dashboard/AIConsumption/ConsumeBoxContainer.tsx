import React, { useEffect } from 'react'
import { useSelector } from 'react-redux'
import { BotIcon, Clock4Icon, CpuIcon, FileTextIcon, StarIcon, Wand2Icon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import ConsumeBox from './ConsumeBox'
import Slider from '../../../../../components/unconnected/slider'
import { useRequest } from '../../../../hooks/useRequest'
import creditMovementApi from '../../../../resources/creditMovement/api'
import ConsumeAlerts from './ConsumeAlerts'
import { selectors as authSelectors } from '../../../../modules/auth'
import * as accountsFlags from '../../../App/Dashboard/Admin/accounts/AccountsFlags'

const defaultBalance = {
  balance: 0,
  outs: 0,
  ins: 0,
  used: 0,
}

const ConsumeBoxContainer = ({ plans, renewDate, account }) => {
  const { t } = useTranslation(['aIConsumption', 'common'])

  const user = useSelector(authSelectors.getUser)

  const [{ isLoading: isLoadingBalances, response: balancesResponse }, fetchBalances] = useRequest(
    creditMovementApi.balancesV2,
  )

  const [balances, setBalances] = React.useState({
    transcription: defaultBalance,
    summary: defaultBalance,
    magicText: defaultBalance,
    copilot: defaultBalance,
    csat: defaultBalance,
    agent: defaultBalance,
  })

  useEffect(() => {
    if (renewDate) {
      fetchBalances({ from: renewDate })
    }
  }, [renewDate])

  useEffect(() => {
    if (!isLoadingBalances && balancesResponse) {
      setBalances({
        transcription: balancesResponse.transcription?.all ?? defaultBalance,
        summary: balancesResponse.summary?.all ?? defaultBalance,
        magicText: balancesResponse['magic-text']?.all ?? defaultBalance,
        copilot: balancesResponse.copilot?.all ?? defaultBalance,
        csat: balancesResponse.csat?.all ?? defaultBalance,
        agent: balancesResponse.agent?.all ?? defaultBalance,
      })
    }
  }, [isLoadingBalances, balancesResponse])

  const calculateProgress = (plan: number, consumed: any) => {
    if (plan === 0) {
      return 0
    }
    const realConsumption = consumed?.realConsumption ?? consumed?.outs
    const totalCredits = plan + (consumed?.additionalCredits || 0)

    return (realConsumption * 100) / totalCredits
  }

  return (
    <div>
      <ConsumeAlerts
        flags={user?.account?.settings?.flags}
        transcriptionProgress={calculateProgress(plans.transcription, balances.transcription)}
        smartSummaryProgress={calculateProgress(plans.summary, balances.summary)}
        magicTextProgress={calculateProgress(plans?.magicText, balances?.magicText)}
        copilotProgress={calculateProgress(plans?.copilot, balances?.copilot)}
        csatProgress={calculateProgress(plans?.csat, balances?.csat)}
        agentProgress={calculateProgress(plans?.agent, balances?.agent)}
      />

      <Slider>
        {accountsFlags.isEnable(account?.settings?.flags || {}, 'enable-audio-transcription') === true && (
          <ConsumeBox
            icon={Clock4Icon}
            title={t('AUDIO_TRANSCRIPTION')}
            plan={plans.transcription}
            credits={balances.transcription}
            renewDate={renewDate}
            useCredits={false}
            serviceType="transcription"
          />
        )}

        {accountsFlags.isEnable(account?.settings?.flags || {}, 'enable-smart-summary') === true && (
          <ConsumeBox
            icon={FileTextIcon}
            title={t('SMART_SUMMARY')}
            plan={plans.summary}
            credits={balances.summary}
            renewDate={renewDate}
            useCredits={true}
            serviceType="summary"
          />
        )}

        {accountsFlags.isEnable(account?.settings?.flags || {}, 'enable-magic-text') === true && (
          <ConsumeBox
            icon={Wand2Icon}
            title={t('MAGIC_TEXT')}
            plan={plans.magicText}
            credits={balances.magicText}
            renewDate={renewDate}
            useCredits={true}
            serviceType="magic-text"
          />
        )}

        {accountsFlags.isEnable(account?.settings?.flags || {}, 'enable-copilot') === true && (
          <ConsumeBox
            icon={BotIcon}
            title={t('COPILOT')}
            plan={plans.copilot}
            credits={balances.copilot}
            renewDate={renewDate}
            useCredits={true}
            serviceType="copilot"
          />
        )}

        {accountsFlags.isEnable(account?.settings?.flags || {}, 'enable-smart-csat-score') === true && (
          <ConsumeBox
            icon={StarIcon}
            title={t('CSAT')}
            plan={plans.csat}
            credits={balances.csat}
            renewDate={renewDate}
            useCredits={true}
            serviceType="csat"
          />
        )}

        {accountsFlags.isEnable(account?.settings?.flags || {}, 'enable-bots-v3-ai-node') === true && (
          <ConsumeBox
            icon={CpuIcon}
            title={t('AGENT')}
            plan={plans.agent}
            credits={balances.agent}
            renewDate={renewDate}
            useCredits={true}
            serviceType="agent"
          />
        )}
      </Slider>
    </div>
  )
}
export default ConsumeBoxContainer
