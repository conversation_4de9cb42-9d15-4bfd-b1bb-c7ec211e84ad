import { FormGroup, Progress } from 'reactstrap'
import styled from 'styled-components'
import { DrawerContent } from '@ikatec/nebula-react'
import { PrimaryColor } from '../../styles/colors'
import { Button } from '../../../common/unconnected/ui/button'
import { DialogContent, DialogHeader } from '../../../common/unconnected/ui/dialog'

interface TabButtonProps {
  isActive: boolean
}

export const Title = styled.h2`
  font-size: 32px;
  font-weight: 600;
  color: ${PrimaryColor};
`

export const StyledDrawerContent = styled(DrawerContent)`
  max-width: 480px !important;
`

export const TabButton = styled.button<TabButtonProps>`
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: ${(props) => (props.isActive ? '#24272D' : '#586171')};
  border: 0px;
  border-bottom: 2px solid ${(props) => (props.isActive ? '#3C66B9' : '#B4BBC5')};
  border-radius: 0px;
  background: none;
`

export const Actions = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
`

export const ActionFilters = styled.div`
  .react_select__indicator-separator {
    display: none;
  }
`

export const ActionButtons = styled.div`
  a {
    padding: 16px;

    span {
      font-size: 16px;
      font-weight: 600;
      padding: 0px 8px;
    }
  }
`

export const FiltersFormGroup = styled(FormGroup)`
  display: flex;
  margin-bottom: 0px;
  flex-direction: row;
  gap: 8px;
`

export const AreaChartWide = styled.div`
  height: 430px;
  padding: 24px;
  background-color: #ffffff;
  border-radius: 16px;
`

export const AreaChart = styled.div`
  height: 430px;
  padding: 24px;
  background-color: #ffffff;
  border-radius: 16px;
`

export const DivIcon = styled.div`
  padding: 8px;
  height: 40px;
  width: 40px;
  color: #3c66b9;
  background: #e1edf8;
  border-radius: 20px;
  overflow: unset;
`

export const ConsumeBox = styled.div`
  background: #ffffff;
  padding: 24px;
  gap: 16px;
  min-width: 320px;
  height: 255px;
  gap: 16px;
  angle: 0deg;
  opacity: 1;
  border-radius: 16px;
  display: flex;
  flex-direction: column;

  .consumptionSpentLabel {
    font-size: 20px;
    line-height: 20px;
    font-weight: 600;
    margin-bottom: 0px;
    text-align: end;
    align-self: end;
  }

  .consumptionAvailableLabel {
    font-size: 10px;
    font-weight: 500;
    text-align: end;
    align-self: end;
    justify-content: flex-end;
    margin-bottom: 0px;
  }

  .consumptionInfo {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 10px;

    .descriptionLabel {
      font-size: 14px;
      font-weight: 600;
      color: #24272d;
    }

    .contractedLabel {
      font-size: 12px;
      font-weight: 500;
      color: #586171;
    }

    .renewInLabel {
      font-size: 10px;
      font-weight: 500;
      color: #6e7a89;
    }
  }
`

export const ConsumeBoxHeader = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 10px;
  color: #6e7a89;
`

export const Dot = styled.span`
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${(props) => props.color || '#3D6FC8'};
  margin-right: 2px;
  margin-bottom: 2px;
  vertical-align: middle;
`

export const DoubleProgressBar = styled(Progress)`
  height: 4px;
  background: #d7dbe0;
`

export const RoundDiv = styled.div`
  height: 20px;
  width: 30px;
  border-radius: 100px;
  background-color: ${(props) => props.color};
`

export const ProgressBar = styled(Progress)`
  height: 4px;
  border-radius: 2px;
  background: #d7dbe0;

  .progress-bar {
    background: ${(props) => (props.value < 100 ? (props.value < 75 ? '#3C66B9' : '#C48B0A') : '#B81D1D')};
  }
`

export const ProgressBarDrawer = styled(Progress)`
  .progress-bar {
    background: ${(props) => props.color} !important;
  }
`

export const ChartWrapper = styled.div`
  * {
    outline: none !important;
  }

  .recharts-wrapper,
  .recharts-surface,
  .recharts-sector,
  .recharts-pie,
  svg {
    outline: none !important;
    stroke: none !important;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
  }

  svg {
    &:focus,
    &:focus-visible,
    &:focus-within {
      outline: none !important;
      box-shadow: none !important;
      border: none !important;
    }
  }
`

export const DialogSubtitle = styled.span`
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  text-align: center;
`

export const ModalExportButton = styled(Button)`
  width: 224px;
  height: 40px;
  padding: 0px 16px 0px 16px;
  border-radius: 500px;
  font-size: 16px;
  font-weight: 600;
  line-height: 19.36px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
`

export const ButtonBack = styled(ModalExportButton)`
  border: 1px solid ${PrimaryColor};
  color: ${PrimaryColor};
  background: #ffffff;

  &:hover {
    background: #f5f7f9;
  }
`

export const ButtonExport = styled(ModalExportButton)`
  color: #ffffff;
  background: ${PrimaryColor};
`

export const DialogContentCustom = styled(DialogContent)`
  gap: 40px;
  z-index: 1000;
  overflow: visible;

  .select-service {
    .react_select__control {
      border-radius: 500px;
      border-color: hsl(0, 0%, 50%);
      padding-left: 8px;
      padding-right: 8px;
    }
  }
`

export const TableContainer = styled.div`
  width: 100%;
  border: 1px solid #e6edf5;
  border-color: rgba(82, 101, 140, 0.15);
  border-radius: 18px;
  background-color: #ffffff;
  overflow: hidden;
`

export const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`

export const TableHead = styled.thead`
  background-color: #f9fbff;
`

export const TableHeader = styled.th`
  font-size: 14px;
  font-weight: 600;
  color: #2d3e50;
  text-align: left;
  padding: 12px 16px;
  border-bottom: 1px solid #e6edf5;
`

export const TableBody = styled.tbody``

export const TableRow = styled.tr``

export const TableCell = styled.td`
  height: 80px;
  font-size: 14px;
  color: #495867;
  padding: 12px 16px;
  border-bottom: 1px solid #e6edf5;

  &.activity {
    font-weight: 600;
  }
`

export const PaginationContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  font-size: 14px;
  color: #495867;
  background-color: #f9fbff;
`

export const PaginationButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;

  button {
    font-size: 14px;
    color: #007bff;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;

    &:hover {
      background-color: rgba(0, 123, 255, 0.1);
    }

    &:disabled {
      color: #cccccc;
      cursor: not-allowed;
    }

    &.active {
      font-weight: 600;
      color: #ffffff;
      background-color: #007bff;
    }
  }
`

export const DialogHeaderCustom = styled(DialogHeader)`
  margin-top: 30px;
`

export const TextLegendChart = styled.span`
  width: 120px;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 20px;
`
