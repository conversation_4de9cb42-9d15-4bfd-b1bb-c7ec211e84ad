import React, { useState } from 'react'
import { useTranslation, Trans } from 'react-i18next'
import { Button, Label, ModalBody, ModalHeader } from 'reactstrap'
import { ModalDigisac, ModalFooter } from '../../../styles/common'
import { OutlineRadio, OutlineInputRadio } from './style'
import IconCircleKey from '../../../../../assets/icons/mfa/keyCircle.svg'
import ButtonClose from '../../../../common/unconnected/ButtonClose'

type EverybodyModalProps = {
  isOpen: boolean
  setIsOpen: (boolean) => void
  setScheduleDate: (string) => void
}

const EverybodyModal = ({ isOpen, setIsOpen, setScheduleDate }: EverybodyModalProps) => {
  // translate
  const { t } = useTranslation(['accountPage', 'common'])

  // states
  const [schedule, setSchedule] = useState('now')

  return (
    <ModalDigisac isOpen={isOpen}>
      <ModalHeader>
        <img src={IconCircleKey} alt="circle key" className="m-3" />
        <ButtonClose onClick={() => setIsOpen(false)} />
      </ModalHeader>
      <ModalBody>
        <div className="text-center">
          <h5>{t('MODAL_MFA_TITLE_MANDATORY')}</h5>
          <p>
            <Trans i18nKey="accountPage">{t('MODAL_MFA_DESCRIPTION_MANDATORY')}</Trans>
          </p>
        </div>

        <>
          <Label className="d-flex mt-2">
            <OutlineRadio>
              <OutlineInputRadio
                type="radio"
                name="schedule"
                id="schedule"
                value="now"
                onClick={() => setSchedule('now')}
                checked={schedule === 'now'}
              />
              {t('accountPage:MODAL_MFA_NOW_MANDATORY')}
            </OutlineRadio>
          </Label>

          <Label className="mt-2 d-flex">
            <OutlineRadio>
              <OutlineInputRadio
                type="radio"
                name="schedule"
                id="schedule"
                value="after"
                onClick={() => setSchedule('after')}
                checked={schedule === 'after'}
              />
              {t('accountPage:MODAL_MFA_SCHEDULE_MANDATORY')}
            </OutlineRadio>
          </Label>
        </>
      </ModalBody>
      <ModalFooter>
        <Button outline={true} onClick={() => setIsOpen(false)}>
          {t('common:FORM_ACTION_CANCEL')}
        </Button>

        <Button className="confirm" onClick={() => setScheduleDate(schedule)}>
          {schedule === 'now' ? t('common:FORM_ACTION_TO_EFFECT') : t('common:FORM_ACTION_TO_SCHEDULE')}
        </Button>
      </ModalFooter>
    </ModalDigisac>
  )
}

export default EverybodyModal
