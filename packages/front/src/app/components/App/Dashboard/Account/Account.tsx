import React, { memo, useCallback, useEffect, useState } from 'react'
import Helmet from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { Col, Form, Input, Label, Row } from 'reactstrap'
import useEventCallback from '../../../../hooks/useEventCallback'
import useForm from '../../../../hooks/useForm'
import usePrevious from '../../../../hooks/usePrevious'
import useValidation from '../../../../hooks/useValidation2'
import toast from '../../../../utils/toast'
import { between, betweenOrEqual, required } from '../../../../utils/validator/validators'
import { useHistory } from 'react-router-dom'

// components
import DepartmentsSelect from '../../../common/connected/DepartmentsSelect/DepartmentsSelectContainer'
import { hasPermission } from '../../../common/connected/IfUserCan/IfUserCan'
import HelpPopover from '../../../common/unconnected/HelpPopover'
import { IconClock, IconDepartment, IconHelp, IconOrganization } from '../../../common/unconnected/IconDigisac'
import InputGroup, { InputGroupWrapper } from '../../../common/unconnected/InputGroup'
import { LoadingButton } from '../../../common/unconnected/LoadingButton'
import MultipleInput from '../../../common/unconnected/MultipleInput'
import Switch from '../../../common/unconnected/Switch'
import TimezoneSelect from '../../../common/unconnected/TimezoneSelect'
import WorkPlan from '../../../common/unconnected/WorkPlan'
import { TextColor } from '../../styles/colors'
import { GroupInput } from '../../styles/common'
import Container from '../../styles/container'
import * as accountsFlags from '../Admin/accounts/AccountsFlags'
import AbsenceManagement from './AbsenceManagement'
import EverybodyModal from './MFA/EverybodyModal'
import ConfirmDisableModal from './MFA/ConfirmDisableModal'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../common/unconnected/ui/select'
import * as S from './styles'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../common/unconnected/ui/tabs'
import config from '../../../../../../config'
import CopyToClipboard from '../../../common/unconnected/CopyToClipboard'
import { Badge } from '../../../common/unconnected/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../../common/unconnected/ui/tooltip'
import { HelpCircle } from 'lucide-react'

const initialModel = {
  name: '',
  alias: '',
  settings: {
    disableDefaultTicketTransfer: false,
    absence: {},
  },
  isUserEmailRequired: true,
  userNameInMessages: false,
  isQueueNotificationActive: false,
  ticketOpenNotification: false,
  ticketTransferNotification: false,
  newMessageNotification: false,
  showSupportInfo: false,
  allowDuplicateNames: false,
  changeUserPasswordOnFirstAccess: false,
  userPasswordCreationMethod: 'manual',
  topicRequired: false,
  defaultDepartment: null,
  ticketsEnabled: true,
  ticketInactiveTime: 30,
  userAwayMinutesTime: 5,
  workPlan: [],
  timezone: 'ESAST',
  protocolFormat: '{{date}}{{count}}',
  ipRestriction: false,
  allowedIps: [],
  isPasswordExpirationActive: false,
  expirationPasswordTime: 5,
  twoFactorAuthMandatory: false,
  twoFactorAuthMandatorySchedule: null,
  showTagsInChat: false,
  userCanChangeVisibilityTagsOnChat: false,
  autoGenerateSummaryOnClosure: false,
  autoGenerateSummaryOnTransfer: false,
  azureAdEnabled: false,
  azureAdLabel: 'Azure AD',
  azureAdTenantId: '',
  azureAdClientId: '',
  azureAdClientSecret: '',
}

const formatToApi = (model: typeof initialModel) => ({
  ...model,
  settings: {
    ...model.settings,
    isUserEmailRequired: model.emailRequired,
    disableDefaultTicketTransfer: model.disableDefaultTicketTransfer,
    timezone: model.timezone,
    ticketsEnabled: !!model.ticketsEnabled,
    isQueueNotificationActive: !!model.isQueueNotificationActive,
    ticketOpenNotification: model.ticketOpenNotification,
    ticketTransferNotification: model.ticketTransferNotification,
    userNameInMessages: model.userNameInMessages,
    newMessageNotification: model.newMessageNotification,
    showSupportInfo: model.showSupportInfo,
    allowDuplicateNames: model.allowDuplicateNames,
    changeUserPasswordOnFirstAccess: model.changeUserPasswordOnFirstAccess,
    userPasswordCreationMethod: model.userPasswordCreationMethod,
    topicRequired: model.topicRequired,
    ticketInactiveTime: parseInt(model.ticketInactiveTime, 10),
    userAwayMinutesTime: parseInt(model.userAwayMinutesTime, 10),
    workPlan: model.workPlan.filter((rule) => rule.weekDays.length && rule.start && rule.end),
    protocolFormat: model.protocolFormat,
    ipRestriction: model.ipRestriction,
    allowedIps: model.allowedIps,
    isPasswordExpirationActive: !!model.isPasswordExpirationActive,
    expirationPasswordTime: parseInt(model.expirationPasswordTime) || 5,
    twoFactorAuthMandatory: !!model.twoFactorAuthMandatory,
    twoFactorAuthMandatorySchedule: model.twoFactorAuthMandatorySchedule,
    showTagsInChat: model.showTagsInChat,
    userCanChangeVisibilityTagsOnChat: model.userCanChangeVisibilityTagsOnChat,
    autoGenerateSummaryOnClosure: model.autoGenerateSummaryOnClosure,
    autoGenerateSummaryOnTransfer: model.autoGenerateSummaryOnTransfer,
    sso: {
      ...(model.azureAdTenantId && {
        azureAd: {
          enabled: model.azureAdEnabled,
          label: model.azureAdLabel,
          tenantId: model.azureAdTenantId,
          clientId: model.azureAdClientId,
          clientSecret: model.azureAdClientSecret,
        },
      }),
    },
  },
})

const formatFromApi = (account: ReturnType<typeof formatToApi>) => ({
  ...account,
  ...account.settings,
  azureAdEnabled: !!account.settings?.sso?.azureAd?.enabled,
  azureAdLabel: account.settings?.sso?.azureAd?.label,
  azureAdTenantId: account.settings?.sso?.azureAd?.tenantId,
  azureAdClientId: account.settings?.sso?.azureAd?.clientId,
  azureAdClientSecret: account.settings?.sso?.azureAd?.clientSecret,
})

function Account(props) {
  const { isLoading, error, save, user } = props

  const [currentTab, setCurrentTab] = useState('general')

  const wasLoading = usePrevious(isLoading)

  const { t } = useTranslation(['companyPage', 'common', 'creditsControlPage'])

  const history = useHistory()

  const { model, setModel, bindInput, setProperty } = useForm(initialModel)

  // states
  const [isOpenMFAEverybody, setIsOpenMFAEverybody] = useState(false)
  const [isDisableModalOpen, setIsDisableModalOpen] = useState(false)

  const validationRules = {
    name: [[required, t('common:REQUIRED_FIELD')]],
    ticketInactiveTime: [[between(0, 525600), t('MESSAGE_GREATER_THAN_LESS_THAN', { min: 0, max: 525600 })]],
    userAwayMinutesTime: [[betweenOrEqual(5, 90), t('MESSAGE_GREATER_THAN_EQUAL_LESS_THAN', { min: 5, max: 90 })]],
    expirationPasswordTime: [
      model.expirationPasswordTime && [between(4, 366), t('MESSAGE_GREATER_THAN_LESS_THAN', { min: 4, max: 366 })],
    ],
  }

  const { plan, creditsControlEnabled } = user.account

  const validation = useValidation(validationRules, model)

  const availableHsmLimit = plan.hsmLimit - plan.hsmUsedLimit

  const handleSubmit = useEventCallback((e) => {
    e.preventDefault()

    if (
      (model.settings?.absence || {}).enabled &&
      (model.settings?.absence || {})?.reason_required &&
      !((model.settings?.absence || {})?.reasons || []).length
    ) {
      toast.warn(t('REASONS_EMPTY'))
      return
    }
    if (!hasPermission(user.permissions, 'myAccount.update')) {
      toast.warn(t('EDIT_PERMISSION'))
      return
    }

    validation.validateAll().then((valid) => {
      if (!valid) return

      const formattedModel = formatToApi(model)

      save(formattedModel)

      // TODO: Necessário refatorar esssa lógica para garantir atualização dos atributos via socket.
      // Por hora será mantido para garantir consistência nos estados do front
      setTimeout(() => {
        window.location.reload()
      }, 10000)
    })
  })

  useEffect(() => {
    if (wasLoading && !isLoading && !error) {
      toast.success(t('TOAST_MESSAGE_SAVE'))
    }
  }, [isLoading, wasLoading])

  useEffect(() => {
    if (!user) return

    setModel((prevModel) => ({
      ...prevModel,
      ...formatFromApi(user.account),
    }))
  }, [!!user, user && user.account.updatedAt])

  /**
   * Ao ativar obrigatóriedade para todos os usuários
   *
   * @param {boolean} status
   */
  const handleChangeMFAEverybody = useCallback(
    (status) => {
      setIsOpenMFAEverybody(status)
      setProperty('twoFactorAuthMandatory', status)

      if (status === false) {
        setProperty('twoFactorAuthMandatorySchedule', null)
      }
    },
    [setProperty, setIsOpenMFAEverybody],
  )

  /**
   * Seta data para agendar ativação
   *
   * @param {string} schedule
   */
  const handleSetScheduleMFAMandatory = useCallback(
    (schedule) => {
      const scheduleDate = new Date()

      if (schedule === 'now') {
        scheduleDate.setMinutes(scheduleDate.getMinutes() + 2)
      } else {
        scheduleDate.setHours(23, 59, 0, 0)
      }

      setProperty('twoFactorAuthMandatorySchedule', scheduleDate)

      setIsOpenMFAEverybody(false)
    },
    [setProperty, setIsOpenMFAEverybody],
  )

  return (
    <>
      <Helmet title={t('TITLE_COMPANY')} />

      <EverybodyModal
        isOpen={isOpenMFAEverybody}
        setIsOpen={handleChangeMFAEverybody}
        setScheduleDate={handleSetScheduleMFAMandatory}
      />
      <ConfirmDisableModal
        isOpen={isDisableModalOpen}
        setIsOpen={setIsDisableModalOpen}
        handleDisable={() => {
          handleChangeMFAEverybody(false)
          setIsDisableModalOpen(false)
        }}
      />

      <Container size="sm">
        <h2 data-testid="tag-title-page" className="mb-4">
          {t('TITLE_COMPANY')}
        </h2>

        <Tabs defaultValue="flow" value={currentTab} onValueChange={setCurrentTab}>
          <TabsList className="mb-3">
            <TabsTrigger value="general">{t('TAB_GENERAL')}</TabsTrigger>
            <TabsTrigger value="sac">{t('TAB_SAC')}</TabsTrigger>
            <TabsTrigger value="workPlan">{t('TAB_WORK_PLAN')}</TabsTrigger>
            <TabsTrigger value="security">{t('TAB_SECURITY')}</TabsTrigger>
          </TabsList>

          <Form onSubmit={handleSubmit} data-testid="account-label-company">
            <S.CardAccount>
              <TabsContent value="general">
                <S.CardInfo>
                  <h3>{t('TITLE_GENERAL_COMPANY_SETTINGS')}</h3>

                  {creditsControlEnabled && (
                    <p className="m-0">
                      <b>{t('creditsControlPage:LABEL_CREDITS_CONTROL_ENABLED')}</b>
                    </p>
                  )}

                  <Row className="mt-4">
                    <Col data-testid="account-label-name_company">
                      <InputGroup
                        id="name"
                        label={t('LABEL_NAME_COMPANY')}
                        icon={<IconOrganization fill={TextColor} width="28" height="28" />}
                        data-testid="account-name"
                        {...{ bindInput, validation }}
                      />
                    </Col>
                  </Row>

                  <Row>
                    <Col data-testid="account-label-timezone">
                      <InputGroupWrapper
                        id="timezone"
                        label={t('LABEL_TIMEZONE')}
                        render={() => (
                          <TimezoneSelect
                            value={model.timezone}
                            icon={<IconClock fill={TextColor} width="27" height="27" className="icon-clock" />}
                            onChange={(value) => {
                              setProperty('timezone', value)
                            }}
                          />
                        )}
                      />
                    </Col>
                    <Col>
                      <InputGroup
                        id="hsmUsedLimit"
                        data-testid="account-label-hsm_available"
                        value={availableHsmLimit > 0 ? availableHsmLimit : 0}
                        type="number"
                        label={t('LABEL_WHATSAPP_BUSINESS_HSM_AVAILABLE')}
                        readOnly
                        {...{ bindInput, validation }}
                      />
                    </Col>
                  </Row>
                </S.CardInfo>
              </TabsContent>

              <TabsContent value="sac">
                <S.CardInfo data-testid="account-label-title_sac">
                  <h3>{t('TITLE_SAC')}</h3>

                  {model.ipRestriction && (
                    <InputGroupWrapper
                      className="mb-3"
                      id="allowedIps"
                      label="IPs aceitos"
                      {...{ model, bindInput, validation }}
                      render={(input) => <MultipleInput id={input.id} {...input.bindInput(input.id)} />}
                    />
                  )}

                  <Row>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="ticketsEnabled"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-SAC"
                            id={input.id}
                            label={t('TITLE_MENU_SAC')}
                            checked={!!input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="userNameInMessages"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-user-name-in-Messages"
                            id={input.id}
                            label={t('LABEL_SAC_NAME_ATTENDANT_BEGINNING_MESSAGE')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                  </Row>
                  <Row>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="disableDefaultTicketTransfer"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-disable-Default-Ticket-Transfer"
                            id={input.id}
                            label={t('LABEL_SAC_DISABLE_AUTOMATIC_TRANSFER')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="isQueueNotificationActive"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-is-Queue-Notification-Active"
                            id={input.id}
                            label={t('LABEL_VALIDATE_NOTIFICATION_QUEUE')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                  </Row>
                  <Row>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="newMessageNotification"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-new-Message-Notification"
                            id={input.id}
                            label={t('LABEL_SAC_NOTIFY_NEW_MESSAGE')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="ticketOpenNotification"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-ticket-Open-Notification"
                            id={input.id}
                            label={t('LABEL_SAC_NOTIFY_OPENING_OF_TICKETS')}
                            checked={!!input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                  </Row>
                  <Row>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="ticketTransferNotification"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-ticket-Transfer-Notification"
                            id={input.id}
                            label={t('LABEL_SAC_NOTIFY_TRANSFER_OF_TICKETS')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="topicRequired"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-topic-Required"
                            id={input.id}
                            label={t('LABEL_SAC_MAKE_SUBJECT_REQUIRED')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                  </Row>
                  <Row>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="showSupportInfo"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-show-Support-Info"
                            id={input.id}
                            label={t('LABEL_SAC_SHOW_SUPPORT_INFORMATION', { appName: config('whitelabel.appName') })}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                    <Col xl={6}>
                      <InputGroupWrapper
                        id="allowDuplicateNames"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-allow-Duplicate-Names"
                            id={input.id}
                            label={t('LABEL_SAC_DUPLICATE_NAMES')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <InputGroupWrapper
                        id="newMessageNotification"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-new-Message-Notification"
                            id={input.id}
                            label={t('LABEL_SAC_NOTIFY_NEW_MESSAGE')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />

                      <InputGroupWrapper
                        id="ticketOpenNotification"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-ticket-Open-Notification"
                            id={input.id}
                            label={t('LABEL_SAC_NOTIFY_OPENING_OF_TICKETS')}
                            checked={!!input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />

                      <InputGroupWrapper
                        id="ticketTransferNotification"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-ticket-Transfer-Notification"
                            id={input.id}
                            label={t('LABEL_SAC_NOTIFY_TRANSFER_OF_TICKETS')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />

                      <InputGroupWrapper
                        id="isQueueNotificationActive"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-is-Queue-Notification-Active"
                            id={input.id}
                            label={t('LABEL_VALIDATE_NOTIFICATION_QUEUE')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />

                      <InputGroupWrapper
                        id="showSupportInfo"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-show-Support-Info"
                            id={input.id}
                            label={t('LABEL_SAC_SHOW_SUPPORT_INFORMATION', { appName: config('whitelabel.appName') })}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                  </Row>

                  <Row className="mt-3">
                    <Col xl={4}>
                      <InputGroup
                        data-testid="account-input-ticket_Inactive_Time"
                        id="ticketInactiveTime"
                        label={t('LABEL_SAC_CALL_DOWNTIME_MINUTES')}
                        icon={<IconClock fill={TextColor} width="25" height="25" />}
                        type="number"
                        min={0}
                        max={525600}
                        {...{ bindInput, validation }}
                      />
                    </Col>
                    <Col xl={4}>
                      <InputGroup
                        id="protocolFormat"
                        label={t('LABEL_SAC_PROTOCOL_NUMBER_FORMAT')}
                        textPopover={
                          <>
                            <p>
                              <b>{t('LABEL_SAC_AVAILABLE_VARIABLES')}</b>
                            </p>
                            <code>{'{{date}}'}</code>: {t('FORMAT_DATE')} "YYYYMMDD"
                            <br />
                            <code>{'{{count}}'}</code>: {t('COUNT_TICKETS')}
                          </>
                        }
                        data-testid="protocol-format"
                        placeholder="{{date}}{{count}}"
                        {...{ bindInput, validation }}
                      />
                    </Col>

                    <Col xl={4}>
                      <InputGroupWrapper
                        id="defaultDepartment"
                        label={t('LABEL_SAC_STANDARD_DEPARTMENT_TICKETS')}
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <GroupInput withIcon>
                            <DepartmentsSelect
                              icon={<IconDepartment fill={TextColor} width="25" height="25" />}
                              data-testid="account-select-default_department"
                              className="default-department-select"
                              stateId="defaultDepartmentSelect"
                              id={input.id}
                              value={input.model[input.id]}
                              onChange={(value) => input.setProperty(input.id, value)}
                              onBlur={() => input.validation.setTouched(input.id)}
                              hideArchived
                            />
                          </GroupInput>
                        )}
                      />
                    </Col>
                  </Row>
                </S.CardInfo>

                <S.CardInfo data-testid="account-label-tags">
                  <h3>{t('TAGS_DISPLAY_IN_CHAT')}</h3>
                  <div className="mt-3">
                    <div>
                      <Switch
                        id={'showTagsInChat'}
                        data-testid="switch-show-tags-in-chat"
                        label={t('CONFIGURE_TAGS_DISPLAY')}
                        checked={!!model.showTagsInChat}
                        onChange={(e) => {
                          setProperty('showTagsInChat', e.target.checked)
                          if (!e.target.checked) {
                            setProperty('userCanChangeVisibilityTagsOnChat', false)
                          }
                        }}
                      />
                    </div>

                    {!!model.showTagsInChat && (
                      <div>
                        <Switch
                          id={'userCanChangeVisibilityTagsOnChat'}
                          data-testid="switch-user-can-change-visibility-tags-on-chat"
                          label={t('ALLOW_OPERATORS_TOGGLE_TAGS_DISPLAY')}
                          checked={!!model.userCanChangeVisibilityTagsOnChat}
                          onChange={(e) => setProperty('userCanChangeVisibilityTagsOnChat', e.target.checked)}
                          style={{ margin: 0 }}
                        />
                      </div>
                    )}
                  </div>
                </S.CardInfo>
              </TabsContent>

              <TabsContent value="workPlan">
                <h3>{t('LABEL_TIMETABLE_COMPANY')}</h3>

                <div className="workPlanStyle">
                  <InputGroupWrapper
                    id="workPlan"
                    render={() => (
                      <WorkPlan value={model.workPlan} onChange={(workPlan) => setProperty('workPlan', workPlan)} />
                    )}
                  />
                </div>

                {accountsFlags.isEnable(user.account.settings.flags || {}, 'absence-management') && (
                  <S.CardInfo>
                    <AbsenceManagement
                      t={t}
                      bindInput={bindInput}
                      validation={validation}
                      model={model}
                      setModel={setModel}
                    />
                  </S.CardInfo>
                )}
              </TabsContent>

              <TabsContent value="security">
                <S.CardInfo data-testid="account-label-password">
                  <h3>{t('CONFIG_PASS_USERS')}</h3>

                  <Row>
                    <Col>
                      <InputGroupWrapper
                        id="isPasswordExpirationActive"
                        className="mb-0"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <>
                            <Switch
                              id={input.id}
                              data-testid="switch-is_Password_Expiration_Active"
                              label={t('LABEL_PASSWORD_EXPIRATION_ACTIVE')}
                              checked={!!input.model[input.id]}
                              onChange={(e) => {
                                if (!e.target.checked) {
                                  input.setProperty('expirationPasswordTime', null)
                                }
                                return input.setProperty(input.id, e.target.checked)
                              }}
                            />
                            <span className="ml-2">
                              <HelpPopover
                                title={t('LABEL_PASSWORD_EXPIRATION_ACTIVE')}
                                body={t('PASSWORD_EXPIRATION_TOOLTIP')}
                              ></HelpPopover>
                            </span>
                          </>
                        )}
                      />
                      {model.isPasswordExpirationActive && (
                        <Row>
                          <Col md={8}>
                            <InputGroupWrapper
                              id="expirationPasswordTime"
                              label={t('LABEL_PASSWORD_EXPIRATION_TIME')}
                              {...{
                                bindInput,
                                validation,
                                model,
                                setProperty,
                              }}
                              render={(input) => (
                                <Select
                                  data-testid="account-input-expiration_Password_Time"
                                  value={input.model[input.id]?.toString() || '5'}
                                  onValueChange={(value) => input.setProperty(input.id, parseInt(value))}
                                >
                                  <SelectTrigger id={input.id} style={{ marginBottom: 24 }}>
                                    <SelectValue placeholder={t('SELECT_EXPIRATION_TIME')} />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="5">5 {t('DAYS')}</SelectItem>
                                    <SelectItem value="15">15 {t('DAYS')}</SelectItem>
                                    <SelectItem value="30">30 {t('DAYS')}</SelectItem>
                                    <SelectItem value="60">60 {t('DAYS')}</SelectItem>
                                    <SelectItem value="90">90 {t('DAYS')}</SelectItem>
                                    <SelectItem value="120">120 {t('DAYS')}</SelectItem>
                                  </SelectContent>
                                </Select>
                              )}
                            />
                          </Col>
                        </Row>
                      )}

                      <InputGroupWrapper
                        id="changeUserPasswordOnFirstAccess"
                        className="mb-0"
                        {...{
                          bindInput,
                          validation,
                          model,
                          setProperty,
                        }}
                        render={(input) => (
                          <Switch
                            data-testid="switch-change-user-password-on-first-access"
                            id={input.id}
                            label={t('SHOW_PASS_FIRST_ACESS')}
                            checked={input.model[input.id]}
                            onChange={(e) => input.setProperty(input.id, e.target.checked)}
                          />
                        )}
                      />
                    </Col>
                    <Col>
                      <h5>{t('ADMIN_TYPE_CREATE')}</h5>
                      <div
                        className="d-flex flex-column ml-4"
                        onChange={(e) => {
                          setProperty('userPasswordCreationMethod', e.target.value)
                        }}
                      >
                        <div>
                          <Input
                            id="manual"
                            type="radio"
                            value="manual"
                            checked={model.userPasswordCreationMethod === 'manual'}
                            color="primary"
                          />
                          <Label htmlFor="manual">{t('MANUAL_CREATE')}</Label>
                        </div>
                        <div>
                          <Input
                            id="automatic"
                            value="automatic"
                            type="radio"
                            checked={model.userPasswordCreationMethod === 'automatic'}
                            color="primary"
                          />
                          <Label htmlFor="automatic">{t('GENERATE_PASS')}</Label>
                        </div>
                        <div>
                          <Input
                            id="link"
                            value="link"
                            type="radio"
                            checked={model.userPasswordCreationMethod === 'link'}
                            color="primary"
                          />
                          <Label htmlFor="link">{t('SEND_LINK')}</Label>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </S.CardInfo>

                <S.CardInfo data-testid="account-label-2fa">
                  <h3>{t('TWO_FACTOR_AUTHENTICATION')}</h3>
                  <Row>
                    <Col xl={6} md={6}>
                      <Switch
                        id={'twoFactorAuthMandatory'}
                        data-testid="switch-two-factor-auth-mandatory"
                        label={t('MAKE_MANDATORY_FOR_ALL_USERS')}
                        checked={!!model.twoFactorAuthMandatory}
                        onChange={(e) => {
                          if (!e.target.checked) {
                            setIsDisableModalOpen(true)
                          } else {
                            handleChangeMFAEverybody(true)
                          }
                        }}
                      />
                      <span className="ml-2">
                        <HelpPopover
                          title={t('accountPage:TWO_FACTOR_AUTH_MANDATORY_CAUTION_TITLE')}
                          body={t('accountPage:TWO_FACTOR_AUTH_MANDATORY_CAUTION_MESSAGE')}
                        ></HelpPopover>
                      </span>
                    </Col>
                  </Row>
                </S.CardInfo>

                <S.CardInfo>
                  <h3>{t('SWITCH_IP_RESTRICTION')}</h3>

                  <InputGroupWrapper
                    id="ipRestriction"
                    className="mb-0"
                    {...{
                      bindInput,
                      validation,
                      model,
                      setProperty,
                    }}
                    render={(input) => (
                      <Switch
                        data-testid="switchIpRestriction"
                        id={input.id}
                        label={t('SWITCH_IP_RESTRICTION')}
                        checked={!!input.model[input.id]}
                        onChange={(e) => input.setProperty(input.id, e.target.checked)}
                      />
                    )}
                  />

                  {model.ipRestriction && (
                    <InputGroupWrapper
                      className="mb-3"
                      id="allowedIps"
                      label={t('LABEL_IPS_ACCEPT')}
                      {...{ model, bindInput, validation }}
                      render={(input) => <MultipleInput id={input.id} {...input.bindInput(input.id)} />}
                    />
                  )}
                </S.CardInfo>

                {!!model.showTagsInChat && (
                  <Switch
                    id={'userCanChangeVisibilityTagsOnChat'}
                    data-testid="switch-user-can-change-visibility-tags-on-chat"
                    label={t('ALLOW_OPERATORS_TOGGLE_TAGS_DISPLAY')}
                    checked={!!model.userCanChangeVisibilityTagsOnChat}
                    onChange={(e) => setProperty('userCanChangeVisibilityTagsOnChat', e.target.checked)}
                    style={{ margin: 0 }}
                  />
                )}

                {accountsFlags.isEnable(user.account.settings.flags || {}, 'enable-smart-summary') && (
                  <S.CardInfo data-testid="auto-smart-summary" className="mt-5">
                    <div style={{ display: 'flex', alignItems: 'start', gap: '8px' }}>
                      <h3>{t('AUTO_SMART_SUMMARY')}</h3>
                    </div>
                    <div
                      className="mt-4"
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      <Switch
                        id={'autoGenerateSummaryOnTransfer'}
                        data-testid="switch-auto-generate-summary-on-transfer"
                        label={t('AUTO_GENERATE_SUMMARY_ON_TRANSFER')}
                        checked={!!model.autoGenerateSummaryOnTransfer}
                        onChange={(e) => setProperty('autoGenerateSummaryOnTransfer', e.target.checked)}
                      />
                      <Switch
                        id={'autoGenerateSummaryOnClosure'}
                        data-testid="switch-auto-generate-summary-on-closure"
                        label={t('AUTO_GENERATE_SUMMARY_ON_CLOSURE')}
                        checked={!!model.autoGenerateSummaryOnClosure}
                        onChange={(e) => setProperty('autoGenerateSummaryOnClosure', e.target.checked)}
                        style={{ margin: 0 }}
                      />
                    </div>
                  </S.CardInfo>
                )}
                {user.account?.settings?.flags?.['enable-sso'] && (
                  <S.CardInfo data-testid="account-sso">
                    <h3>Single Sign On (SSO)</h3>

                    <InputGroupWrapper
                      id="azureAdEnabled"
                      className="mb-0"
                      {...{
                        bindInput,
                        validation,
                        model,
                        setProperty,
                      }}
                      render={(input) => (
                        <Switch
                          data-testid={input.id}
                          id={input.id}
                          label="Azure AD"
                          checked={!!input.model[input.id]}
                          onChange={(e) => input.setProperty(input.id, e.target.checked)}
                        />
                      )}
                    />

                    {model.azureAdEnabled && (
                      <>
                        <Row>
                          <Col>
                            <InputGroup
                              id="azureAdLabel"
                              label={t('SSO_AZURE_AD_LABEL')}
                              {...{ model, bindInput, validation }}
                            />
                            <InputGroup
                              id="azureAdTenantId"
                              label={t('SSO_AZURE_AD_TENANT_ID')}
                              {...{ model, bindInput, validation }}
                            />
                          </Col>
                          <Col>
                            <InputGroup
                              id="azureAdClientId"
                              label={t('SSO_AZURE_AD_CLIENT_ID')}
                              {...{ model, bindInput, validation }}
                            />
                            <InputGroup
                              id="azureAdClientSecret"
                              label={t('SSO_AZURE_AD_CLIENT_SECRET')}
                              type="password"
                              {...{ model, bindInput, validation }}
                            />
                          </Col>
                        </Row>
                        <Row>
                          <Col md={6}>
                            <label htmlFor="azureAdCallback">{t('SSO_AZURE_AD_CALLBACK_URL')}</label>
                            <CopyToClipboard
                              id="azureAdCallback"
                              value={`${config('apiUrl')}/sso/azure-ad/${model.alias}/callback`}
                            />
                          </Col>
                        </Row>
                      </>
                    )}
                  </S.CardInfo>
                )}
              </TabsContent>
            </S.CardAccount>

            <br />
            <S.AccountFooter>
              <LoadingButton
                className="confirm"
                data-testid="company-button-save"
                color="primary"
                type="submit"
                disabled={!validation.isValid}
                onClick={handleSubmit}
                isLoading={isLoading}
              >
                {t('common:FORM_ACTION_SAVE')}
              </LoadingButton>
            </S.AccountFooter>
          </Form>
        </Tabs>
      </Container>
    </>
  )
}

export default memo(Account)
