import styled from 'styled-components'
import { SecondaryColor, DarkColor, PrimaryColor } from '../../styles/colors'

export const CardAccount = styled.section`
  border: 1px solid #e5e5e5;
  padding: 1.5rem;
  border-radius: 12px;
  background: white;
  margin-bottom: 1rem;
`

export const CardInfo = styled.div`
  &:not(:last-child) {
    margin-bottom: 2rem;
  }

  h3 {
    color: ${SecondaryColor};
  }

  b {
    color: ${DarkColor};
  }

  label {
    font-size: 12px;
    color: ${DarkColor};
    font-weight: 600;
  }

  .form-control {
    border-radius: 25px;
    border: 1px solid rgba(82, 101, 140, 0.15);
  }
`

export const GroupSelect = styled.div`
  border-bottom: 1px solid rgba(82, 101, 140, 0.15);
`

export const AccountFooter = styled.div`
  display: flex;

  .confirm {
    padding: 10px 40px;
  }
`
