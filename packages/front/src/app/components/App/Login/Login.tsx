import moment from 'moment'
import React, { useEffect, useCallback, FormEvent } from 'react'
import Helmet from 'react-helmet'
import reformed from 'react-reformed'
import { withRouter, RouteComponentProps } from 'react-router'
import Qs from 'qs'
import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import { useQuery } from '@tanstack/react-query'
import { useDebounce } from 'use-debounce'
import * as S from './styles'
import redirectIf from '../../common/unconnected/redirectIf'
import withValidation from '../../common/unconnected/withValidation'
import { required } from '../../../utils/validator/validators'
import InputGroup from '../../common/unconnected/InputGroup'
import logoUrl from '../../../assets/logos/logoUrl'
import config from '../../../../../config'
import { Form } from '../styles/common'
import SwitchLanguage from '../../common/unconnected/SwitchLanguage'
import { getUrlRedirect } from '../../../modules/auth/selectors'
import CheckToken from '../TwoFactorAuthentication/CheckToken'
import Icon from '../../common/unconnected/Icon'
import ssoApi from '../../../resources/sso/api'
import { Button } from '../../common/unconnected/ui/button'
import AccountSelect from './AccountSelect'

interface SSOOption {
  type: 'azure-ad'
  url: string
  label: string
  icon?: string | [string, string]
}

interface LoginModel {
  accountAlias: string
  username: string
  password: string
}

interface LoginProps extends RouteComponentProps {
  setModel: (model: Partial<LoginModel>) => void
  validation: any
  hydrateLogin: (data: any) => void
  attemptLogin: (model: LoginModel) => void
  model: LoginModel
  bindInput: (id: string) => {
    value: string
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  }
  setProperty: (key: keyof LoginModel, value: string) => void
  isLoading: boolean
  getShowTwoFactorAuthTokenModal: boolean
  showTwoFactorAuthTokenModal: (params: { show: boolean }) => void
}

const useSSO = (accountAlias: string) => {
  const [debouncedAlias] = useDebounce(accountAlias, 800, { leading: true })

  const { data: ssoOptions = [] } = useQuery({
    queryKey: ['sso-options', debouncedAlias],
    queryFn: () => ssoApi.getOptionsForAccount(debouncedAlias),
    enabled: !!debouncedAlias,
    select: (data: any): SSOOption[] =>
      data?.map?.((sso: SSOOption) => ({
        ...sso,
        icon: {
          'azure-ad': ['fab', 'microsoft'],
        }[sso.type],
      })) || [],
  })

  return ssoOptions
}

const Login: React.FC<LoginProps> = ({
  setModel,
  validation,
  history,
  hydrateLogin,
  attemptLogin,
  model,
  bindInput,
  setProperty,
  isLoading,
  getShowTwoFactorAuthTokenModal,
  showTwoFactorAuthTokenModal,
}) => {
  const trim = (value = '') => value.replace(/[\u200B-\u200D\uFEFF]/g, '').trim()
  const { t } = useTranslation(['login', 'common'])
  const isOnClusterMode = config('isOnClusterMode')

  const ssoAccountAlias = isOnClusterMode ? model.accountAlias : config('subdomain')
  const ssoOptions = useSSO(ssoAccountAlias)

  useEffect(() => {
    const query = history.location.search && Qs.parse(history.location.search.substring(1))
    const authData = query && query.auth && JSON.parse(atob(query.auth as string))

    if (authData) {
      hydrateLogin(authData)
      return
    }

    setModel({
      ...(isOnClusterMode && {
        accountAlias: '',
      }),
      username: '',
      password: '',
    })

    validation.setRules({
      ...(isOnClusterMode && {
        accountAlias: [[required, () => t('common:REQUIRED_FIELD')]],
      }),
      username: [[required, () => t('common:REQUIRED_FIELD')]],
      password: [[required, () => t('common:REQUIRED_FIELD')]],
    })
  }, [])

  const handleSubmit = useCallback(
    (event: FormEvent) => {
      event.preventDefault()

      validation.validateAll().then((valid) => {
        if (!valid) return
        attemptLogin(model)
      })
    },
    [isOnClusterMode, model, validation, attemptLogin],
  )

  return (
    <>
      <S.Login>
        <Helmet title="Login" />
        <S.ContentLogin>
          <S.ContentHeader>
            <img
              data-testid="image-Logo-login"
              src={logoUrl(true)}
              alt={config('whitelabel.appName')}
              style={{ width: 150 }}
            />
          </S.ContentHeader>

          <Form onSubmit={handleSubmit}>
            {isOnClusterMode && (
              <AccountSelect
                validation={validation}
                value={model.accountAlias}
                onChange={(v) => setProperty('accountAlias', v)}
              />
            )}

            <InputGroup
              id="username"
              label={t('INPUT_PLACEHOLDER_EMAIL')}
              autoComplete="username"
              data-testid="login-input-username"
              {...{ bindInput, validation }}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setProperty('username', trim(e.target.value))}
            />

            <InputGroup
              id="password"
              data-testid="login-input-password"
              value={model.password}
              isPassword={true}
              autoComplete="current-password"
              placeholder={t('INPUT_PLACEHOLDER_PASSWORD')}
              {...{ bindInput, validation }}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setProperty('password', trim(e.target.value))}
            />

            <Link
              id="reset-pass-link"
              to={`/reset-password?account=${model.accountAlias}&email=${model.username}`}
              data-testid="login-link-forgot_password"
            >
              {t('LINK_FORGOT_PASSWORD')}
            </Link>

            <S.LoginButton type="submit" data-testid="login-button-login" disabled={isLoading}>
              {t('BUTTON_LOGIN')}
            </S.LoginButton>

            {ssoOptions.length > 0 && (
              <>
                <S.OrDivider>{t('SSO_DIVIDER')}</S.OrDivider>

                {ssoOptions.map((sso) => (
                  <div className="d-flex justify-content-center mt-3" key={sso.type}>
                    <Button as="a" variant="outline" href={sso.url}>
                      <Icon name={sso.icon} className="mr-2" /> {sso.label}
                    </Button>
                  </div>
                ))}
              </>
            )}
          </Form>
          <S.Footer>
            <SwitchLanguage renderName data-testid="login-list-language" />

            <S.Copyright>
              {config('whitelabel.appName')} © {moment(new Date()).format('YYYY')}{' '}
              <Link
                to={{ pathname: config('whitelabel.termsOfUseUrl') }}
                target="_blank"
                rel="noopener noreferrer"
                data-testid="login-link-terms_of_use"
                style={{ marginLeft: '10px', marginRight: '10px' }}
              >
                {t('TERMS_LINK')}
              </Link>
              <span>|</span>
              <Link
                to={{ pathname: config('whitelabel.privacyPolicyUrl') }}
                target="_blank"
                rel="noopener noreferrer"
                data-testid="login-link-terms_of_use"
                style={{ marginLeft: '10px', marginRight: '10px' }}
              >
                {t('POLICY_PRIVACY')}
              </Link>
            </S.Copyright>
          </S.Footer>
        </S.ContentLogin>
      </S.Login>
      {getShowTwoFactorAuthTokenModal && (
        <CheckToken
          setupMode={false}
          isOpen
          model={model}
          goBack={() => showTwoFactorAuthTokenModal({ show: false })}
        />
      )}
    </>
  )
}

export default redirectIf(
  ({ isAuthenticated }: { isAuthenticated: boolean }) => isAuthenticated,
  () => useSelector(getUrlRedirect) || '/',
)(reformed()(withValidation()(withRouter(Login))))
