import styled from 'styled-components'
import { Button } from 'reactstrap'
import { MalachiteColor, TextColor } from '../../../App/styles/colors'

export const ButtonDelete = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease-in-out;
  border-radius: 50%;
  cursor: pointer;
  padding-left: 0px;

  &:hover {
    border-color: transparent;
    background: #f9f9f9;
  }
`

export const ButtonWeekDays = styled(Button)`
  background: ${(props) => (props.active ? MalachiteColor : '#fff')};
  border: 1px solid #f0f0f0;
  color: ${TextColor};
  font-weight: 500;
  border-radius: 0px;
  padding: 0.5rem;
  box-shadow: none !important;
`

export const ButtonGroup = styled.div`
  display: flex;
  .active {
    background: ${MalachiteColor}!important;
    border-color: #f0f0f0 !important;
  }
`

export const NoResults = styled.div`
  display: flex;
  justify-content: center;
  padding: 2rem;
  color: ${TextColor};
`
