import React, { memo } from 'react'
import isBefore from 'date-fns/isBefore'
import isAfter from 'date-fns/isAfter'
import format from 'date-fns/format'
import parse from 'date-fns/parse'
import { useTranslation } from 'react-i18next'
import Datetime from '../Datetime'
import Icon from '../Icon'
import FieldArray2, { ArrayHelpers } from '../FieldArray2'
import WeekdaysSelector from './WeekDaysSelector'
import { Table, TableHead, TableColumn, TableBody, TableRow, TableCell } from '../../../App/styles/table'
import { IconTrash } from '../IconDigisac'
import { DangerColor, PrimaryColor } from '../../../App/styles/colors'
import Button from '../Button'
import * as S from './styles'

const createRule = () => ({
  weekDays: [],
  start: null,
  end: null,
})

const TIME_FORMAT = 'HH:mm'

const now = new Date()

export const toDate = (timeString: string) => {
  if (!timeString) return timeString

  try {
    return parse(timeString, TIME_FORMAT, now)
  } catch (e) {
    return null
  }
}

export const toTime = (date: Date | string) => (date instanceof Date ? format(date, TIME_FORMAT) : date)

function WorkPlan(props) {
  const { value = [], onChange } = props
  const { t } = useTranslation(['workPlan'])

  return (
    <FieldArray2
      value={value}
      onChange={onChange}
      render={(arr: ArrayHelpers) => (
        <>
          <Table mtNone data-testid="modal-timetable">
            <TableHead disableGap gridColumns="40% 25% 25% 10%">
              <TableColumn>{t('COLUMN_NAME_DAYS')}</TableColumn>
              <TableColumn>{t('COLUMN_NAME_START')}</TableColumn>
              <TableColumn>{t('COLUMN_NAME_TERMINATION')}</TableColumn>
              <TableColumn />
            </TableHead>
            <TableBody>
              {value.map((rule, i) => (
                <TableRow background="#fff" firstChildPadding="0px" gridColumns="40% 25% 25% 10%" disableGap key={i}>
                  <TableCell className="pl-3" data-testid="timetable-button-days-selector">
                    <WeekdaysSelector
                      value={rule.weekDays}
                      onChange={(weekDays) =>
                        arr.set(i, {
                          ...rule,
                          weekDays,
                        })
                      }
                    />
                  </TableCell>
                  <TableCell disableOverflow className="pr-4" data-testid="timetable-start-time">
                    <Datetime
                      id={`startTime_${i}`}
                      mode="time"
                      value={toDate(rule.start)}
                      onChange={(start) => {
                        const end = rule.end && toDate(rule.end)

                        const startOrEnd = end && isAfter(start, end) ? end : start

                        arr.set(i, {
                          ...rule,
                          start: toTime(startOrEnd),
                        })
                      }}
                    />
                  </TableCell>
                  <TableCell disableOverflow className="pr-4" data-testid="timetable-endtime">
                    <Datetime
                      id={`endTime_${i}`}
                      mode="time"
                      value={toDate(rule.end)}
                      onChange={(end) => {
                        const start = rule.start && toDate(rule.start)

                        const endOrStart = start && isBefore(end, start) ? start : end

                        arr.set(i, {
                          ...rule,
                          end: toTime(endOrStart),
                        })
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <S.ButtonDelete onClick={() => arr.remove(i)} data-testid="timetable-button-delete">
                      <IconTrash fill={DangerColor} width="25" height="25" />
                    </S.ButtonDelete>
                  </TableCell>
                </TableRow>
              ))}
              {value.length === 0 && <S.NoResults>{t('NO_RESULTS')}</S.NoResults>}
            </TableBody>
            <div className="d-flex mt-3">
              <Button
                background={PrimaryColor}
                size="lg"
                onClick={() => arr.push(createRule())}
                className="mt-2"
                onBlur={props.onBlur}
                data-testid="company-button-add_timetable_rule"
              >
                <Icon name="plus" /> {t('BUTTON_ADD_RULE')}
              </Button>
            </div>
          </Table>
        </>
      )}
    />
  )
}

export default memo(WorkPlan)
