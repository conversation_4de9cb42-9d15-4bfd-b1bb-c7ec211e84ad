import React, { memo, useRef } from 'react'
import { Popover, <PERSON>overBody, PopoverHeader } from 'reactstrap'
import useToggle from '../../../../hooks/useToggle'
import { CircleHelp } from 'lucide-react'

function HelpPopover({ title, body }) {
  const buttonRef = useRef()
  const { toggle, isOpen } = useToggle(false)

  return (
    <>
      <span className="text-muted hover-pointer">
        <CircleHelp size={16} color="#365497" style={{ marginBottom: 5 }} ref={buttonRef} />
      </span>
      <Popover placement="top" isOpen={isOpen} target={buttonRef} trigger="legacy" toggle={toggle}>
        <PopoverHeader>{title}</PopoverHeader>
        <PopoverBody>{body}</PopoverBody>
      </Popover>
    </>
  )
}

export default memo(HelpPopover)
