import React, { useCallback, memo, useState } from 'react'
import { FormGroup, Input, FormFeedback, Popover, PopoverBody, PopoverHeader } from 'reactstrap'
import { Input, InputProps } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import * as S from './styles'
import * as SL from '../../../App/Login/styles'
import { TextColor } from '../../../App/styles/colors'
import { IconHidePassword, IconShowPassword, IconInfo } from '../IconDigisac/index'
import { CircleHelp, Info } from 'lucide-react'

const styles = {
  formFeedback: { display: 'block !important' },
  popoverIcon: {
    cursor: 'pointer',
    marginLeft: 10,
    marginBottom: 1,
  },
}

export const InputGroupWrapper = memo((props) => {
  const {
    validation,
    label,
    textColor,
    id,
    children,
    render,
    description,
    required = false,
    textPopover,
    headerPopover,
    isPassword = false,
    newPopover = false,
    classNameInputGroupWrapper = '',
    infoPopover = false,
    className,
    ...rest
  } = props
  const errors = validation && validation.errors[id]
  const childProps = {
    ...rest,
    id,
    label,
    validation,
  }

  const [openPopover, setOpenPopover] = useState(false)

  const togglePopover = () => {
    setOpenPopover(!openPopover)
  }

  const PopoverIcon = infoPopover ? Info : CircleHelp

  return (
    <FormGroup color={errors ? 'danger' : null} className={className}>
      <S.LabelWrapper>
        {!!label && (
          <S.Label htmlFor={id} style={{ color: textColor }}>
            {label}{' '}
            {required && (
              <strong className="text-danger" title="Campo obrigatório">
                *
              </strong>
            )}
            {textPopover && (
              <>
                <PopoverIcon id={`${id}-help`} size={16} color="#365497" style={styles.popoverIcon} />

                <Popover
                  target={`${id}-help`}
                  placement="top"
                  isOpen={openPopover}
                  toggle={togglePopover}
                  trigger="legacy"
                >
                  {headerPopover && <PopoverHeader>{headerPopover}</PopoverHeader>}
                  <PopoverBody>{textPopover}</PopoverBody>
                </Popover>
              </>
            )}
          </S.Label>
        )}
        {Boolean(description) && <S.Description>{description}</S.Description>}
      </S.LabelWrapper>
      {children || render(childProps)}
      {errors && (
        <FormFeedback data-testid="Required-field" style={styles.formFeedback}>
          {errors.messages[0]}
        </FormFeedback>
      )}
    </FormGroup>
  )
})

interface InputGroupProps extends InputProps {
  disabled?: boolean
  id: string
  label: string | React.ReactNode
  placeholder?: string
  required?: boolean
  rows?: number
  type?: string
  after?: string | React.ReactNode
}

const InputGroup = memo((props: InputGroupProps) => {
  const {
    validation,
    bindInput,
    label,
    id,
    title,
    type = 'text',
    noPlaceholder,
    before = null,
    after = null,
    icon = null,
    isPassword = false,
    classNameInputGroup = '',
    ...rest
  } = props
  const errors = validation && validation.errors[id]
  const [showPassword, setShowPassword] = useState(false)

  const Component = type === 'textarea' ? Textarea : Input

  if ('value' in (rest || {})) {
    rest.value = rest.value || ''
  }

  const handleBlur = useCallback(() => {
    validation && validation.setTouched(id)
  }, [validation && validation.setTouched, id])

  const Password = () => {
    if (!isPassword) return <></>
    return (
      <>
        <FontAwesomeIcon icon={showPassword ? 'eye-slash' : 'eye'} onClick={() => setShowPassword(!showPassword)} />{' '}
        {props.label}
      </>
    )
  }

  const typePassword = () => (showPassword ? 'text' : 'password')

  return (
    <S.InputGroup className={classNameInputGroup}>
      <InputGroupWrapper {...props}>
        {before}
        {!!icon && (
          <div>
            <S.IconWrapper>{icon}</S.IconWrapper>
          </div>
        )}
        {isPassword ? (
          <>
            <SL.InputGroup>
              <input
                id={id}
                data-testid={`InputGroupWrapper_${props.label}_${props.id}`}
                title={title}
                style={{ paddingLeft: icon && '40px' }}
                type={!isPassword ? type : typePassword()}
                placeholder={!noPlaceholder && label}
                className="form-control"
                onBlur={handleBlur}
                {...(bindInput ? bindInput(id) : {})}
                {...rest}
              />
              <SL.Icon onClick={() => setShowPassword(!showPassword)}>
                <>{showPassword ? <IconHidePassword fill="#52658C" /> : <IconShowPassword fill="#52658C" />}</>
              </SL.Icon>
            </SL.InputGroup>
            {after}
          </>
        ) : (
          <>
            <Input
              type={type}
              id={id}
              title={title}
              data-testid={`InputGroupWrapper_${props.label}_${props.id}`}
              style={{ paddingLeft: icon && '40px' }}
              placeholder={!noPlaceholder && label}
              onBlur={handleBlur}
              valid={errors ? false : undefined}
              {...(bindInput ? bindInput(id) : {})}
              {...rest}
            />
            {after}
          </>
        )}
      </InputGroupWrapper>
    </S.InputGroup>
  )
})

export default InputGroup
