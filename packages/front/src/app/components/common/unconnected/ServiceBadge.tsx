import React, { memo } from 'react'
import { Badge } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import Icon from './Icon'
import { getIconTypeAndColorByType } from './ServiceIcon'
import { fontFamily } from '../../App/styles/common'
import { IconGoogleBusinessMessage } from './IconDigisac'
import { isEqual } from '../../../utils/logic'
import { TextLimiter } from './TextLimiter'

const style = {
  serviceBadge: {
    padding: '5px 10px',
    color: '#FFFFFF',
    fontFamily: `${fontFamily}`,
    borderRadius: '16px',
    fontSize: '12px',
    fontWeight: '400',
  },
  icon: {
    color: 'white!important',
  },
}

const ServiceBadge = ({ service, ...rest } = {}) => {
  const { t } = useTranslation('servicesPage')
  if (!service) return null
  const { name } = service
  const [icon, color] = getIconTypeAndColorByType(service.type)

  return (
    <Badge
      color="primary"
      className="mr-2"
      title={`${t('TITLE_SERVICE')}: ${name}`}
      {...rest}
      style={{ background: color, ...style.serviceBadge }}
    >
      {service.type === 'google-business-message' ? (
        <IconGoogleBusinessMessage className="mr-1" width="13px" height="13px" fill="#FFFFFF" />
      ) : (
        <Icon name={icon} fixedWidth className="mr-1" style={style.icon} />
      )}
      <TextLimiter>{name}</TextLimiter>
    </Badge>
  )
}

const propsAreEqual = (props, nextProps) => isEqual(props.service, nextProps.service, ['name', 'type'])

export default memo(ServiceBadge, propsAreEqual)
