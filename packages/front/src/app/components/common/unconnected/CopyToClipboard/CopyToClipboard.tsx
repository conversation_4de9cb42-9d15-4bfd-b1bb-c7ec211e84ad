import React, { useState } from 'react'
import { InputGroupAddon, InputGroup, Input, Tooltip } from 'reactstrap'
import Clipboard from 'react-clipboard.js'
import { useTranslation } from 'react-i18next'

const styles = {
  clipBoard: {
    background: '#52658C',
    padding: '4px 20px',
    borderRadius: '20px',
    zIndex: 0,
    fontFamily: 'Inter, sans-serif',
    fontWeight: '500',
    height: '30px',
  },
  inputCopy: {
    background: 'none',
    fontFamily: 'Inter, sans-serif',
    border: 'none',
    color: '#6c757d',
    fontSize: '16px',
    padding: 0,
  },
  invisible: {
    display: 'none',
  },
}

const CopyToClipboard = ({ value, disabled = false, show = true, id = undefined }) => {
  const [tooltipOpen, setTooltipOpen] = useState(false)

  const { t } = useTranslation('common')

  const onSuccess = () => {
    setTooltipOpen(true)
    setTimeout(() => setTooltipOpen(false), 1500)
  }

  return (
    <InputGroup id="clipboard">
      <Input style={!show ? styles.invisible : styles.inputCopy} disabled={disabled} value={value} id={id} />
      <InputGroupAddon
        data-testid="chat-button-copy_protocol"
        className="d-flex align-items-center"
        title={t('TOOTIP_BUTTON_COPY')}
        addonType="prepend"
      >
        <Clipboard
          className="btn btn-primary d-flex align-items-center"
          style={styles.clipBoard}
          data-clipboard-text={value}
          onSuccess={onSuccess}
        >
          {t('BUTTON_COPY')}
        </Clipboard>

        <Tooltip placement="top" isOpen={tooltipOpen} target="clipboard">
          {t('TOOLTIP_COPIED')}
        </Tooltip>
      </InputGroupAddon>
    </InputGroup>
  )
}

export default CopyToClipboard
