import React, { memo } from 'react'
import { Badge } from 'reactstrap'
import { useTranslation } from 'react-i18next'
import { TextColor } from '../../App/styles/colors'
import { fontFamily } from '../../App/styles/common'
import { IconFlowChart } from './IconDigisac'
import { isEqual } from '../../../utils/logic'
import { TextLimiter } from './TextLimiter/TextLimiter'

const style = {
  departmentBadge: {
    padding: '2px 10px',
    background: `${TextColor}`,
    color: '#FFFFFF',
    fontFamily: `${fontFamily}`,
    borderRadius: '16px',
    fontSize: '12px',
    fontWeight: '400',
    display: 'flex',
    alignItems: 'center',
  },
}

const DepartmentBadge = ({ department, ...rest } = {}) => {
  if (!department) return null
  const { t } = useTranslation('chatPage')

  const { name } = department

  return (
    <Badge style={style.departmentBadge} color="secondary" title={`${t('DEPARTMENT_OF_THE_CALL')}: ${name}`} {...rest}>
      <IconFlowChart fill="#fff" width="18" height="18" className="mr-1" />
      <TextLimiter>{name}</TextLimiter>
    </Badge>
  )
}

const propsAreEqual = (props, nextProps) => isEqual(props.department, nextProps.department, ['name'])

export default memo(DepartmentBadge, propsAreEqual)
