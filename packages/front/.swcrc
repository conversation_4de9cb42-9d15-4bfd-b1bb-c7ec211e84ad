{
 "$schema": "https://swc.rs/schema.json",
 "jsc": {
  "baseUrl": "./src",
  "parser": {
   "syntax": "typescript",
   "jsx": true,
   "decorators": true,
   "classPrivateProperties": true,
   "decoratorsBeforeExport": true,
   "dynamicImport": true,
   "optionalChaining": true
  },
  "transform": {
   "legacyDecorator": true,
   "decoratorMetadata": true,
   "react": {
    "runtime": "automatic"
   }
  },
  "target": "es2015", //versões inferiores geram código incorreto
  "loose": true,
  "keepClassNames": true,
  "experimental": {
   "cacheRoot": "/tmp/.swc",
   "plugins": [
    [
     "@swc/plugin-styled-components",
     {
      "displayName": true,
      "ssr": true
     }
    ]
   ]
  }
 },
 "module": {
  "type": "commonjs",
  "ignoreDynamic": true //Necessário para magic comments do webpack
 }
}