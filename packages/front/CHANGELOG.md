# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.60.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.60.0-mr-3630.2...v3.60.1) (2025-09-16)

**Note:** Version bump only for package @digisac/front





# [3.60.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.60.0-rc.10...v3.60.0) (2025-09-15)



# [3.59.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.60.0-mr-3453.15...v3.59.0) (2025-09-09)

**Note:** Version bump only for package @digisac/front





# [3.59.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.59.0-rc.29...v3.59.0) (2025-09-09)

**Note:** Version bump only for package @digisac/front





## [3.58.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.58.1-mr-3562.5...v3.58.2) (2025-09-08)

**Note:** Version bump only for package @digisac/front





# [3.58.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.58.0-rc.21...v3.58.0) (2025-09-02)

**Note:** Version bump only for package @digisac/front





## [3.57.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.57.2-mr-3500.5...v3.57.3) (2025-09-02)

**Note:** Version bump only for package @digisac/front





## [3.57.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.57.1-mr-3541.2...v3.57.2) (2025-09-01)

**Note:** Version bump only for package @digisac/front





## [3.57.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.57.0-mr-3560.1...v3.57.1) (2025-08-27)

**Note:** Version bump only for package @digisac/front





# [3.57.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.57.0-rc.15...v3.57.0) (2025-08-26)

**Note:** Version bump only for package @digisac/front





# [3.56.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.56.0-rc.20...v3.56.0) (2025-08-18)

**Note:** Version bump only for package @digisac/front





## [3.55.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.1-mr-3518.1...v3.55.1) (2025-08-15)

**Note:** Version bump only for package @digisac/front





# [3.55.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.3...v3.55.0) (2025-08-11)



# [3.55.0-rc.15](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3438.4...v3.55.0-rc.15) (2025-08-08)



# [3.55.0-mr-3438.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3438.2...v3.55.0-mr-3438.3) (2025-08-08)


### Bug Fixes

* update notification message for insufficient credits ([f9a1382](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/f9a13822144a20fc2f9ad5cc93176b1ff33c5511))



# [3.55.0-mr-3438.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-rc.14...v3.55.0-mr-3438.2) (2025-08-08)


### Bug Fixes

* missing translation token ([492e73d](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/492e73d5a70667f394527c907f6b027153ecf7f3))



# [3.55.0-rc.14](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3438.1...v3.55.0-rc.14) (2025-08-07)



# [3.55.0-mr-3438.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.2...v3.55.0-mr-3438.1) (2025-08-07)



# [3.55.0-mr-3483.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3427.13...v3.55.0-mr-3483.0) (2025-08-06)



# [3.55.0-rc.13](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3427.12...v3.55.0-rc.13) (2025-08-06)



# [3.55.0-mr-3427.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-rc.12...v3.55.0-mr-3427.12) (2025-08-06)


### Bug Fixes

* **ai-agent-dashboard:** adiciona agente inteligente ao exportar os dados de uso no dashboard ([c63e37e](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c63e37e887b521d46acdc7149ddff64b90edb40b))
* remove needless showAgentToast call ([fa68def](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/fa68defa69a823b67e5bc084ec76da80b6eda041))


### Features

* block agent credit limits ([200ac5b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/200ac5bd057026889f2528c07334fe8405028971))



# [3.55.0-rc.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3427.11...v3.55.0-rc.12) (2025-08-06)



# [3.55.0-mr-3427.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-rc.11...v3.55.0-mr-3427.11) (2025-08-06)



# [3.55.0-rc.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-rc.10...v3.55.0-rc.11) (2025-08-06)


### Bug Fixes

* lint at pipelineResource.test ([b5cdacb](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/b5cdacb63b1cd3d64ce1412c9d8a55d13247ba87))



# [3.55.0-rc.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3431.3...v3.55.0-rc.10) (2025-08-05)



# [3.55.0-mr-3431.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.1...v3.55.0-mr-3431.3) (2025-08-05)



# [3.55.0-rc.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3468.2...v3.55.0-rc.9) (2025-08-05)



# [3.55.0-mr-3468.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-rc.8...v3.55.0-mr-3468.2) (2025-08-05)



# [3.55.0-rc.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3459.3...v3.55.0-rc.8) (2025-08-05)



# [3.55.0-mr-3459.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3431.2...v3.55.0-mr-3459.3) (2025-08-05)



# [3.55.0-mr-3431.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3431.1...v3.55.0-mr-3431.2) (2025-08-05)



# [3.55.0-mr-3431.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3459.2...v3.55.0-mr-3431.1) (2025-08-05)


### Features

* corrige erro do copiloto tradução ([a1cd7bb](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a1cd7bb0491567da650d3419d8542d51e0aeb4bd))



# [3.55.0-mr-3427.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.1-mr-3475.0...v3.55.0-mr-3427.10) (2025-08-05)



# [3.55.0-mr-3427.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3427.8...v3.55.0-mr-3427.9) (2025-08-05)



# [3.54.0-mr-3427.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-rc.7...v3.54.0-mr-3427.8) (2025-08-05)


### Features

* **ai-agent-dashboard:** adiciona input de visualização de creditos do agente inteligente na configuração da conta ([72029d4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/72029d472b3fa75ee0d169d6e7bd0398575752cf))



# [3.55.0-rc.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0...v3.55.0-rc.7) (2025-08-05)


### Bug Fixes

* **version:** versao alterada para 3.55.0-rc.6 ([8866639](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/88666390da0983d6ae39bf74e7e94ff7c8614f7f))



## [3.55.5-mr-3439.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.5-mr-3439.1...v3.55.5-mr-3439.2) (2025-08-04)



## [3.55.5-mr-3439.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-rc.6...v3.55.5-mr-3439.1) (2025-08-04)



# [3.55.0-rc.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.1-mr-3443.3...v3.55.0-rc.6) (2025-08-04)


### Bug Fixes

* **version:** versao alterada para 3.55.0-rc.5 ([26051bd](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/26051bd370f523841211935c713cb01fe48f6ae1))



## [3.55.1-mr-3443.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.51.4-mr-3466.1...v3.55.1-mr-3443.3) (2025-08-04)



# [3.55.0-rc.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-rc.16...v3.55.0-rc.5) (2025-07-31)



# [3.55.0-mr-3465.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-rc.4...v3.55.0-mr-3465.0) (2025-07-31)



# [3.55.0-rc.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3431.0...v3.55.0-rc.4) (2025-07-31)



# [3.55.0-mr-3431.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3468.1...v3.55.0-mr-3431.0) (2025-07-31)



# [3.55.0-mr-3468.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3468.0...v3.55.0-mr-3468.1) (2025-07-31)



# [3.55.0-mr-3468.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3467.0...v3.55.0-mr-3468.0) (2025-07-31)


### Bug Fixes

* ajustando posição dos botões do filtro ([5f896bd](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/5f896bd091de81a48ef0e7a46e92491280013811))



# [3.55.0-mr-3458.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3458.1...v3.55.0-mr-3458.0) (2025-07-31)



## [3.53.1-mr-3443.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.1-mr-3443.1...v3.53.1-mr-3443.2) (2025-07-31)



## [3.53.1-mr-3443.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3427.6...v3.53.1-mr-3443.1) (2025-07-31)



# [3.54.0-mr-3427.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-rc.15...v3.54.0-mr-3427.6) (2025-07-30)


### Features

* **ai-agent-dashboard:** remove dados mockados do front ([6394058](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/639405844e0c190ee3dc475e0bf044c7e84f2799))



# [3.55.0-rc.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-mr-3358.5...v3.55.0-rc.3) (2025-07-30)



# [3.55.0-mr-3358.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.55.0-rc.2...v3.55.0-mr-3358.5) (2025-07-30)



# [3.55.0-rc.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3458.0...v3.55.0-rc.2) (2025-07-30)



# [3.55.0-mr-3331.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3431.9...v3.55.0-mr-3331.10) (2025-07-30)



# [3.55.0-rc.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-rc.13...v3.55.0-rc.1) (2025-07-30)


### Bug Fixes

* **version:** versao alterada para 3.55.0-rc.0 ([f8f7a21](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/f8f7a21798b44e4d89acea190c5930de6892f3b2))



# [3.54.0-mr-3427.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3432.0...v3.54.0-mr-3427.4) (2025-07-29)



# [3.54.0-mr-3431.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.1...v3.54.0-mr-3431.8) (2025-07-29)



# [3.54.0-mr-3427.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.1-mr-3452.0...v3.54.0-mr-3427.3) (2025-07-28)



# [3.54.0-mr-3427.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.0...v3.54.0-mr-3427.2) (2025-07-28)



# [3.54.0-mr-3431.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.0-rc.14...v3.54.0-mr-3431.6) (2025-07-28)



# [3.54.0-mr-3431.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.0-rc.13...v3.54.0-mr-3431.5) (2025-07-28)



# [3.54.0-mr-3431.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-rc.11...v3.54.0-mr-3431.4) (2025-07-28)



# [3.54.0-mr-3431.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3444.2...v3.54.0-mr-3431.3) (2025-07-26)



# [3.54.0-mr-3444.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3431.2...v3.54.0-mr-3444.2) (2025-07-26)



# [3.54.0-mr-3431.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.0-rc.12...v3.54.0-mr-3431.2) (2025-07-25)



# [3.54.0-mr-3444.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.0-mr-3448.0...v3.54.0-mr-3444.1) (2025-07-25)


### Features

* corrige nome da variavel no copiloto ([abc2ca2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/abc2ca2f13b1b97a1443585bbe28e27ee090f9a1))



# [3.54.0-mr-3444.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.51.5-mr-3443.0...v3.54.0-mr-3444.0) (2025-07-25)


### Features

* adiciona novas mensagens para falta de credito para transcrição ([62abc0c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/62abc0ca1b72d928f81ba7fa274fbac39e4ca7e5))



## [3.51.5-mr-3443.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-mr-3424.1...v3.51.5-mr-3443.0) (2025-07-25)



# [3.53.0-mr-3427.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.51.5-mr-3439.0...v3.53.0-mr-3427.1) (2025-07-24)



## [3.51.5-mr-3439.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.0-rc.11...v3.51.5-mr-3439.0) (2025-07-24)


### Bug Fixes

* hsm countdown reinicia com mensagens recebidas tunel ([60efa15](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/60efa15ab12ab7600fc30d48e9f18469ce54b139))


### Features

* adiciona bloqueio no copilot para msg unica de audio caso não tenha transcrição de audio habilitada ([e13135a](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/e13135aaa616f7209faa92a8d0d7f54c831405a8))



# [3.53.0-mr-3427.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.51.5-mr-3432.0...v3.53.0-mr-3427.0) (2025-07-23)


### Features

* **ai-agent-dashboard:** adiciona card e gráfico de Agente Inteligente no front do dashboard com dados fixos até adequação do backend ([1767f04](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/1767f04dcbd5b56070db586037a470beca268579))



# [3.52.0-mr-3358.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.52.0-mr-3401.3...v3.52.0-mr-3358.4) (2025-07-16)



# [3.52.0-mr-3358.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.52.0-mr-3379.3...v3.52.0-mr-3358.3) (2025-07-16)


### Bug Fixes

* correct translation for 'ACTIVE_TICKET_TOPICS' in Portuguese locale ([0ac42c7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/0ac42c7d741b9021a853d50302a2ea5b36b565bf))



# [3.52.0-mr-3331.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.52.0-mr-3284.2...v3.52.0-mr-3331.9) (2025-07-16)



# [3.48.0-mr-3358.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.52.0-mr-3385.2...v3.48.0-mr-3358.2) (2025-07-16)


### Bug Fixes

* add validation messages for ticket topic name in Portuguese, Spanish, and English ([08fefd2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/08fefd228417d3f93b1bf9197ff238b23e48062a))
* correct translation for ticket topics in Portuguese ([0512c5c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/0512c5cc3c185c5121125ab2656a5a5ae8ca80f2))
* refactor buildQuery to destructure paginate parameter for clarity ([c55ee35](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c55ee357f8e003b485917d27aeaa956f9903c641))



# [3.48.0-mr-3331.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.52.0-mr-3399.0...v3.48.0-mr-3331.8) (2025-07-14)


### Features

* add validation messages for organization name requirements in localization files ([32acd47](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/32acd47cf1bcce89135e6c90ca7f871018332fef))



# [3.48.0-mr-3358.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.52.0-mr-3369.0...v3.48.0-mr-3358.1) (2025-07-14)



# [3.48.0-mr-3331.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.52.0-mr-3228.8...v3.48.0-mr-3331.7) (2025-07-14)


### Features

* enhance nebula-tokens with new styles for alerts, badges, and typography ([82ed69b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/82ed69bc4e4a839849dc63beff9e1e4b8b74b5c9))
* **nebula-tokens:** enhance styling variables for alerts, badges, buttons, and typography ([6eaadda](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/6eaaddadede09062afc31f0b8b69145d238c6b69))
* update buildQuery functions to include default pagination handling across multiple APIs ([1743788](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/17437884486d096c6173b6db5ae11ad0121ce6ff))



# [3.48.0-mr-3331.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3288.13...v3.48.0-mr-3331.6) (2025-07-02)



# [3.48.0-mr-3358.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3284.17...v3.48.0-mr-3358.0) (2025-07-02)



# [3.48.0-mr-3331.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.15...v3.48.0-mr-3331.5) (2025-07-02)


### Bug Fixes

* **ticket-topics:** update filter form submission logging and correct archive options values ([4401279](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/4401279916b774f18d83bef7c190a643dd7816ae))


### Features

* **ticket-topics:** implement CRUD functionality for ticket topics ([afc51bf](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/afc51bff21041dca975ef09894db086c68bae7d1))



# [3.48.0-mr-3331.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.19...v3.48.0-mr-3331.4) (2025-06-29)





## [3.54.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.1-mr-3466.3...v3.54.2) (2025-08-07)

**Note:** Version bump only for package @digisac/front





# [3.54.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.54.0-rc.16...v3.54.0) (2025-08-04)

**Note:** Version bump only for package @digisac/front





# [3.53.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.53.0-rc.14...v3.53.0) (2025-07-28)

**Note:** Version bump only for package @digisac/front





# [3.52.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.52.0-rc.20...v3.52.0) (2025-07-28)

**Note:** Version bump only for package @digisac/front





## [3.51.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.51.4-mr-3105.30...v3.51.5) (2025-07-28)

**Note:** Version bump only for package @digisac/front





## [3.51.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.51.1-mr-3406.0...v3.51.1) (2025-07-16)

**Note:** Version bump only for package @digisac/front





# [3.51.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.51.0-rc.10...v3.51.0) (2025-07-14)

**Note:** Version bump only for package @digisac/front





## [3.50.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.2-mr-3360.3...v3.50.3) (2025-07-14)

**Note:** Version bump only for package @digisac/front





## [3.50.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.2-mr-3381.1...v3.50.2) (2025-07-08)

**Note:** Version bump only for package @digisac/front





## [3.50.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.20...v3.50.1) (2025-07-07)

**Note:** Version bump only for package @digisac/front





# [3.50.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.2...v3.50.0) (2025-07-07)



# [3.50.0-rc.18](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.1...v3.50.0-rc.18) (2025-07-07)



# [3.50.0-mr-3376.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.1-mr-3368.0...v3.50.0-mr-3376.0) (2025-07-07)


### Bug Fixes

* **departments:** refactor buildQuery to destructure paginate parameter ([e559cd6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/e559cd66d69211b78258bfb376979ed7fc0ddb6c))



# [3.50.0-rc.17](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3364.1...v3.50.0-rc.17) (2025-07-03)



# [3.50.0-mr-3364.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.15...v3.50.0-mr-3364.0) (2025-07-02)


### Bug Fixes

* SD-1562 (defect)  new contacts not showing up in the chat ([6e331fe](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/6e331feab633e0a46ad00ad6883d98a6314a2312))



# [3.50.0-rc.15](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3356.2...v3.50.0-rc.15) (2025-07-02)



# [3.50.0-mr-3356.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3356.1...v3.50.0-mr-3356.2) (2025-07-01)



# [3.50.0-mr-3356.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.14...v3.50.0-mr-3356.1) (2025-07-01)



# [3.50.0-rc.14](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.20...v3.50.0-rc.14) (2025-06-30)



# [3.49.0-rc.20](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0...v3.49.0-rc.20) (2025-06-30)



# [3.50.0-rc.13](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3311.1...v3.50.0-rc.13) (2025-06-30)



# [3.50.0-mr-3311.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.12...v3.50.0-mr-3311.1) (2025-06-30)



# [3.50.0-mr-3356.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3355.1...v3.50.0-mr-3356.0) (2025-06-30)



# [3.50.0-mr-3355.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3356.0...v3.50.0-mr-3355.1) (2025-06-30)



# [3.49.0-mr-3356.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3355.0...v3.49.0-mr-3356.0) (2025-06-30)


### Features

* ajuster experience copiloto ([119c9a3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/119c9a3137ca2737f697bdae1d5b31e5c3ede6bf))



# [3.50.0-rc.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3311.0...v3.50.0-rc.11) (2025-06-30)



# [3.50.0-mr-3311.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3311.7...v3.50.0-mr-3311.0) (2025-06-30)



# [3.49.0-mr-3311.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3337.4...v3.49.0-mr-3311.7) (2025-06-30)



# [3.50.0-mr-3343.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.10...v3.50.0-mr-3343.9) (2025-06-30)



# [3.50.0-rc.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3343.8...v3.50.0-rc.10) (2025-06-30)



# [3.50.0-mr-3343.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3334.2...v3.50.0-mr-3343.8) (2025-06-30)



# [3.50.0-mr-3334.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.19...v3.50.0-mr-3334.2) (2025-06-30)



# [3.50.0-rc.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3259.8...v3.50.0-rc.9) (2025-06-27)



# [3.50.0-mr-3259.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.2-mr-3259.7...v3.50.0-mr-3259.8) (2025-06-27)



## [3.48.2-mr-3259.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.8...v3.48.2-mr-3259.7) (2025-06-27)



# [3.50.0-rc.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.47.1-mr-3323.0...v3.50.0-rc.8) (2025-06-27)



## [3.47.1-mr-3323.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3343.7...v3.47.1-mr-3323.0) (2025-06-27)



# [3.50.0-mr-3343.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3334.1...v3.50.0-mr-3343.7) (2025-06-27)



# [3.50.0-mr-3334.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.7...v3.50.0-mr-3334.1) (2025-06-27)



# [3.50.0-rc.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3318.3...v3.50.0-rc.7) (2025-06-27)



# [3.50.0-mr-3318.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.3-mr-3349.0...v3.50.0-mr-3318.3) (2025-06-27)



## [3.48.2-mr-3259.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.1-mr-3259.5...v3.48.2-mr-3259.6) (2025-06-27)



## [3.45.1-mr-3259.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3348.1...v3.45.1-mr-3259.5) (2025-06-26)


### Bug Fixes

* **myplan-intl:** initial load on macos ([e9fed8e](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/e9fed8e83e9e68fb714b2a744de56d95a6d023c9))



# [3.50.0-rc.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.17...v3.50.0-rc.6) (2025-06-26)



# [3.50.0-mr-3343.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3343.5...v3.50.0-mr-3343.6) (2025-06-26)



# [3.50.0-mr-3343.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3343.3...v3.50.0-mr-3343.4) (2025-06-26)


### Bug Fixes

* changed to not mutate the original filter dates ([9092aca](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/9092acab1f294687ec1b20eb1d3ecb285d6136e5))



# [3.50.0-mr-3343.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3343.1...v3.50.0-mr-3343.2) (2025-06-26)


### Bug Fixes

* alter fix startPeriod hour to 00:00:00 on page filter ([ea6b5b6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/ea6b5b6f715a7fe8f8711bb694676ed75a52f87c))
* pontos de teste ([3b0aca7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/3b0aca7ac7c038b854aa3ac6f133fac0a4c6a9f7))



# [3.50.0-rc.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3337.3...v3.50.0-rc.5) (2025-06-26)



# [3.50.0-mr-3343.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3337.1...v3.50.0-mr-3343.0) (2025-06-26)


### Bug Fixes

* fixed service filter on evaluation metrics page ([a8f0347](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a8f0347463a30359336a338f624983f53f284ced))



# [3.50.0-rc.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.15...v3.50.0-rc.4) (2025-06-26)



# [3.50.0-mr-3341.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3311.5...v3.50.0-mr-3341.0) (2025-06-25)



# [3.49.0-mr-3311.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3311.4...v3.49.0-mr-3311.5) (2025-06-25)


### Bug Fixes

* cleaning the gain filters. ([a5985b5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a5985b5013b551cc050a2a1a2ca7d5b20ddf8eaa))



# [3.49.0-mr-3311.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3318.2...v3.49.0-mr-3311.4) (2025-06-25)


### Bug Fixes

* cleaning the gain filters. ([c05aa56](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c05aa56d49d1b119b9f4c4c326027f75ee52b64c))



# [3.50.0-mr-3318.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3318.1...v3.50.0-mr-3318.2) (2025-06-25)


### Features

* removendo label new ([50fa989](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/50fa9891ebf078ba70f69315b9e0462bf784bab1))



# [3.49.0-mr-3318.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-mr-3334.0...v3.49.0-mr-3318.1) (2025-06-25)



# [3.50.0-mr-3334.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.3...v3.50.0-mr-3334.0) (2025-06-25)



# [3.50.0-rc.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.50.0-rc.2...v3.50.0-rc.3) (2025-06-25)



# [3.50.0-rc.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.12...v3.50.0-rc.1) (2025-06-25)


### Bug Fixes

* **version:** versao alterada para 3.50.0-rc.0 ([66b9617](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/66b9617e44e3b2c4a46451fc738eddf149be03b4))



# [3.49.0-mr-3334.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.9...v3.49.0-mr-3334.0) (2025-06-24)



# [3.49.0-mr-3311.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3311.2...v3.49.0-mr-3311.3) (2025-06-24)



# [3.49.0-mr-3311.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.8...v3.49.0-mr-3311.2) (2025-06-24)



# [3.49.0-mr-3311.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3327.1...v3.49.0-mr-3311.1) (2025-06-24)



# [3.49.0-mr-3327.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3311.0...v3.49.0-mr-3327.1) (2025-06-24)



# [3.49.0-mr-3311.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.7...v3.49.0-mr-3311.0) (2025-06-24)



# [3.49.0-mr-3318.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.6...v3.49.0-mr-3318.0) (2025-06-23)



# [3.49.0-mr-3327.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3327.0...v3.49.0-mr-3327.0) (2025-06-23)


### Features

* implementando filtro por data de ganho ([bd39415](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/bd39415cfe91a1c093c1b7abbb0abfc0f8ea8bf6))



# [3.48.0-mr-3327.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-rc.17...v3.48.0-mr-3327.0) (2025-06-23)



# [3.49.0-mr-3300.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3300.6...v3.49.0-mr-3300.7) (2025-06-20)


### Features

* **SDIG-657-improved-interpolation-of-variables:** corrige label com  informações textuais e ajusta função que retorna o nome do contato ([2cef054](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/2cef0549631e3d49589a215665e3be59bfc3a978))



# [3.49.0-mr-3300.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3326.0...v3.49.0-mr-3300.6) (2025-06-20)



# [3.49.0-mr-3326.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3325.0...v3.49.0-mr-3326.0) (2025-06-20)


### Bug Fixes

* SD-1526 - notifications would not work for new contacts ([1cd4465](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/1cd44652fe43b2c1217e93aa458d5dc26de98adc))


### Features

* Finalizing the demand. ([c4747c5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c4747c561ba191d912116213be7c3355cc15352f))
* Remove debug ([d4750b6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d4750b620c69c26637c0f372ba834d76c768bc89))
* Remove debug ([411a405](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/411a40506b0c631ff8795ad87548aef475925ed0))



# [3.48.0-mr-3311.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3317.0...v3.48.0-mr-3311.0) (2025-06-19)


### Features

* Completing the filter in saga. ([9c58892](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/9c588921ea4abe4305b50f559c21f5d50efaa105))
* correcting the screen labels. ([b590ea1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/b590ea10ccdf5263e57d229de7a531f1b476401f))



# [3.47.0-mr-3300.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-rc.12...v3.47.0-mr-3300.5) (2025-06-17)


### Features

* **SDIG-657-improved-interpolation-of-variables:** adiciona novas variáveis de interpolação no bot e ajusta limite de itens no autocomplete ([b7a2b0a](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/b7a2b0a06b2283a7bc8db393289e3e90d907ef40))
* Validating whether the status is gained. ([4b5a981](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/4b5a9813a498a8225da19e65209dd28e5616aa70))



## [3.45.1-mr-3259.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.1-mr-3259.3...v3.45.1-mr-3259.4) (2025-06-17)



## [3.45.1-mr-3259.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-rc.11...v3.45.1-mr-3259.3) (2025-06-17)


### Bug Fixes

* **myplan-intl:** ensure initial language loads with useEffect ([d38a92f](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d38a92f0e6b606d821a7223254d36e1f39d20d94))



## [3.45.1-mr-3259.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.46.1-mr-3304.0...v3.45.1-mr-3259.2) (2025-06-16)


### Bug Fixes

* **myplan-intl:** initial language load ([b99e652](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/b99e652fe03e6ab52eec8d8feb5f3ea4add3e649))
* **myplan:** post message dev environment ([d851682](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d8516822cc4ebb35a46629a36d265313ae0ed597))



## [3.45.1-mr-3259.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.1-mr-3259.0...v3.45.1-mr-3259.1) (2025-06-16)



## [3.45.1-mr-3259.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3308.4...v3.45.1-mr-3259.0) (2025-06-16)


### Features

* add new filter ([8f4aa7d](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/8f4aa7d34d103ac7edb479e191ee9bc4e31617c4))
* **MyPlan:** send i18n changes to iframe ([8a4227c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/8a4227c99729417288a7fa1b4a67d47a0c78182b))





# [3.49.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.3...v3.49.0) (2025-06-30)



# [3.49.0-mr-3337.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.19...v3.49.0-mr-3337.4) (2025-06-30)



# [3.49.0-rc.19](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3352.0...v3.49.0-rc.19) (2025-06-27)



# [3.49.0-mr-3352.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.3-mr-3349.0...v3.49.0-mr-3352.0) (2025-06-27)


### Bug Fixes

* remove redundant label margin style and enforce margin reset ([2cc19d4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/2cc19d45c7e13295f706e83db0ec1794831a73c9))



# [3.49.0-rc.18](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3348.1...v3.49.0-rc.18) (2025-06-27)



# [3.49.0-mr-3348.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3348.0...v3.49.0-mr-3348.1) (2025-06-26)


### Bug Fixes

* adjust token name label in the view modal ([5e362ab](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/5e362ab8b5bdc132a3f71adee176fc4292fd99d6))



# [3.49.0-mr-3348.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.17...v3.49.0-mr-3348.0) (2025-06-26)


### Bug Fixes

* adjust label for webhook and profile name ([c57964d](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c57964d45f7dcf24dc02c625c3509d7d9c6d4d25))



# [3.49.0-rc.17](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.2...v3.49.0-rc.17) (2025-06-26)



# [3.49.0-rc.16](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3337.3...v3.49.0-rc.16) (2025-06-26)



# [3.49.0-mr-3337.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3337.2...v3.49.0-mr-3337.3) (2025-06-26)



# [3.49.0-mr-3337.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3344.0...v3.49.0-mr-3337.2) (2025-06-26)



# [3.49.0-mr-3344.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3337.1...v3.49.0-mr-3344.0) (2025-06-26)



# [3.49.0-mr-3337.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.15...v3.49.0-mr-3337.1) (2025-06-26)



# [3.49.0-rc.15](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3317.6...v3.49.0-rc.15) (2025-06-26)



# [3.49.0-mr-3317.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.14...v3.49.0-mr-3317.6) (2025-06-26)



# [3.49.0-rc.14](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.13...v3.49.0-rc.14) (2025-06-25)



# [3.49.0-rc.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3304.0...v3.49.0-rc.12) (2025-06-25)



# [3.49.0-mr-3304.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.1-mr-3338.0...v3.49.0-mr-3304.0) (2025-06-25)



# [3.49.0-rc.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0...v3.49.0-rc.11) (2025-06-24)



# [3.49.0-mr-3134.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3134.2...v3.49.0-mr-3134.3) (2025-06-24)



# [3.49.0-mr-3134.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3337.0...v3.49.0-mr-3134.2) (2025-06-24)



# [3.49.0-mr-3337.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.10...v3.49.0-mr-3337.0) (2025-06-24)



# [3.49.0-rc.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.9...v3.49.0-rc.10) (2025-06-24)



# [3.49.0-rc.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.7...v3.49.0-rc.8) (2025-06-24)



# [3.49.0-mr-3330.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3317.5...v3.49.0-mr-3330.0) (2025-06-23)


### Bug Fixes

* add correct value to no expires token create ([0aca3bb](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/0aca3bba27a1cde6f8ae448f28c5907bc6f3593e))



# [3.49.0-mr-3317.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3329.0...v3.49.0-mr-3317.5) (2025-06-23)


### Bug Fixes

* **TicketInactiveJob:** corrige casos em que a última mensagem do chamado é um comentário ([58afd70](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/58afd70fb935caf6e79be35de5bb02f5914ca6ca))



# [3.49.0-mr-3329.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.6...v3.49.0-mr-3329.0) (2025-06-23)


### Features

* removendo label de new da funcionalidade smart summary triggers ([db48b04](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/db48b04ea0a8b78392c3d4496002ecc331c80a0a))



# [3.49.0-rc.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3325.1...v3.49.0-rc.6) (2025-06-23)



# [3.49.0-mr-3325.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.5...v3.49.0-mr-3325.1) (2025-06-23)



# [3.49.0-rc.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3310.1...v3.49.0-rc.5) (2025-06-23)



# [3.49.0-mr-3310.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-rc.4...v3.49.0-mr-3310.1) (2025-06-23)



# [3.49.0-rc.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-rc.17...v3.49.0-rc.4) (2025-06-23)



# [3.49.0-mr-3317.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3317.1...v3.49.0-mr-3317.2) (2025-06-20)



# [3.49.0-mr-3317.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3325.0...v3.49.0-mr-3317.1) (2025-06-20)



# [3.49.0-rc.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3297.0...v3.49.0-rc.3) (2025-06-20)



# [3.49.0-mr-3297.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3310.0...v3.49.0-mr-3297.0) (2025-06-20)



# [3.49.0-mr-3310.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3297.3...v3.49.0-mr-3310.0) (2025-06-20)



# [3.48.0-mr-3310.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3324.0...v3.48.0-mr-3310.4) (2025-06-20)


### Bug Fixes

* pontos do teste ([5fbb25c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/5fbb25cb71e9dd4979db6dd089e443f35dfb0a85))
* pontos do teste ([362033e](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/362033e4468111890653097da3e5099ca5960db6))



# [3.49.0-rc.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.49.0-mr-3229.7...v3.49.0-rc.2) (2025-06-18)



# [3.49.0-mr-3229.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3229.7...v3.49.0-mr-3229.7) (2025-06-18)



# [3.49.0-rc.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-rc.16...v3.49.0-rc.1) (2025-06-18)


### Bug Fixes

* **version:** versao alterada para 3.49.0-rc.0 ([20f9df2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/20f9df2692dfd8575b1d686b22b074e43282ab18))



# [3.48.0-mr-3134.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-rc.9...v3.48.0-mr-3134.1) (2025-06-16)



# [3.48.0-mr-3229.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-rc.8...v3.48.0-mr-3229.6) (2025-06-16)



# [3.48.0-mr-3310.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3024.11...v3.48.0-mr-3310.3) (2025-06-16)



# [3.48.0-mr-3310.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.47.0-rc.13...v3.48.0-mr-3310.2) (2025-06-13)



# [3.48.0-mr-3310.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3310.0...v3.48.0-mr-3310.1) (2025-06-12)



# [3.48.0-mr-3310.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-mr-3236.1...v3.48.0-mr-3310.0) (2025-06-12)


### Features

* redesign de mensagens de audio no copilot ([c40f7b6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c40f7b6132525a819c41b6c9163ddb542ccc104d))



# [3.48.0-mr-3229.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.47.0...v3.48.0-mr-3229.5) (2025-06-11)



# [3.44.0-mr-3134.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.2-mr-3251.0...v3.44.0-mr-3134.0) (2025-05-30)



# [3.44.0-mr-3024.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.46.0-mr-3234.1...v3.44.0-mr-3024.5) (2025-05-30)


### Bug Fixes

* update localization strings for tag linking in multiple languages ([9956ba5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/9956ba5ef59b9756b784afb7157c481303a2ae66))



# [3.43.0-mr-3134.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.44.0-mr-3024.4...v3.43.0-mr-3134.8) (2025-05-28)


### Bug Fixes

* enable save button only when form is valid ([ee8fc27](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/ee8fc27d2986b17d52a59175b09eec868ac23998))



# [3.44.0-mr-3024.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.0-mr-3225.3...v3.44.0-mr-3024.4) (2025-05-28)


### Bug Fixes

* disable submit button if form is invalid or not dirty in TagsForm and LinkTagsToDepartmentDialog ([ad6652d](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/ad6652df0eff32dbf96c903c3ecca973493a32aa))
* refactor filter handling and debounce logic ([116325f](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/116325f560afb0f1ad4a86f73f873064ea69b9dd))
* refactor InputFilter to use local state and improve handlers ([a1e5336](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a1e53369f3f30be2fba1edb69f800f19380edfcc))
* update InputText width in InputFilter component ([c46fb81](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c46fb81d3c0363d334b542071c28bdb030f9a331))



# [3.44.0-mr-3024.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.44.0-mr-3024.2...v3.44.0-mr-3024.3) (2025-05-28)



# [3.44.0-mr-3024.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.0-rc.10...v3.44.0-mr-3024.2) (2025-05-28)


### Bug Fixes

* update nebula-react and nebula-tokens to version 0.0.1-alpha.24 ([ef87c43](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/ef87c4362d115e0328ee67a57b3cd78cbb88dafc))



# [3.44.0-mr-3024.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.44.0-mr-3024.0...v3.44.0-mr-3024.1) (2025-05-27)


### Bug Fixes

* update nebula-react and nebula-tokens to version 0.0.1-alpha.21 ([d45dfbe](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d45dfbe12cc8bbe56ed5c173e22945f02494e62a))



# [3.44.0-mr-3024.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.0-mr-3234.0...v3.44.0-mr-3024.0) (2025-05-27)


### Bug Fixes

* update tag creation messages and clean up dialog components ([be4d293](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/be4d2939ab750d31870c5f6839a1d10772ef2c26))



# [3.43.0-mr-3227.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.0-rc.5...v3.43.0-mr-3227.0) (2025-05-26)


### Features

* add new event for personal access token notification with i18n ([f2425dd](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/f2425ddcd52b330c5eb308f432643b4dd87968bc))



# [3.43.0-mr-3201.26](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.2-mr-3222.0...v3.43.0-mr-3201.26) (2025-05-22)



# [3.43.0-mr-3201.25](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-mr-3201.24...v3.43.0-mr-3201.25) (2025-05-22)


### Bug Fixes

* **personal-token:** add paginate false on query ([3489c28](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/3489c2873bc652c081c2867225e5df7000689ba9))



# [3.43.0-mr-3201.24](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-mr-3201.23...v3.43.0-mr-3201.24) (2025-05-22)



# [3.43.0-mr-3201.23](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-mr-3201.22...v3.43.0-mr-3201.23) (2025-05-22)


### Bug Fixes

* fixed paginate parameters ([b51f71b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/b51f71b1492f571fe1e45342ff1118ecbb5d6483))



# [3.43.0-mr-3201.22](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-mr-3201.21...v3.43.0-mr-3201.22) (2025-05-22)



# [3.43.0-mr-3201.21](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-mr-3201.20...v3.43.0-mr-3201.21) (2025-05-22)



# [3.43.0-mr-3201.20](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.44.0-mr-3220.0...v3.43.0-mr-3201.20) (2025-05-22)



# [3.43.0-mr-3213.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0...v3.43.0-mr-3213.6) (2025-05-21)



# [3.43.0-mr-3213.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.0-rc.1...v3.43.0-mr-3213.5) (2025-05-21)



# [3.43.0-mr-3213.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.2-mr-3196.2...v3.43.0-mr-3213.4) (2025-05-21)



# [3.43.0-mr-3213.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.1-mr-3158.7...v3.43.0-mr-3213.3) (2025-05-20)



# [3.43.0-mr-3213.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.44.0-mr-3212.5...v3.43.0-mr-3213.2) (2025-05-20)



# [3.43.0-mr-3134.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-mr-3129.5...v3.43.0-mr-3134.7) (2025-05-15)



# [3.43.0-mr-3134.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-mr-3134.5...v3.43.0-mr-3134.6) (2025-05-14)



# [3.43.0-mr-3134.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-rc.12...v3.43.0-mr-3134.5) (2025-05-14)



## [3.42.1-mr-3134.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-rc.11...v3.42.1-mr-3134.4) (2025-05-14)


### Features

* **departments:** update success messages and improve filter display ([b37f810](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/b37f81034ec9409a96b2e5724287b57de521d086))



## [3.42.1-mr-3134.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-rc.10...v3.42.1-mr-3134.3) (2025-05-13)


### Features

* **departments:** remove old files ([a1ebbf1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a1ebbf15422e02019e076a3c2f308d7113bdad49))





## [3.48.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.1-mr-3305.3...v3.48.2) (2025-06-26)

**Note:** Version bump only for package @digisac/front





## [3.48.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.1-mr-3338.0...v3.48.1) (2025-06-25)

**Note:** Version bump only for package @digisac/front





# [3.48.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.48.0-rc.17...v3.48.0) (2025-06-24)

**Note:** Version bump only for package @digisac/front





## [3.47.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.47.0-rc.14...v3.47.3) (2025-06-18)

**Note:** Version bump only for package @digisac/front





## [3.47.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.47.2-mr-3313.0...v3.47.2) (2025-06-18)

**Note:** Version bump only for package @digisac/front





## [3.47.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.47.1-mr-3319.0...v3.47.1) (2025-06-18)

**Note:** Version bump only for package @digisac/front





# [3.47.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.46.0...v3.47.0) (2025-06-11)



## [3.45.1-mr-3254.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.46.0-rc.12...v3.45.1-mr-3254.0) (2025-06-03)

**Note:** Version bump only for package @digisac/front





# [3.46.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.46.0-rc.19...v3.46.0) (2025-06-10)

**Note:** Version bump only for package @digisac/front





## [3.45.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.1-mr-3298.2...v3.45.2) (2025-06-09)

**Note:** Version bump only for package @digisac/front





## [3.45.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.0-mr-3281.4...v3.45.1) (2025-06-09)

**Note:** Version bump only for package @digisac/front





# [3.45.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.45.0-rc.12...v3.45.0) (2025-06-02)

**Note:** Version bump only for package @digisac/front





# [3.44.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.44.0-rc.12...v3.44.0) (2025-06-02)

**Note:** Version bump only for package @digisac/front





## [3.43.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.3-mr-3251.1...v3.43.4) (2025-06-02)

**Note:** Version bump only for package @digisac/front





## [3.43.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.2...v3.43.3) (2025-05-30)

**Note:** Version bump only for package @digisac/front





## [3.43.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.1-mr-3166.5...v3.43.2) (2025-05-27)

**Note:** Version bump only for package @digisac/front





# [3.43.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.43.0-rc.14...v3.43.0) (2025-05-21)

**Note:** Version bump only for package @digisac/front





## [3.42.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.1-mr-3158.7...v3.42.2) (2025-05-21)

**Note:** Version bump only for package @digisac/front





# [3.42.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.2...v3.42.0) (2025-05-12)



# [3.42.0-rc.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-mr-3175.0...v3.42.0-rc.12) (2025-05-08)



# [3.42.0-mr-3175.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-rc.10...v3.42.0-mr-3175.0) (2025-05-08)


### Bug Fixes

* consertando problema de dois cliques na notificacao para fechar ([bf42da0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/bf42da0f254358245bd3371ae1eebb016afdc4b8))



# [3.42.0-rc.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-mr-3163.2...v3.42.0-rc.10) (2025-05-06)



# [3.42.0-mr-3163.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-rc.9...v3.42.0-mr-3163.2) (2025-05-06)



# [3.42.0-rc.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-mr-3124.0...v3.42.0-rc.9) (2025-05-06)



# [3.42.0-mr-3124.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-rc.8...v3.42.0-mr-3124.0) (2025-05-06)



# [3.42.0-rc.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-rc.7...v3.42.0-rc.8) (2025-05-06)



# [3.42.0-rc.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-mr-3164.5...v3.42.0-rc.7) (2025-05-06)



# [3.42.0-mr-3164.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-rc.6...v3.42.0-mr-3164.4) (2025-05-06)



# [3.42.0-mr-3164.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-mr-3164.2...v3.42.0-mr-3164.3) (2025-05-06)



# [3.42.0-mr-3164.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-mr-3164.1...v3.42.0-mr-3164.2) (2025-05-06)



# [3.42.0-mr-3164.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-mr-3164.0...v3.42.0-mr-3164.1) (2025-05-06)



# [3.41.0-mr-3124.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-mr-3163.1...v3.41.0-mr-3124.4) (2025-05-06)



# [3.42.0-mr-3163.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3160.2...v3.42.0-mr-3163.1) (2025-05-06)



# [3.42.0-rc.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3160.1...v3.42.0-rc.5) (2025-05-06)



# [3.41.0-mr-3124.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3153.0...v3.41.0-mr-3124.3) (2025-05-06)


### Features

* mantendo botão fixo caso smartSummary ativo ([804811c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/804811cc697c356a66faf30a3064b143b9a44003))



# [3.41.0-mr-3153.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-rc.23...v3.41.0-mr-3153.0) (2025-05-06)



# [3.41.0-rc.23](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3122.1...v3.41.0-rc.23) (2025-05-06)



# [3.41.0-mr-3122.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3160.0...v3.41.0-mr-3122.1) (2025-05-06)



# [3.42.0-mr-3163.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-rc.4...v3.42.0-mr-3163.0) (2025-05-06)


### Bug Fixes

* adiciona o menu de integrações ao menu vertical, e adciona outros itens ao menu mais opções ([c8cb6d0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c8cb6d0ed878c7e381b0a061af2efb5f42e35971))



# [3.42.0-rc.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0...v3.42.0-rc.4) (2025-05-05)



# [3.41.0-mr-3128.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-2919.6...v3.41.0-mr-3128.3) (2025-05-05)



# [3.41.0-mr-2919.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-2919.5...v3.41.0-mr-2919.6) (2025-05-05)



# [3.41.0-mr-2919.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-2919.4...v3.41.0-mr-2919.5) (2025-05-05)



# [3.41.0-mr-2919.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-rc.2...v3.41.0-mr-2919.4) (2025-05-05)



# [3.42.0-rc.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3124.2...v3.42.0-rc.2) (2025-05-05)



# [3.41.0-mr-3124.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-rc.20...v3.41.0-mr-3124.2) (2025-05-05)


### Bug Fixes

* adicionando contador de itens novos ao mais opções ([b5b1756](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/b5b175670fcda4cb3782a6c40b2edd9d279f1f72))
* typo ([f65a4df](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/f65a4df31f7ae2d7a70804e5463e6e6c22665aad))



# [3.41.0-rc.20](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-2919.3...v3.41.0-rc.20) (2025-05-05)



# [3.41.0-mr-2919.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3137.1...v3.41.0-mr-2919.3) (2025-05-05)



# [3.41.0-mr-3137.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-2919.2...v3.41.0-mr-3137.1) (2025-05-02)



# [3.41.0-mr-2919.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.5-mr-3085.2...v3.41.0-mr-2919.1) (2025-05-02)



# [3.41.0-mr-3128.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.40.2-mr-3153.0...v3.41.0-mr-3128.2) (2025-05-02)



## [3.40.2-mr-3153.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.1-rc.0...v3.40.2-mr-3153.0) (2025-04-30)


### Bug Fixes

* SD-1470 and SD-1475 - restore focus to chat textbox when replying to a message or sending quick reply ([9727bb6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/9727bb6aebae0fa7245ee918b42212f5980fadfa))



## [3.42.1-rc.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.1-mr-3125.1...v3.42.1-rc.0) (2025-04-30)



## [3.42.1-mr-3125.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.42.0-rc.1...v3.42.1-mr-3125.1) (2025-04-30)



# [3.42.0-rc.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-rc.19...v3.42.0-rc.1) (2025-04-30)


### Bug Fixes

* pontos mr ([17ebb30](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/17ebb301118c394a19043c4e3ae203c0d3a07424))
* pontos mr ([eac9702](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/eac9702b163a3fd1ad6284fcf7afdbb9f60d8812))



# [3.41.0-mr-3090.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-rc.17...v3.41.0-mr-3090.3) (2025-04-29)



# [3.41.0-mr-3090.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.39.1-mr-3104.2...v3.41.0-mr-3090.2) (2025-04-29)



# [3.41.0-mr-3137.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-rc.15...v3.41.0-mr-3137.0) (2025-04-29)



## [3.40.1-mr-3125.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3128.1...v3.40.1-mr-3125.0) (2025-04-28)



# [3.41.0-mr-3128.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.36.1-mr-3137.0...v3.41.0-mr-3128.1) (2025-04-28)



## [3.36.1-mr-3137.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.40.0...v3.36.1-mr-3137.0) (2025-04-28)



## [3.39.2-mr-3125.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-rc.11...v3.39.2-mr-3125.0) (2025-04-28)



# [3.41.0-mr-3122.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-rc.9...v3.41.0-mr-3122.0) (2025-04-25)



# [3.41.0-mr-3090.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3130.0...v3.41.0-mr-3090.0) (2025-04-25)



# [3.41.0-mr-3124.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.36.1-mr-3109.2...v3.41.0-mr-3124.0) (2025-04-25)



# [3.40.0-mr-3124.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3118.3...v3.40.0-mr-3124.1) (2025-04-25)


### Bug Fixes

* add round style in select ([d6ae191](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d6ae191926212c15a03d9bd026d7295d4f9f06ab))


### Features

* add new translates ([75e7a74](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/75e7a7465c45a3743ee2e2f861997203b6cab81d))
* finalizando gatilhos ([de2659f](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/de2659f314c236b55d50f0055c80664833fe0b02))
* finalizando gatilhos ([b86884e](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/b86884e40b2b88a91e6c8b3f28f0a78111415265))



# [3.40.0-mr-3124.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3123.0...v3.40.0-mr-3124.0) (2025-04-24)


### Features

* criacao da notificacao e inicio do chat ([1f50173](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/1f501738763570022c19bf4fc2e9304d78e16f82))
* finalizando notification ([be15c18](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/be15c180615d0cd47447ef07d559b5d976ab8626))



## [3.36.1-mr-3090.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.39.0-mr-3100.1...v3.36.1-mr-3090.4) (2025-04-16)


### Features

* criando branch ([25bff23](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/25bff2376496be767efacae4e8f992a5a7788d7d))





## [3.41.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.1-mr-2602.11...v3.41.2) (2025-05-08)

**Note:** Version bump only for package @digisac/front





## [3.41.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-mr-3160.2...v3.41.1) (2025-05-06)

**Note:** Version bump only for package @digisac/front





# [3.41.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.41.0-rc.19...v3.41.0) (2025-05-05)

**Note:** Version bump only for package @digisac/front





## [3.40.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.40.1-mr-3147.1...v3.40.1) (2025-04-30)

**Note:** Version bump only for package @digisac/front





# [3.40.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.40.0-rc.9...v3.40.0) (2025-04-28)

**Note:** Version bump only for package @digisac/front





## [3.39.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.39.1-mr-3093.1...v3.39.2) (2025-04-28)

**Note:** Version bump only for package @digisac/front





## [3.39.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.39.0-rc.19...v3.39.1) (2025-04-22)

**Note:** Version bump only for package @digisac/front





# [3.38.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.38.0-rc.5...v3.38.0) (2025-04-14)

**Note:** Version bump only for package @digisac/front





## [3.37.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.37.2-mr-3066.1...v3.37.3) (2025-04-10)

**Note:** Version bump only for package @digisac/front





## [3.37.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.36.6...v3.37.2) (2025-04-09)

**Note:** Version bump only for package @digisac/front





## [3.36.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.36.3-mr-3048.6...v3.36.6) (2025-04-08)

**Note:** Version bump only for package @digisac/front





## [3.37.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.37.0-rc.14...v3.37.1) (2025-04-07)

**Note:** Version bump only for package @digisac/front





## [3.36.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.36.3-mr-3041.2...v3.36.4) (2025-04-03)

**Note:** Version bump only for package @digisac/front





## [3.36.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.36.3-mr-3047.1...v3.36.3) (2025-04-02)

**Note:** Version bump only for package @digisac/front





## [3.36.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.36.1-mr-3016.3...v3.36.2) (2025-03-27)

**Note:** Version bump only for package @digisac/front





## [3.36.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.36.0-mr-3038.1...v3.36.1) (2025-03-26)

**Note:** Version bump only for package @digisac/front





# [3.36.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.36.0-rc.8...v3.36.0) (2025-03-25)

**Note:** Version bump only for package @digisac/front





## [3.35.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.35.1-mr-3010.1...v3.35.1) (2025-03-21)

**Note:** Version bump only for package @digisac/front





# [3.35.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.35.0-rc.12...v3.35.0) (2025-03-17)

**Note:** Version bump only for package @digisac/front





## [3.34.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.34.3-mr-2986.1...v3.34.3) (2025-03-12)

**Note:** Version bump only for package @digisac/front





## [3.34.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.34.2-mr-2969.1...v3.34.2) (2025-03-07)

**Note:** Version bump only for package @digisac/front





## [3.34.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.34.1-mr-2968.0...v3.34.1) (2025-03-06)

**Note:** Version bump only for package @digisac/front





# [3.34.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.34.0-rc.9...v3.34.0) (2025-03-05)

**Note:** Version bump only for package @digisac/front





# [3.33.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.3...v3.33.0) (2025-02-24)



# [3.33.0-rc.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.1...v3.33.0-rc.9) (2025-02-21)



# [3.33.0-rc.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2930.0...v3.33.0-rc.8) (2025-02-20)



# [3.33.0-mr-2930.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-rc.7...v3.33.0-mr-2930.0) (2025-02-20)



# [3.33.0-rc.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2920.0...v3.33.0-rc.7) (2025-02-20)



# [3.33.0-mr-2920.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-rc.6...v3.33.0-mr-2920.0) (2025-02-20)



# [3.33.0-rc.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2719.3...v3.33.0-rc.6) (2025-02-20)



# [3.33.0-mr-2719.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2719.1...v3.33.0-mr-2719.2) (2025-02-19)



# [3.33.0-mr-2719.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-rc.5...v3.33.0-mr-2719.1) (2025-02-19)



# [3.33.0-rc.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2719.0...v3.33.0-rc.5) (2025-02-19)



# [3.33.0-mr-2719.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2746.13...v3.33.0-mr-2719.0) (2025-02-19)



# [3.33.0-mr-2746.13](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2841.2...v3.33.0-mr-2746.13) (2025-02-19)



# [3.33.0-mr-2841.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-rc.4...v3.33.0-mr-2841.2) (2025-02-19)



# [3.33.0-rc.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.1-mr-2927.0...v3.33.0-rc.4) (2025-02-19)



# [3.33.0-mr-2746.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2746.11...v3.33.0-mr-2746.12) (2025-02-18)



# [3.33.0-mr-2746.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2719.4...v3.33.0-mr-2746.11) (2025-02-18)



# [3.33.0-mr-2719.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-mr-2920.1...v3.33.0-mr-2719.4) (2025-02-18)



# [3.32.0-mr-2920.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0...v3.32.0-mr-2920.1) (2025-02-18)



# [3.33.0-mr-2841.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-mr-2923.2...v3.33.0-mr-2841.1) (2025-02-18)



# [3.32.0-mr-2920.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-rc.3...v3.32.0-mr-2920.0) (2025-02-17)



# [3.33.0-rc.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2912.0...v3.33.0-rc.3) (2025-02-17)



# [3.33.0-mr-2912.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-rc.2...v3.33.0-mr-2912.0) (2025-02-17)



# [3.33.0-rc.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.33.0-mr-2900.0...v3.33.0-rc.2) (2025-02-17)



# [3.33.0-mr-2900.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-mr-2915.1...v3.33.0-mr-2900.0) (2025-02-17)



# [3.33.0-rc.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-mr-2915.0...v3.33.0-rc.1) (2025-02-14)



# [3.32.0-mr-2912.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-mr-2912.0...v3.32.0-mr-2912.1) (2025-02-14)



# [3.32.0-mr-2912.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-rc.18...v3.32.0-mr-2912.0) (2025-02-14)



## [3.31.1-dev.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2878.3...v3.31.1-dev.1) (2025-02-14)



# [3.31.0-mr-2892.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2908.0...v3.31.0-mr-2892.12) (2025-02-13)



# [3.31.0-mr-2892.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2892.10...v3.31.0-mr-2892.11) (2025-02-13)


### Features

* block right click on documents ([a5cc286](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a5cc286ed8e6a2a8014132f781078e1479877c4f))



# [3.31.0-mr-2892.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2892.9...v3.31.0-mr-2892.10) (2025-02-12)



# [3.31.0-mr-2892.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2892.8...v3.31.0-mr-2892.9) (2025-02-12)



# [3.31.0-mr-2892.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2906.3...v3.31.0-mr-2892.8) (2025-02-12)



# [3.31.0-mr-2906.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2906.2...v3.31.0-mr-2906.3) (2025-02-12)



# [3.31.0-mr-2906.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2906.1...v3.31.0-mr-2906.2) (2025-02-12)



# [3.31.0-mr-2906.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2906.0...v3.31.0-mr-2906.1) (2025-02-12)



# [3.31.0-mr-2906.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2892.7...v3.31.0-mr-2906.0) (2025-02-12)



# [3.31.0-mr-2892.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2892.6...v3.31.0-mr-2892.7) (2025-02-12)


### Bug Fixes

* corrige permissao para downloads de documents ([03944bd](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/03944bdc6abbf12b27bfaf654e8db308913631cb))



# [3.31.0-mr-2892.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-rc.13...v3.31.0-mr-2892.6) (2025-02-12)



# [3.31.0-mr-2892.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2885.4...v3.31.0-mr-2892.5) (2025-02-12)



# [3.31.0-mr-2892.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-rc.12...v3.31.0-mr-2892.4) (2025-02-11)



# [3.31.0-mr-2892.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-rc.10...v3.31.0-mr-2892.3) (2025-02-11)



# [3.31.0-mr-2841.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2874.13...v3.31.0-mr-2841.0) (2025-02-11)



# [3.31.0-mr-2874.13](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2874.12...v3.31.0-mr-2874.13) (2025-02-11)


### Bug Fixes

* aplica correcoes de cr ([10555b9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/10555b947649fc8522d68124f3d0be20e9f41d3b))



# [3.31.0-mr-2874.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2874.11...v3.31.0-mr-2874.12) (2025-02-11)


### Bug Fixes

* aplica correcoes de cr ([2eceef6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/2eceef6329ed93f6321e630a317018e95dfa1dcc))



# [3.31.0-mr-2874.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2885.3...v3.31.0-mr-2874.11) (2025-02-11)


### Bug Fixes

* aplica correcoes de cr ([e7b7881](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/e7b78811df5f8c80f3c05bd0a860baea3a320d13))



# [3.31.0-mr-2874.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2892.2...v3.31.0-mr-2874.10) (2025-02-10)


### Bug Fixes

* corrige bug de unidefined ([71d0a72](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/71d0a72bf8876721d0da9bd7a57aa3eb86b6da99))



# [3.31.0-mr-2892.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-rc.9...v3.31.0-mr-2892.2) (2025-02-10)


### Bug Fixes

* corrige bug de unidefined ([f64548f](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/f64548f7bb18255b3e6efd537ae30dece09ce1c3))



# [3.31.0-mr-2892.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2892.0...v3.31.0-mr-2892.1) (2025-02-10)



# [3.31.0-mr-2892.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2900.0...v3.31.0-mr-2892.0) (2025-02-10)



## [3.31.1-mr-2900.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2874.9...v3.31.1-mr-2900.0) (2025-02-10)



# [3.31.0-mr-2874.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2841.1...v3.31.0-mr-2874.9) (2025-02-10)



# [3.31.0-mr-2841.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-rc.8...v3.31.0-mr-2841.1) (2025-02-10)



## [3.30.1-mr-2874.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-rc.7...v3.30.1-mr-2874.8) (2025-02-07)


### Bug Fixes

* atrela controlsList para variavel ([47da826](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/47da82635354d875c9427d3d5e78bd7b933dcbfe))



## [3.30.1-mr-2892.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.30.1-mr-2874.7...v3.30.1-mr-2892.0) (2025-02-06)



## [3.30.1-mr-2874.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.30.1-mr-2874.6...v3.30.1-mr-2874.7) (2025-02-06)


### Bug Fixes

* corrige erros de sitaxe encontrados ([a14dd41](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a14dd41fc295f6db20b5b7e9c38e84040718ff62))



## [3.30.1-mr-2874.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2839.3...v3.30.1-mr-2874.6) (2025-02-05)


### Bug Fixes

* ajusta bloquei dos downloads dentro do componet de reproducao ([f336a02](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/f336a024701c9c9adfb28958053f420164f02c65))



# [3.26.0-mr-2746.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2877.5...v3.26.0-mr-2746.10) (2025-02-05)



## [3.30.1-mr-2874.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2885.0...v3.30.1-mr-2874.5) (2025-02-05)


### Bug Fixes

* pega mimetype do component e reaproveita ele no bloqueio de documentos ([a524139](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a524139eea94b270e1255c444cce82f832a2739e))



## [3.30.1-mr-2874.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2858.2...v3.30.1-mr-2874.4) (2025-02-04)



## [3.31.1-mr-2878.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2877.0...v3.31.1-mr-2878.0) (2025-02-04)


### Features

* altearando o openai como IA default do digisac ([18fb7a3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/18fb7a357579d3ff09cae27590baff0d98fafe73))



## [3.30.1-mr-2841.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.27.0-mr-2858.0...v3.30.1-mr-2841.0) (2025-01-29)



## [3.30.1-mr-2840.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.29.1-mr-2854.3...v3.30.1-mr-2840.3) (2025-01-28)


### Bug Fixes

* corrige importacao ([8795ca7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/8795ca7e9a1e43966bed3a375e5e832e8b84f487))



## [3.30.1-mr-2840.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-mr-2855.0...v3.30.1-mr-2840.2) (2025-01-28)


### Bug Fixes

* altera image icon, e modifica icone para lucide ([57ef162](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/57ef1620df867b209799cf6c54ac6b836a2a44d1))



## [3.30.1-mr-2840.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.30.1...v3.30.1-mr-2840.1) (2025-01-27)


### Features

* criacao da permissao de download no front ([dbce815](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/dbce8157a42273e75ad1328ed10baf9942bd588f))



# [3.26.0-mr-2746.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.29.0-mr-2798.0...v3.26.0-mr-2746.9) (2025-01-10)



# [3.29.0-mr-2719.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.28.1-dev.0...v3.29.0-mr-2719.3) (2025-01-08)



# [3.29.0-mr-2719.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.27.1-dev.2...v3.29.0-mr-2719.2) (2025-01-07)



# [3.27.0-mr-2719.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.27.1-mr-2732.0...v3.27.0-mr-2719.1) (2025-01-06)



# [3.26.0-mr-2759.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2719.0...v3.26.0-mr-2759.5) (2025-01-02)



# [3.25.0-mr-2719.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2761.3...v3.25.0-mr-2719.0) (2024-12-31)



# [3.25.0-mr-2761.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.26.0-mr-2759.4...v3.25.0-mr-2761.3) (2024-12-31)



# [3.26.0-mr-2759.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.26.0-mr-2696.12...v3.26.0-mr-2759.4) (2024-12-31)



# [3.26.0-mr-2696.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.26.0-mr-2719.0...v3.26.0-mr-2696.12) (2024-12-31)



# [3.26.0-mr-2719.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.27.0-rc.11...v3.26.0-mr-2719.0) (2024-12-31)



# [3.26.0-mr-2755.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2761.2...v3.26.0-mr-2755.3) (2024-12-30)



# [3.25.0-mr-2761.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2759.3...v3.25.0-mr-2761.2) (2024-12-30)



# [3.25.0-mr-2759.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2761.1...v3.25.0-mr-2759.3) (2024-12-30)



# [3.25.0-mr-2761.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2761.0...v3.25.0-mr-2761.1) (2024-12-30)



# [3.25.0-mr-2761.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2759.2...v3.25.0-mr-2761.0) (2024-12-30)



# [3.25.0-mr-2759.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2759.1...v3.25.0-mr-2759.2) (2024-12-30)


### Bug Fixes

* adjust numerical custom fields limits validation ([7594e66](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/7594e66f73f1b294cfae885e757c18ad49ae588d))


### Features

* adjusts to allow minimum or maximum limits on monetary and numerical custom fields ([137dd7e](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/137dd7e2f5cd8e01dd847cb0b21b542ccb1e0e45))



# [3.25.0-mr-2759.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2755.3...v3.25.0-mr-2759.1) (2024-12-30)



# [3.25.0-mr-2755.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2755.2...v3.25.0-mr-2755.3) (2024-12-27)



# [3.25.0-mr-2755.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2759.0...v3.25.0-mr-2755.2) (2024-12-27)


### Features

* add identifier code change behavior for new custom fields ([2fe03a2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/2fe03a293fdc1c9a4a4cb4699ea00541dc1adbea))



# [3.25.0-mr-2759.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2757.0...v3.25.0-mr-2759.0) (2024-12-27)



# [3.25.0-mr-2757.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2755.1...v3.25.0-mr-2757.0) (2024-12-27)



# [3.25.0-mr-2755.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.26.1-mr-2756.0...v3.25.0-mr-2755.1) (2024-12-27)



# [3.26.0-mr-2696.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.26.0...v3.26.0-mr-2696.8) (2024-12-23)



# [3.26.0-mr-2719.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.2-dev.1...v3.26.0-mr-2719.2) (2024-12-20)



# [3.25.0-mr-2719.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.24.1-dev.2...v3.25.0-mr-2719.1) (2024-12-13)



## [3.24.1-dev.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-rc.9...v3.24.1-dev.2) (2024-12-13)





## [3.32.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.1-mr-2927.1...v3.32.1) (2025-02-21)

**Note:** Version bump only for package @digisac/front





# [3.32.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.32.0-rc.22...v3.32.0) (2025-02-18)

**Note:** Version bump only for package @digisac/front





## [3.31.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2908.2...v3.31.2) (2025-02-18)

**Note:** Version bump only for package @digisac/front





## [3.31.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.1-mr-2885.4...v3.31.1) (2025-02-13)

**Note:** Version bump only for package @digisac/front





# [3.31.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.31.0-rc.18...v3.31.0) (2025-01-29)

**Note:** Version bump only for package @digisac/front





## [3.30.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.30.1-mr-2854.6...v3.30.2) (2025-01-29)

**Note:** Version bump only for package @digisac/front





## [3.30.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.30.1-mr-2844.3...v3.30.1) (2025-01-27)

**Note:** Version bump only for package @digisac/front





# [3.30.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.30.0-rc.11...v3.30.0) (2025-01-21)

**Note:** Version bump only for package @digisac/front





## [3.29.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.29.2-mr-2811.2...v3.29.3) (2025-01-20)

**Note:** Version bump only for package @digisac/front





## [3.29.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.29.1-mr-2817.1...v3.29.2) (2025-01-17)

**Note:** Version bump only for package @digisac/front





# [3.29.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.29.0-rc.16...v3.29.0) (2025-01-14)

**Note:** Version bump only for package @digisac/front





# [3.28.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.28.0-rc.4...v3.28.0) (2025-01-07)


### Features

* force build version 3.28.0 ([f4ec577](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/f4ec5777c7c13e664ae70357fedca3ad0f1da426))





# [3.27.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.27.0-rc.10...v3.27.0) (2025-01-02)



# [3.26.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.27.0-mr-2731.3...v3.26.0) (2024-12-23)

**Note:** Version bump only for package @digisac/front





# [3.26.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.26.0-rc.7...v3.26.0) (2024-12-23)



## [3.25.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.26.0-rc.6...v3.25.2) (2024-12-20)

**Note:** Version bump only for package @digisac/front





## [3.25.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.2-mr-2735.1...v3.25.2) (2024-12-20)

**Note:** Version bump only for package @digisac/front





## [3.25.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-mr-2642.4...v3.25.1) (2024-12-16)

**Note:** Version bump only for package @digisac/front





# [3.25.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.25.0-rc.9...v3.25.0) (2024-12-16)

**Note:** Version bump only for package @digisac/front





# [3.24.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.24.0-rc.11...v3.24.0) (2024-12-09)

**Note:** Version bump only for package @digisac/front





# [3.23.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.23.0-rc.13...v3.23.0) (2024-12-02)

**Note:** Version bump only for package @digisac/front





# [3.22.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.22.0-rc.2...v3.22.0) (2024-11-25)

**Note:** Version bump only for package @digisac/front





## [3.20.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.20.1-mr-2592.4...v3.20.1) (2024-11-22)

**Note:** Version bump only for package @digisac/front





## [3.21.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.21.2-mr-2593.2...v3.21.3) (2024-11-18)

**Note:** Version bump only for package @digisac/front





## [3.21.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.21.1...v3.21.2) (2024-11-13)



## [3.21.1-mr-2609.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.21.0...v3.21.1-mr-2609.0) (2024-11-13)

**Note:** Version bump only for package @digisac/front





# [3.21.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.21.0-rc.3...v3.21.0) (2024-11-12)

**Note:** Version bump only for package @digisac/front





# [3.20.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.20.0-rc.9...v3.20.0) (2024-11-05)

**Note:** Version bump only for package @digisac/front





## [3.19.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.18.3...v3.19.3) (2024-10-31)

**Note:** Version bump only for package @digisac/front





## [3.18.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.18.3-mr-2577.2...v3.18.3) (2024-10-31)

**Note:** Version bump only for package @digisac/front





## [3.18.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.19.0...v3.18.2) (2024-10-31)

**Note:** Version bump only for package @digisac/front





# [3.19.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.19.0-rc.8...v3.19.0) (2024-10-23)

**Note:** Version bump only for package @digisac/front





# [3.18.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.18.0-rc.19...v3.18.0) (2024-10-14)

**Note:** Version bump only for package @digisac/front





## [3.17.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.17.4-mr-2474.0...v3.17.4) (2024-10-02)

**Note:** Version bump only for package @digisac/front





## [3.17.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.17.3-mr-2464.1...v3.17.3) (2024-09-26)

**Note:** Version bump only for package @digisac/front





## [3.17.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.17.1...v3.17.2) (2024-09-25)


### Bug Fixes

* **createAccount:** corrige utilização da feature-flag enableSearchMessages ([8423b42](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/8423b4204d0387600393449227145f8232ef242f))





## [3.17.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.17.1-mr-2463.0...v3.17.1) (2024-09-25)

**Note:** Version bump only for package @digisac/front





# [3.17.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.17.0-rc.8...v3.17.0) (2024-09-24)

**Note:** Version bump only for package @digisac/front





## [3.16.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.11-mr-2449.1...v3.16.12) (2024-09-24)

**Note:** Version bump only for package @digisac/front





## [3.16.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.7-mr-2442.2...v3.16.11) (2024-09-24)

**Note:** Version bump only for package @digisac/front





## [3.16.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.10-mr-2435.1...v3.16.10) (2024-09-20)

**Note:** Version bump only for package @digisac/front





## [3.16.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.9-mr-2441.7...v3.16.9) (2024-09-18)

**Note:** Version bump only for package @digisac/front





## [3.16.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.7...v3.16.8) (2024-09-18)

**Note:** Version bump only for package @digisac/front





## [3.16.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.6-mr-2425.2...v3.16.6) (2024-09-13)

**Note:** Version bump only for package @digisac/front





## [3.16.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.4-mr-2424.1...v3.16.5) (2024-09-13)

**Note:** Version bump only for package @digisac/front





## [3.16.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.4-mr-2423.0...v3.16.4) (2024-09-13)

**Note:** Version bump only for package @digisac/front





## [3.16.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.2...v3.16.3) (2024-09-11)


### Bug Fixes

* **account:** corrige bug em accountTransformer ([ce2311b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/ce2311bc4d241999c9ea938c7b5ed93ca1c26951))





## [3.16.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.2-mr-2407.1...v3.16.2) (2024-09-10)

**Note:** Version bump only for package @digisac/front





## [3.16.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.1-mr-2370.0...v3.16.1) (2024-09-09)

**Note:** Version bump only for package @digisac/front





# [3.16.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.16.0-rc.7...v3.16.0) (2024-09-05)

**Note:** Version bump only for package @digisac/front





## [3.15.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.15.3-mr-2400.1...v3.15.3) (2024-09-05)

**Note:** Version bump only for package @digisac/front





## [3.15.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.15.2-mr-2372.1...v3.15.2) (2024-08-29)

**Note:** Version bump only for package @digisac/front





## [3.15.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.15.0...v3.15.1) (2024-08-21)

**Note:** Version bump only for package @digisac/front





# [3.15.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.1...v3.15.0) (2024-08-21)



# [3.15.0-rc.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.15.0-rc.7...v3.15.0-rc.8) (2024-08-21)


### Bug Fixes

* **contacts:** corrige ortografico no nome da coluna archivedAt ([8a2dc9b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/8a2dc9b247fb4a168ee2b1bcdc5e497a57b8634b))



# [3.15.0-rc.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.4-rc.0...v3.15.0-rc.2) (2024-08-15)



## [3.13.4-rc.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.4-mr-2331.2...v3.13.4-rc.0) (2024-08-15)



## [3.13.4-mr-2331.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.7-mr-2338.0...v3.13.4-mr-2331.2) (2024-08-15)



# [3.15.0-rc.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-rc.6...v3.15.0-rc.1) (2024-08-15)



# [3.14.0-rc.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-rc.5...v3.14.0-rc.6) (2024-08-15)



# [3.14.0-rc.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-rc.4...v3.14.0-rc.5) (2024-08-15)



# [3.14.0-rc.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-mr-2327.1...v3.14.0-rc.4) (2024-08-14)



# [3.14.0-mr-2327.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.6...v3.14.0-mr-2327.1) (2024-08-14)



# [3.13.0-rc.13](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.0-mr-2306.6...v3.13.0-rc.13) (2024-08-14)



# [3.13.0-mr-2306.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.5-mr-2332.1...v3.13.0-mr-2306.6) (2024-08-14)



## [3.13.4-mr-2331.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.4-mr-2331.0...v3.13.4-mr-2331.1) (2024-08-13)



# [3.14.0-dev.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.0-mr-2306.5...v3.14.0-dev.10) (2024-08-12)



# [3.14.0-mr-2312.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.0-mr-2306.2...v3.14.0-mr-2312.3) (2024-08-09)


### Bug Fixes

* alterado condição para exportar relatório ([ad98ce3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/ad98ce32ae2bb6e27da203a79dfb76b5a8785e90))



# [3.14.0-mr-2312.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-dev.9...v3.14.0-mr-2312.2) (2024-08-06)



# [3.14.0-dev.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.12.3-mr-2304.2...v3.14.0-dev.9) (2024-08-06)



# [3.14.0-mr-2307.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-mr-2312.1...v3.14.0-mr-2307.0) (2024-08-06)



# [3.14.0-mr-2307.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-dev.8...v3.14.0-mr-2307.5) (2024-08-03)



# [3.14.0-dev.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-rc.3...v3.14.0-dev.8) (2024-08-03)



# [3.13.0-dev.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.0-dev.10...v3.13.0-dev.11) (2024-08-02)


### Reverts

* Revert "chore: atualiza yarn.lock para front e back" ([41ce8d2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/41ce8d2e1c6abacc5650279bc7613d77cf614bdd))



# [3.13.0-dev.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.3-mr-2307.4...v3.13.0-dev.10) (2024-08-02)



## [3.13.3-mr-2307.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.0-dev.9...v3.13.3-mr-2307.4) (2024-08-02)



# [3.13.0-dev.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.0-dev.8...v3.13.0-dev.9) (2024-08-02)



# [3.13.0-dev.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-dev.0...v3.13.0-dev.8) (2024-08-02)



# [3.14.0-dev.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-rc.2...v3.14.0-dev.0) (2024-08-02)



# [3.13.0-dev.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.3-mr-2307.3...v3.13.0-dev.7) (2024-08-02)



## [3.13.3-mr-2307.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.12.3-mr-2304.1...v3.13.3-mr-2307.3) (2024-08-02)



## [3.12.3-mr-2307.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.0-mr-2306.0...v3.12.3-mr-2307.2) (2024-08-02)


### Bug Fixes

* corrige a lógica na redução de retentativas, quando está no stage de otp ([d2224e5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d2224e58dde13a9ad7b04a318b9b32171f45909b))





## [3.14.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0...v3.14.1) (2024-08-21)

**Note:** Version bump only for package @digisac/front





# [3.14.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.14.0-rc.7...v3.14.0) (2024-08-21)

**Note:** Version bump only for package @digisac/front





## [3.13.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.5-mr-2332.2...v3.13.6) (2024-08-14)

**Note:** Version bump only for package @digisac/front





## [3.13.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.4-mr-2325.1...v3.13.5) (2024-08-14)

**Note:** Version bump only for package @digisac/front





## [3.13.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.3-mr-2323.3...v3.13.3) (2024-08-12)

**Note:** Version bump only for package @digisac/front





## [3.13.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.2-mr-2317.3...v3.13.2) (2024-08-09)

**Note:** Version bump only for package @digisac/front





## [3.13.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.0...v3.13.1) (2024-08-07)

**Note:** Version bump only for package @digisac/front





# [3.13.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.13.0-rc.12...v3.13.0) (2024-08-07)

**Note:** Version bump only for package @digisac/front





## [3.12.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.12.1-mr-2290.1...v3.12.1) (2024-07-24)

**Note:** Version bump only for package @digisac/front





# [3.12.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.12.0-rc.10...v3.12.0) (2024-07-23)

**Note:** Version bump only for package @digisac/front





## [3.11.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.8-mr-2281.5...v3.11.9) (2024-07-18)

**Note:** Version bump only for package @digisac/front





## [3.11.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.7-mr-2272.3...v3.11.8) (2024-07-18)

**Note:** Version bump only for package @digisac/front





## [3.11.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.6-mr-2273.6...v3.11.7) (2024-07-18)

**Note:** Version bump only for package @digisac/front





## [3.11.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.5-mr-2278.1...v3.11.6) (2024-07-18)

**Note:** Version bump only for package @digisac/front





## [3.11.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.3...v3.11.4) (2024-07-11)

**Note:** Version bump only for package @digisac/front





## [3.11.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.3-mr-2254.0...v3.11.3) (2024-07-10)

**Note:** Version bump only for package @digisac/front





## [3.11.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.2-mr-2245.0...v3.11.2) (2024-07-04)

**Note:** Version bump only for package @digisac/front





## [3.11.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.1-mr-2242.1...v3.11.1) (2024-07-04)

**Note:** Version bump only for package @digisac/front





# [3.11.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.0-rc.16...v3.11.0) (2024-07-03)



## [3.10.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.11.0-mr-2237.0...v3.10.3) (2024-07-02)

**Note:** Version bump only for package @digisac/front





## [3.10.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.10.2-mr-2228.2...v3.10.3) (2024-07-02)

**Note:** Version bump only for package @digisac/front





## [3.10.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.10.2-mr-2222.1...v3.10.2) (2024-06-28)

**Note:** Version bump only for package @digisac/front





## [3.10.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.10.1-mr-2215.0...v3.10.1) (2024-06-20)

**Note:** Version bump only for package @digisac/front





# [3.10.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.10.0-rc.6...v3.10.0) (2024-06-17)

**Note:** Version bump only for package @digisac/front





## [3.9.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.9.4-mr-2178.5...v3.9.5) (2024-06-05)

**Note:** Version bump only for package @digisac/front





## [3.9.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.9.4-mr-2163.0...v3.9.4) (2024-06-05)

**Note:** Version bump only for package @digisac/front





## [3.9.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.9.3-mr-2159.1...v3.9.3) (2024-05-28)

**Note:** Version bump only for package @digisac/front





## [3.9.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.9.2-mr-2157.1...v3.9.2) (2024-05-28)

**Note:** Version bump only for package @digisac/front





## [3.9.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.9.1-mr-2152.1...v3.9.1) (2024-05-22)

**Note:** Version bump only for package @digisac/front





# [3.9.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.9.0-rc.5...v3.9.0) (2024-05-20)

**Note:** Version bump only for package @digisac/front





## [3.8.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.8.3-mr-2115.4...v3.8.4) (2024-05-09)

**Note:** Version bump only for package @digisac/front





## [3.8.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.8.2-mr-2124.2...v3.8.3) (2024-05-09)

**Note:** Version bump only for package @digisac/front





# [3.8.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1...v3.8.0) (2024-05-06)



# [3.8.0-rc.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.8.0-rc.1...v3.8.0-rc.2) (2024-04-29)



# [3.8.0-rc.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-dev.3...v3.8.0-rc.1) (2024-04-29)



## [3.7.1-dev.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-mr-2106.1...v3.7.1-dev.3) (2024-04-29)



## [3.7.1-mr-2106.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-dev.2...v3.7.1-mr-2106.1) (2024-04-29)



## [3.7.1-dev.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-mr-2106.0...v3.7.1-dev.2) (2024-04-29)



## [3.7.1-mr-2094.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-mr-2094.7...v3.7.1-mr-2094.8) (2024-04-29)


### Bug Fixes

* **whatsapp:** corrige envio citação de mensagens. ([31a1671](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/31a16713d71007d428d9a2ffb8dc5579229df027))



## [3.7.1-mr-2094.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-mr-2094.6...v3.7.1-mr-2094.7) (2024-04-28)



## [3.7.1-mr-2094.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-mr-2094.4...v3.7.1-mr-2094.5) (2024-04-26)



## [3.7.1-mr-2094.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-mr-2094.2...v3.7.1-mr-2094.3) (2024-04-25)



## [3.7.1-mr-2094.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-dev.0...v3.7.1-mr-2094.2) (2024-04-23)



## [3.7.1-dev.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.0...v3.7.1-dev.0) (2024-04-23)



## [3.2.1-mr-2094.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.0-mr-2098.0...v3.2.1-mr-2094.1) (2024-04-22)


### Features

* adiciona argumento --ignore-engines no build da imagem do front. ([10f001e](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/10f001e15a4c6e362b8a2a2ec78928353b6a3782))



## [3.2.1-mr-2094.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.2.1-mr-2087.4...v3.2.1-mr-2094.0) (2024-04-19)



## [3.2.1-mr-2087.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.6.4-mr-2092.0...v3.2.1-mr-2087.4) (2024-04-19)



## [3.2.1-mr-2087.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.0-rc.2...v3.2.1-mr-2087.3) (2024-04-17)



## [3.2.1-mr-2087.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.2.1-mr-2084.3...v3.2.1-mr-2087.0) (2024-04-16)





## [3.7.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.1-mr-2092.0...v3.7.1) (2024-04-30)

**Note:** Version bump only for package @digisac/front





# [3.7.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.7.0-mr-2097.1...v3.7.0) (2024-04-22)

**Note:** Version bump only for package @digisac/front





## [3.6.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.6.1...v3.6.3) (2024-04-16)

**Note:** Version bump only for package @digisac/front





# [3.6.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.5.0...v3.6.0) (2024-04-08)

**Note:** Version bump only for package @digisac/front





# [3.5.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.5.0-rc.3...v3.5.0) (2024-03-25)



## [3.4.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.2.1-mr-2052.0...v3.4.5) (2024-03-13)

**Note:** Version bump only for package @digisac/front





## [3.4.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.4.4...v3.4.5) (2024-03-13)

**Note:** Version bump only for package @digisac/front





## [3.4.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.4.2-mr-1981.11...v3.4.3) (2024-03-13)

**Note:** Version bump only for package @digisac/front





## [3.4.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.4.1...v3.4.2) (2024-03-11)

**Note:** Version bump only for package @digisac/front





## [3.4.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.4.0-rc.6...v3.4.1) (2024-03-11)


### Bug Fixes

* ajuste em CSS no alinhamento do contador de chamados. ([8462e34](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/8462e34346aad3afc910e957824e7e64c5958bb8))





## [3.3.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.3.2-mr-2040.6...v3.3.2) (2024-03-01)

**Note:** Version bump only for package @digisac/front





# [3.3.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.3.0-rc.11...v3.3.0) (2024-02-26)

**Note:** Version bump only for package @digisac/front





## [3.2.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.2.3-mr-2016.0...v3.2.3) (2024-02-14)

**Note:** Version bump only for package @digisac/front





## [3.2.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.2.0-mr-2004.4...v3.2.1) (2024-02-07)

**Note:** Version bump only for package @digisac/front





# [3.2.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.2.0-rc.37...v3.2.0) (2024-02-06)



# [3.2.0-rc.23](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.1.8...v3.2.0-rc.23) (2024-02-05)



# [3.2.0-rc.17](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.2.0-rc.16...v3.2.0-rc.17) (2024-02-02)



# [3.2.0-rc.16](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.2.0-mr-1995.0...v3.2.0-rc.16) (2024-02-02)



# [3.2.0-mr-1995.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.1.8-mr-1989.0...v3.2.0-mr-1995.0) (2024-02-02)

**Note:** Version bump only for package @digisac/front





## [3.1.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.1.9...v3.1.10) (2024-02-06)

**Note:** Version bump only for package @digisac/front





## [3.1.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.1.4...v3.1.5) (2024-01-22)

**Note:** Version bump only for package @digisac/front





## [3.1.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.1.3...v3.1.4) (2024-01-22)

**Note:** Version bump only for package @digisac/front





## [3.1.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.1.2...v3.1.3) (2024-01-19)

**Note:** Version bump only for package @digisac/front





## [3.1.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.1.2-mr-1940.1...v3.1.2) (2024-01-16)

**Note:** Version bump only for package @digisac/front





# [3.1.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.1.0-rc.33...v3.1.0) (2024-01-10)

**Note:** Version bump only for package @digisac/front





## [3.0.19](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.0.18-mr-1907.3...v3.0.19) (2024-01-02)

**Note:** Version bump only for package @digisac/front





## [3.0.18](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.0.17-mr-1913.1...v3.0.18) (2024-01-02)

**Note:** Version bump only for package @digisac/front





## [3.0.17](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.0.16...v3.0.17) (2024-01-02)

**Note:** Version bump only for package @digisac/front





## [3.0.12](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.0.11...v3.0.12) (2023-11-03)

**Note:** Version bump only for package @digisac/front





## [3.0.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.0.8...v3.0.9) (2023-09-20)


### Bug Fixes

* **services:** MN-5201 - Correção na sincronização de templates da Gupshup ([53e72ab](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/53e72abe6c0e9cd3a8c6b3c519d0e5e618f6062a))





## [3.0.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.0.7...v3.0.8) (2023-09-20)

**Note:** Version bump only for package @digisac/front





## [3.0.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.0.5...v3.0.7) (2023-08-17)

**Note:** Version bump only for package @digisac/front





## [3.0.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v3.0.0-mr-1632.14...v3.0.5) (2023-08-11)

**Note:** Version bump only for package @digisac/front





# [3.0.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.0.1-mr-1358.37...v3.0.0) (2023-01-31)
## [2.10.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.10.4-mr-1435.0...v2.10.5) (2023-01-16)

**Note:** Version bump only for package @digisac/front





## New Infra

change just for release
## [2.10.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.10.4-mr-1434.0...v2.10.4) (2023-01-16)

**Note:** Version bump only for package @digisac/front





## [2.10.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.10.2...v2.10.3) (2022-12-21)


### Bug Fixes

* Release - 2.10.3 ([9f051bc](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/9f051bc3a13ed322530aa7434017c8634a7bcdb5))





## [2.10.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.10.1...v2.10.2) (2022-12-21)


### Bug Fixes

* Release - 2.10.2 ([4bc3c5c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/4bc3c5ce6202142fa064dc88b2f91833d9d1edad))





## [2.10.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.10.0...v2.10.1) (2022-12-15)


### Bug Fixes

* MN-4317- remove linhas reintroduzidas por resolução de conflito no controller de contacts ([c44785e](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c44785e9fa6ad242c25a460760c02e9a045ab64c))





# [2.10.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.9.2...v2.10.0) (2022-12-15)


### Features

* release 2.9.5 ([9bb1f89](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/9bb1f8925dbc3ea28f41ae54d6b77c3a71d17fc4))





## [2.9.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.9.1...v2.9.2) (2022-10-14)

**Note:** Version bump only for package @digisac/front





## [2.9.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.9.0...v2.9.1) (2022-10-10)


### Bug Fixes

* MN-4223 - Corrige migração de hsm para templates e interpolação de parâmetros ([739045f](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/739045f752d7a5d783c5ab97e315ff8f27c9807e))





# [2.9.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.8.1...v2.9.0) (2022-10-03)


### Features

* Atualização da master 2.8.1 ([b984950](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/b984950c3ae837c4bb20a1c7982e689cecece353))





## [2.8.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.8.0...v2.8.1) (2022-09-05)


### Reverts

* Revert "chore(release): bump version v2.7.2" ([765719f](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/765719fb9a5a4597c62d5db163ff1b01057d644d))


## Merge Develop II


## [2.7.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.7.0...v2.7.1) (2022-08-16)


### Bug Fixes

* **account:** SI-76 - exclui a permissão tickets.view.all.departments na criação do cargo Administrador. ([d2a9730](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d2a973041f03792c6fb35b378dd9abd0a2553430))





# [2.7.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.6.2...v2.7.0) (2022-08-08)



## [2.4.1-rc.31](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.30...v2.4.1-rc.31) (2022-08-02)


### Bug Fixes

* SI-13 - Retirar opção de legenda dos anexos nas conexões Instagram e Facebook ([dd143fa](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/dd143fa85fe31f5e190895728e9714b9f39447f9))



## [2.4.1-rc.30](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.29...v2.4.1-rc.30) (2022-08-02)


### Bug Fixes

* SI-39 - altera a propriedade resize para default em campos do tipo textarea ([cc1902f](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/cc1902fe89a26ea853dbbd09e793fcc20985b246))



## [2.4.1-rc.29](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.28...v2.4.1-rc.29) (2022-08-02)



## [2.4.1-rc.28](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.27...v2.4.1-rc.28) (2022-07-29)


### Features

* **tutorials:** MN-3698 - nova ordem dos videos tutoriais ([a277283](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a277283fa9bba2b18775332ea90b2fd34727f618))



## [2.4.1-rc.27](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1...v2.4.1-rc.27) (2022-07-29)


### Features

* QA-229 - automação nova tela de campanha ([063865b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/063865b66d5d7e8f7ec5cd59ebbb445fc5b5f40f))



## [2.4.1-rc.26](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.25...v2.4.1-rc.26) (2022-07-22)



## [2.4.1-rc.25](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.24...v2.4.1-rc.25) (2022-07-22)



## [2.4.1-rc.24](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.23...v2.4.1-rc.24) (2022-07-22)


### Features

* **filter:** MN-3495 - Possibilidade de filtrar contatos sem tags ([dbebdc9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/dbebdc93220e5be9363bb72660f88efd3eb42aa1))



## [2.4.1-rc.23](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.22...v2.4.1-rc.23) (2022-07-20)


### Bug Fixes

* **colors:** MN-3635 - alteração na cores do Digisac ([02bf76b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/02bf76b54aae8971d6f7d2a2166ae677b70876fb))



## [2.4.1-rc.22](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.21...v2.4.1-rc.22) (2022-07-20)


### Bug Fixes

* **language:** MN-3674 - modal de cadastro de cargo sem internacionalização ([ccd248f](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/ccd248ff0f7d7f962c4a1be9b95656d27665903c))



## [2.4.1-rc.21](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.20...v2.4.1-rc.21) (2022-07-20)


### Bug Fixes

* **campaign:** MN-3424 - Restrições na campanha ([7823c37](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/7823c372163357e98e688db58abb1ea2c21823e3))



## [2.4.1-rc.20](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.19...v2.4.1-rc.20) (2022-07-18)


### Bug Fixes

* **internalChat:** correção na validação para geração de token ([50dce64](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/50dce644617b8380f8c918dd9684c57993e3f300))



## [2.4.1-rc.19](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.18...v2.4.1-rc.19) (2022-07-15)


### Bug Fixes

* **internalChat:**  correção de verificação ([253a5b8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/253a5b831f86a2377a96c594c2d562ecaf07629e))



## [2.4.1-rc.17](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.16...v2.4.1-rc.17) (2022-07-13)



## [2.4.1-rc.16](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1-rc.15...v2.4.1-rc.16) (2022-07-12)


### Bug Fixes

* **thumbnails:** MN-2847 -Duplicação de imagens thumbnails ([08d501b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/08d501b900b567a2ecb66365673e800308271d28))



## [2.4.1-rc.15](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.0...v2.4.1-rc.15) (2022-07-12)


### Bug Fixes

* **profile:** SI-50 - corrige quando tenta salvar senha em branco na tela de perfil ([a3ab26c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/a3ab26c6ea1f5fc2a6d560412080638afb9611bf))





## [2.6.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.6.0...v2.6.1) (2022-08-05)


### Bug Fixes

* versão 2.5.1 ([c5076fe](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c5076fe349ac9169a7afa5b56c5260d80bff57f3))





## [2.5.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.5.3...v2.5.4) (2022-08-05)


### Bug Fixes

* version 2.5.0 ([100e5c6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/100e5c63fac5563e1b1da540f5a97c0657f9eaf6))





## [2.5.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.5.2...v2.5.3) (2022-08-05)


### Bug Fixes

* version 2.4.3 ([f417337](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/f417337366499b1181c1c35ba0eba109faec2be1))





## [2.5.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.5.1...v2.5.2) (2022-08-04)

**Note:** Version bump only for package @digisac/front





## [2.5.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.4.1...v2.5.1) (2022-08-04)

**Note:** Version bump only for package @digisac/front





# [2.4.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.3.2...v2.4.0) (2022-07-04)


### Features

* **version:** versão 2.4.3 ([948d839](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/948d83905515c4787772b2ec521bd10f77039f0f))




## [2.3.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.3.1...v2.3.2) (2022-06-10)


### Bug Fixes

* corrige o tratamento de usuário já autenticado em outra sessão ([91f5a9b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/91f5a9bbbcbf97c05d60e9b06c85ba373fd58aaa))





## [2.3.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.3.0...v2.3.1) (2022-06-06)


### Bug Fixes

* **bot:** correções e melhorias no socket ([2b87642](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/2b8764230e78d132401866b7a88c6cd301088e5b))





# [2.3.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.2.6...v2.3.0) (2022-05-30)


### Features

* nova conexão com Google Businesss ([3f5e6dd](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/3f5e6dd4761c99bf6175a2825e49ad237bb9177e))





## [2.2.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.2.4-rc.12...v2.2.5) (2022-05-23)



## [2.2.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.2.4-rc.1...v2.2.4) (2022-05-09)

**Note:** Version bump only for package @digisac/front





## [2.2.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.2.4-rc.1...v2.2.4) (2022-05-09)

**Note:** Version bump only for package @digisac/front





## [2.2.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.2.2...v2.2.3) (2022-04-27)


### Bug Fixes

* **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([bf0359c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/bf0359c2521ec73756f105230d8db70f37c1b960))





## [2.2.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.2.1...v2.2.2) (2022-04-20)


### Bug Fixes

* **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([aaa401e](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/aaa401eace6a85ff60291b310a0337e803dc45a9))





## [2.2.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.2.0...v2.2.1) (2022-04-11)


### Bug Fixes

* **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([38880cd](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/38880cdf931a33fc4c60692e28be54a66671fbb8))





# [2.2.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.0.1-rc.245...v2.2.0) (2022-04-07)



# [2.1.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.0.1-rc.242...v2.1.0) (2022-04-06)


### Features

* ajusta versao de master ([c3fba04](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c3fba048b00e951e84f80a68aee93e4cf0a3c65a))
* ajusta versao de master ([add0443](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/add04433bf8cf5260b4d567059cc0c172cf889fd))
* ajusta versao de master ([d6d90e9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d6d90e9a9f12be645424f0d52310e8cd2cfd75f5))





# [2.1.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v2.0.1-rc.241...v2.1.0) (2022-04-06)


### Features

* ajusta versao de master ([c3fba04](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c3fba048b00e951e84f80a68aee93e4cf0a3c65a))
* ajusta versao de master ([add0443](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/add04433bf8cf5260b4d567059cc0c172cf889fd))
* ajusta versao de master ([d6d90e9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d6d90e9a9f12be645424f0d52310e8cd2cfd75f5))





## [1.179.74](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.73...v1.179.74) (2022-01-04)


### Bug Fixes

* **whatsapp:** altera consulta que força sincronização de mensagens para recuperar os idFromServices distintamente. ([04f7445](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/04f74457684bc477c45ea24f0202625ba1ef093c))





## [1.179.73](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.72...v1.179.73) (2022-01-03)

**Note:** Version bump only for package @digisac/front





## [1.179.72](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.71...v1.179.72) (2022-01-03)

**Note:** Version bump only for package @digisac/front





## [1.179.71](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.70...v1.179.71) (2022-01-03)

**Note:** Version bump only for package @digisac/front





## [1.179.70](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.69...v1.179.70) (2022-01-03)


### Bug Fixes

* **statistics:** ajusta a comparação de datas pois devido ao timezone alguns tickets estavam sendo contados no dia seguinte. ([d688bca](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/d688bca64674ae11c7981e3bca0367d0cbd8a9a0))





## [1.179.69](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.68...v1.179.69) (2022-01-03)


### Bug Fixes

* **integrations:** ajusta permissão de visualização para os pontos onde se tem acesso a integrações. ([ac07e20](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/ac07e207ce2de0a51987878a1b3d0c00435b19a3))





## [1.179.68](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.67...v1.179.68) (2022-01-03)

**Note:** Version bump only for package @digisac/front





## [1.179.67](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.66...v1.179.67) (2021-12-20)

**Note:** Version bump only for package @digisac/front





## [1.179.66](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.65...v1.179.66) (2021-12-20)


### Bug Fixes

* **contacts:** corrige pontos do sistema em que o ofuscamento de número de telefones não estava atendendo a permissão contacts.view.number ([7aa5e2b](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/7aa5e2bbf792f263a105aab511e02ed01b99cc48))





## [1.179.65](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.64...v1.179.65) (2021-12-10)

**Note:** Version bump only for package @digisac/front





## [1.179.64](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.63...v1.179.64) (2021-12-10)

**Note:** Version bump only for package @digisac/front





## [1.179.63](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.62...v1.179.63) (2021-12-10)

**Note:** Version bump only for package @digisac/front





## [1.179.62](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.61...v1.179.62) (2021-12-10)

**Note:** Version bump only for package @digisac/front





## [1.179.61](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.60...v1.179.61) (2021-12-10)


### Bug Fixes

* **timezones:** Copia os arquivos do package node_modules/timezones.json, para utils tanto no back como no front; Modifica as abreviações que estão em duplicidade para serem tratadas distintamente. ([746a572](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/746a572317d7b4a310b27f7947c6f44072b19265))





## [1.179.60](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.59...v1.179.60) (2021-11-27)

**Note:** Version bump only for package @digisac/front





## [1.179.59](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.58...v1.179.59) (2021-11-27)

**Note:** Version bump only for package @digisac/front





## [1.179.58](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.57...v1.179.58) (2021-11-27)

**Note:** Version bump only for package @digisac/front





## [1.179.57](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.56...v1.179.57) (2021-11-18)

**Note:** Version bump only for package @digisac/front





## [1.179.56](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.55...v1.179.56) (2021-11-18)

**Note:** Version bump only for package @digisac/front





## [1.179.55](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.54...v1.179.55) (2021-11-12)

**Note:** Version bump only for package @digisac/front





## [1.179.54](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.53...v1.179.54) (2021-11-12)

**Note:** Version bump only for package @digisac/front





## [1.179.53](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.52...v1.179.53) (2021-11-12)


### Features

* **email:** disponibiliza script que reconverte mensagens de e-mail que já foram convertidas a partir do HTML, necessário quando não foi recebido o text pelo IMAP. ([550adec](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/550adecdd866afa17c371fac6bf0d86d7cede132))





## [1.179.52](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.51...v1.179.52) (2021-11-12)

**Note:** Version bump only for package @digisac/front





## [1.179.51](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.50...v1.179.51) (2021-11-12)

**Note:** Version bump only for package @digisac/front





## [1.179.50](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.49...v1.179.50) (2021-11-12)

**Note:** Version bump only for package @digisac/front





## [1.179.48](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.47...v1.179.48) (2021-11-01)

**Note:** Version bump only for package @digisac/front





## [1.179.47](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.46...v1.179.47) (2021-11-01)

**Note:** Version bump only for package @digisac/front





## [1.179.44](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.43...v1.179.44) (2021-10-26)

**Note:** Version bump only for package @digisac/front





## [1.179.43](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.42...v1.179.43) (2021-10-26)

**Note:** Version bump only for package @digisac/front





## [1.179.42](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.41...v1.179.42) (2021-10-26)

**Note:** Version bump only for package @digisac/front





## [1.179.41](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.40...v1.179.41) (2021-10-26)

**Note:** Version bump only for package @digisac/front





## [1.179.39](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.38...v1.179.39) (2021-10-22)


### Bug Fixes

* **migration:** corrige os files que que estão deletados porém associados a contatos e conexões ativas. ([c0929a5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c0929a54d9b5ae89dd87114305bd74a8d15eb41e))





## [1.179.38](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.37...v1.179.38) (2021-10-21)

**Note:** Version bump only for package @digisac/front





## [1.179.37](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.36...v1.179.37) (2021-10-21)

**Note:** Version bump only for package @digisac/front





## [1.179.36](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.35...v1.179.36) (2021-10-21)

**Note:** Version bump only for package @digisac/front





## [1.179.35](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.34...v1.179.35) (2021-10-21)

**Note:** Version bump only for package @digisac/front





## [1.179.34](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.33-mr-488.2...v1.179.34) (2021-10-21)

**Note:** Version bump only for package @digisac/front





## [1.179.33](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.33-mr-499.0...v1.179.33) (2021-10-19)

**Note:** Version bump only for package @digisac/front





## [1.179.32](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.31...v1.179.32) (2021-10-18)

**Note:** Version bump only for package @digisac/front





## [1.179.31](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.30...v1.179.31) (2021-10-14)

**Note:** Version bump only for package @digisac/front





## [1.179.30](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.29...v1.179.30) (2021-10-14)

**Note:** Version bump only for package @digisac/front





## [1.179.29](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.9...v1.179.29) (2021-10-13)


### Bug Fixes

* corrige script de check-version.sh ([898142a](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/898142a1657a2e33ec627628d74c03d2048e6a06))





## [1.179.9](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.28...v1.179.9) (2021-10-13)


### Bug Fixes

* corrige lint em stable ([cd0604c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/cd0604c429b7d52c1bcc242244fa8467a5c94ab8))





## [1.179.28](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.27...v1.179.28) (2021-10-11)



## [1.178.23-mr-354.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.178.22...v1.178.23-mr-354.0) (2021-08-20)

**Note:** Version bump only for package @digisac/front





## [1.179.26](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.13...v1.179.26) (2021-10-04)

**Note:** Version bump only for package @digisac/front





## [1.179.13](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.25...v1.179.13) (2021-10-04)

**Note:** Version bump only for package @digisac/front





## [1.179.25](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.24...v1.179.25) (2021-10-04)

**Note:** Version bump only for package @digisac/front





## [1.179.24](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.23...v1.179.24) (2021-09-30)

**Note:** Version bump only for package @digisac/front





## [1.179.22](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.21...v1.179.22) (2021-09-30)


### Bug Fixes

* lerna version ([adcab35](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/adcab35ac8bc4f81e521606cb469394a2fde7f0d))





## [1.179.14](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.16...v1.179.14) (2021-09-30)

**Note:** Version bump only for package @digisac/front





## [1.179.21](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.16...v1.179.21) (2021-09-30)

**Note:** Version bump only for package @digisac/front





## [1.179.20](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.16...v1.179.20) (2021-09-24)

**Note:** Version bump only for package @digisac/front





## [1.179.19](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.16...v1.179.19) (2021-09-23)

**Note:** Version bump only for package @digisac/front





## [1.179.18](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.16...v1.179.18) (2021-09-23)

**Note:** Version bump only for package @digisac/front





## [1.179.17](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.15...v1.179.17) (2021-09-23)

**Note:** Version bump only for package @digisac/front





## [1.179.4](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.15...v1.179.4) (2021-09-23)

**Note:** Version bump only for package @digisac/front





## [1.179.16](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.15...v1.179.16) (2021-09-23)

**Note:** Version bump only for package @digisac/front





## [1.179.15](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.12-mr-420.1...v1.179.15) (2021-09-23)


### Bug Fixes

* modifica yarn.lock ([6823fee](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/6823feeb36a9e9be70ae3be0a5a371970d6c8429))





## [1.179.14](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.12-mr-420.1...v1.179.14) (2021-09-23)


### Bug Fixes

* modifica yarn.lock ([6823fee](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/6823feeb36a9e9be70ae3be0a5a371970d6c8429))





## [1.179.15](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.12-mr-420.1...v1.179.15) (2021-09-21)


### Bug Fixes

* modifica yarn.lock ([6823fee](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/6823feeb36a9e9be70ae3be0a5a371970d6c8429))





## [1.179.14](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.12-mr-420.1...v1.179.14) (2021-09-21)

**Note:** Version bump only for package @digisac/front





## [1.179.11](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.8...v1.179.11) (2021-09-10)

**Note:** Version bump only for package @digisac/front





## [1.179.10](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.8...v1.179.10) (2021-09-03)

**Note:** Version bump only for package @digisac/front

## [1.179.8](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.7...v1.179.8) (2021-09-02)

**Note:** Version bump only for package @digisac/front

## [1.179.7](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.5...v1.179.7) (2021-09-02)

**Note:** Version bump only for package @digisac/front

## [1.179.6](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.5...v1.179.6) (2021-09-01)

**Note:** Version bump only for package @digisac/front

## [1.179.5](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.1...v1.179.5) (2021-08-31)

**Note:** Version bump only for package @digisac/front

### Bug Fixes

## [1.179.3-mr-389.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.3-rc.0...v1.179.3-mr-389.0) (2021-09-02)

- adiciona ajustes labels ([5be5acf](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/5be5acf917e83cdcd733e7732bc52acaf35ddea0))

### Features

- estatisticas HSMs ([0db171c](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/0db171cc4c43ea9966d9f0ff4d24bd77072e7fba))

## [1.179.3-rc.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.178.21-rc.3...v1.179.3-rc.0) (2021-08-24)

## [1.179.3-mr-362.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.3-rc.8...v1.179.3-mr-362.1) (2021-08-30)

## [1.179.3-mr-362.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.3-rc.3...v1.179.3-mr-362.0) (2021-08-25)

**Note:** Version bump only for package @digisac/front

## [1.179.3-mr-362.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.3-rc.3...v1.179.3-mr-362.0) (2021-08-25)

**Note:** Version bump only for package @digisac/front

## [1.179.3-mr-361.3](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.3-mr-361.2...v1.179.3-mr-361.3) (2021-08-25)

**Note:** Version bump only for package @digisac/front

## [1.179.3-mr-361.1](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.3-mr-361.0...v1.179.3-mr-361.1) (2021-08-25)

**Note:** Version bump only for package @digisac/front

## [1.179.3-mr-361.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.1...v1.179.3-mr-361.0) (2021-08-25)

**Note:** Version bump only for package @digisac/front

## [1.179.2](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.179.1...v1.179.2) (2021-08-23)

**Note:** Version bump only for package @digisac/front

## [1.161.1-mr-76.0](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/compare/v1.161.0...v1.161.1-mr-76.0) (2021-04-14)

### Features

- **socket:** otimização de socket de mensagens e contatos ([c4dde6d](https://gitlab.mandeumzap.com.br/mandeumzap/mandeumzap-front/commit/c4dde6d1f242244d22daeed5206a1ab76db2901a))
