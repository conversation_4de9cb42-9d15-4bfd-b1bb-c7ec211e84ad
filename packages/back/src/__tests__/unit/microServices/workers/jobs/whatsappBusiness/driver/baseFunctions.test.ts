// Mock the baseFunctions module to replace httpClient
jest.mock('../../../../../../../microServices/workers/jobs/whatsappBusiness/driver/baseFunctions', () => {
  const mockHttpClient = {
    get: jest.fn(() => Promise.resolve({ data: [] })),
    post: jest.fn(() => Promise.resolve({ data: {} })),
    put: jest.fn(() => Promise.resolve({ data: {} })),
    delete: jest.fn(() => Promise.resolve({ data: {} })),
    patch: jest.fn(() => Promise.resolve({ data: {} })),
    request: jest.fn(() => Promise.resolve({ data: {} })),
  }

  const originalModule = jest.requireActual('../../../../../../../microServices/workers/jobs/whatsappBusiness/driver/baseFunctions')
  return {
    ...originalModule,
    httpClient: mockHttpClient,
  }
})

jest.mock('typedi', () => ({
  Container: {
    get: jest.fn(() => ({
      log: jest.fn(),
    })),
  },
}))
jest.mock('../../../../../../../core/config', () =>
  jest.fn(() => {
    return {
      baseUrl: 'http://localhost',
      password: 'testPassword',
      username: 'testUser',
      partnerId: 'testPartnerId',
    }
  }),
)
jest.mock('../../../../../../../core/resources/contactResource', () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  findById: jest.fn(),
}))
jest.mock('../../../../../../../core/resources/whatsappBusinessTemplateResource', () => ({
  findById: jest.fn(),
}))
jest.mock('../../../../../../../core/resources/userResource', () => ({
  findById: jest.fn(() => ({ name: 'Test User' })),
}))
jest.mock('../../../../../../../core/resources/serviceResource', () => ({
  update: jest.fn(),
  updateById: jest.fn(),
  findById: jest.fn(),
}))
jest.mock('../../../../../../../core/utils/whatsapp/comparableIdFromService', () => jest.fn((n) => n))
jest.mock('../../../../../../../core/utils/getTimezoneMinutesOffset', () => jest.fn(() => 0))
jest.mock('../../../../../../../core/services/httpClient/HttpClient', () => {
  return {
    HttpClient: jest.fn().mockImplementation(() => ({
      get: jest.fn(() => Promise.resolve({ data: [] })),
      post: jest.fn(() => Promise.resolve({ data: {} })),
      put: jest.fn(() => Promise.resolve({ data: {} })),
      delete: jest.fn(() => Promise.resolve({ data: {} })),
      patch: jest.fn(() => Promise.resolve({ data: {} })),
      request: jest.fn(() => Promise.resolve({ data: {} })),
    }))
  }
})
jest.mock('../../../../../../../core/utils/wait', () => jest.fn(() => Promise.resolve()))
jest.mock('../../../../../../../core/utils/validator/validateNumberFormat', () => ({
  verifyIsNumberOrEmail: jest.fn(() => true),
}))
jest.mock('../../../../../../../core/utils/error/HsmLimitExceededError', () =>
  jest.fn(function () {
    this.message = 'Hsm limit exceeded'
  }),
)
jest.mock('../../../../../../../core/utils/error/BadRequestHttpError', () =>
  jest.fn(function (msg) {
    this.message = msg
  }),
)
jest.mock('../../../../../../../core/dbSequelize/models/Contact', () => jest.fn())
jest.mock('../../../../../../../core/services/logs/Logger', () => jest.fn())

import * as baseFunctions from '../../../../../../../microServices/workers/jobs/whatsappBusiness/driver/baseFunctions'

describe('baseFunctions', () => {
  describe('getMediaType', () => {
    it('returns correct media type', () => {
      expect(baseFunctions.getMediaType('audio')).toBe('audio')
      expect(baseFunctions.getMediaType('image')).toBe('image')
      expect(baseFunctions.getMediaType('video')).toBe('video')
      expect(baseFunctions.getMediaType('file')).toBe('document')
      expect(baseFunctions.getMediaType('other')).toBe('unknow')
    })
  })

  describe('getTypeByMimetype', () => {
    it('returns correct type by mimetype', () => {
      expect(baseFunctions.getTypeByMimetype('audio/mp3')).toBe('audio')
      expect(baseFunctions.getTypeByMimetype('image/png')).toBe('image')
      expect(baseFunctions.getTypeByMimetype('video/mp4')).toBe('video')
      expect(baseFunctions.getTypeByMimetype('application/pdf')).toBe('document')
      expect(baseFunctions.getTypeByMimetype(undefined)).toBe('chat')
    })
  })

  describe('getDateByTimestamp', () => {
    it('handles unix and ms timestamps', () => {
      expect(baseFunctions.getDateByTimestamp(**********)).toEqual(new Date(********** * 1000))
      expect(baseFunctions.getDateByTimestamp(*************)).toEqual(new Date(*************))
      expect(baseFunctions.getDateByTimestamp(undefined)).toBeInstanceOf(Date)
    })
  })

  describe('getWebhookType', () => {
    it('returns correct type for meta', () => {
      const req = {
        object: 'whatsapp_business_account',
        entry: [{ changes: [{ value: { messages: [{ type: 'reaction' }] } }] }],
      }
      expect(baseFunctions.getWebhookType(req, true)).toBe('reaction')
      const req2 = { object: 'whatsapp_business_account', entry: [{ changes: [{ value: { messages: [{}] } }] }] }
      expect(baseFunctions.getWebhookType(req2, true)).toBe('messages')
      const req3 = { object: 'whatsapp_business_account', entry: [{ changes: [{ value: { statuses: [{}] } }] }] }
      expect(baseFunctions.getWebhookType(req3, true)).toBe('statuses')
      const req4 = { object: 'whatsapp_business_account', entry: [{ changes: [{ value: { errors: [{}] } }] }] }
      expect(baseFunctions.getWebhookType(req4, true)).toBe('errors')
      expect(
        baseFunctions.getWebhookType(
          {
            object: 'whatsapp_business_account',
            entry: [{ changes: [{}] }],
          },
          true,
        ),
      ).toBe('unknow')
    })
    it('returns correct type for non-meta', () => {
      expect(baseFunctions.getWebhookType({ code: 1 })).toBe('errors')
      expect(baseFunctions.getWebhookType({ statuses: [{}] })).toBe('statuses')
      expect(baseFunctions.getWebhookType({ messages: [{}] })).toBe('messages')
      expect(baseFunctions.getWebhookType({ new_quality_score: 1 })).toBe('template_quality')
      expect(baseFunctions.getWebhookType({ new_status: 1 })).toBe('template_status')
      expect(baseFunctions.getWebhookType({})).toBe('unknow')
    })
  })

  describe('getSendersContact', () => {
    it('returns mapped contacts', () => {
      const contacts = [{ profile: { name: 'A' }, wa_id: '1' }]
      expect(baseFunctions.getSendersContact(contacts)).toEqual([{ name: 'A', number: '1' }])
      expect(baseFunctions.getSendersContact(undefined)).toBeUndefined()
    })
  })

  describe('getVcard', () => {
    it('returns vcard string', () => {
      const contact = {
        name: { first_name: 'A', last_name: 'B' },
        phones: [{ wa_id: '123', phone: '(12)3-4' }],
      }
      expect(baseFunctions.getVcard(contact)).toContain('BEGIN:VCARD')
      expect(baseFunctions.getVcard({ name: {}, phones: [] })).toBeNull()
    })
  })

  describe('getFullNumber', () => {
    it('returns correct full number', () => {
      expect(baseFunctions.getFullNumber('5514998989898')).toBe('5514998989898')
      expect(baseFunctions.getFullNumber('551498989898')).toBe('5514998989898')
      expect(baseFunctions.getFullNumber('14998989898')).toBe('5514998989898')
      expect(baseFunctions.getFullNumber('1498989898')).toBe('5514998989898')
      expect(baseFunctions.getFullNumber('123')).toBe('123')
    })
  })

  describe('parseNumber', () => {
    it('removes non-digits', () => {
      expect(baseFunctions.parseNumber('55-1499 8989-898')).toBe('5514998989898')
      expect(baseFunctions.parseNumber(undefined)).toBe('')
    })
  })

  describe('getPossibleNumbers', () => {
    it('returns all number possibilities', () => {
      const res = baseFunctions.getPossibleNumbers('5514998989898')
      expect(res.length).toBe(4)
    })
  })

  describe('formatNumberToSendMessage', () => {
    it('formats number for provider', () => {
      expect(baseFunctions.formatNumberToSendMessage('positus', '5514998989898')).toBe('+5514998989898')
      expect(baseFunctions.formatNumberToSendMessage('positus', '+5514998989898')).toBe('+5514998989898')
      expect(baseFunctions.formatNumberToSendMessage('360Dialog', '+5514998989898')).toBe('5514998989898')
      expect(baseFunctions.formatNumberToSendMessage('360Dialog', '5514998989898')).toBe('5514998989898')
      expect(baseFunctions.formatNumberToSendMessage('other', '5514998989898')).toBe('5514998989898')
      expect(baseFunctions.formatNumberToSendMessage('other', undefined)).toBeUndefined()
    })
  })

  describe('phoneNumberIsValid', () => {
    it('calls verifyIsNumberOrEmail', () => {
      expect(baseFunctions.phoneNumberIsValid('5514998989898')).toBe(true)
    })
  })

  describe('validatePayload', () => {
    it('returns false for undefined request', () => {
      expect(baseFunctions.validatePayload(undefined)).toBe(false)
    })
    it('returns false for missing messages', () => {
      expect(baseFunctions.validatePayload({})).toBe(false)
    })
    it('returns true for valid messages', () => {
      expect(baseFunctions.validatePayload({ messages: [{ type: 'text' }] })).toBe(true)
    })
    it('returns false for message with error', () => {
      expect(baseFunctions.validatePayload({ messages: [{ errors: [{ code: 400 }] }] })).toBe(false)
    })
    it('returns true for message with error code 501', () => {
      expect(baseFunctions.validatePayload({ messages: [{ errors: [{ code: 501 }] }] })).toBe(true)
    })
  })

  describe('interpolateParameters', () => {
    it('interpolates parameters', () => {
      expect(baseFunctions.interpolateParameters('Hello {{0}}', ['World'], 'Contact')).toEqual({
        text: 'Hello World',
        parameters: ['World'],
      })
      expect(baseFunctions.interpolateParameters('Hello', [], 'Contact')).toEqual({ text: 'Hello' })
    })
  })

  describe('findParamsInComponent', () => {
    it('finds params in component', () => {
      const comps = [{ type: 'BODY', format: 'TEXT', text: 'Hello {{1}} {{100}}' }]
      expect(baseFunctions.findParamsInComponent(comps, 'BODY', 'TEXT')).toEqual(['{{1}}', '{{100}}'])
      expect(baseFunctions.findParamsInComponent([], 'BODY', 'TEXT')).toEqual([])
    })
  })

  describe('hasError', () => {
    it('detects error for positus', () => {
      expect(
        baseFunctions.hasError('positus', {
          contacts: { contacts: [{ status: 'invalid' }] },
          meta: { http_code: 400 },
        }),
      ).toBe(true)
      expect(
        baseFunctions.hasError('positus', { contacts: { contacts: [{ status: 'valid' }] }, meta: { http_code: 200 } }),
      ).toBe(false)
    })
    it('detects error for 360Dialog', () => {
      expect(baseFunctions.hasError('360Dialog', { errors: [{ code: 1 }], meta: { http_code: 400 } })).toBe(1)
      expect(baseFunctions.hasError('360Dialog', { errors: [], meta: { http_code: 200 } })).toBe(false)
    })
  })

  describe('handleError', () => {
    it('returns error for positus', async () => {
      const res = await baseFunctions.handleError('positus', { contacts: { contacts: [{ status: 'invalid' }] } })
      expect(res.error).toBe(1013)
    })
    it('returns error for 360Dialog', async () => {
      const res = await baseFunctions.handleError('360Dialog', { errors: [{ code: 123 }] })
      expect(res.error).toBe(123)
    })
    it('returns UNKNOWN if no code', async () => {
      const res = await baseFunctions.handleError('360Dialog', {})
      expect(res.error).toBe('UNKNOWN')
    })
  })

  describe('validateHsmParams', () => {
    it('returns true for valid params', async () => {
      const hsm = { components: [{ type: 'BODY', text: 'Hello {{1}}' }] }
      const params = [{ type: 'body', parameters: [{ text: 'A', type: 'text' }] }]
      expect(await baseFunctions.validateHsmParams(hsm, params)).toBe(true)
    })
    it('returns false for invalid params', async () => {
      const hsm = { components: [{ type: 'BODY', text: 'Hello {{1}}' }] }
      const params = [{ type: 'body', parameters: [{ text: '', type: '' }] }]
      expect(await baseFunctions.validateHsmParams(hsm, params)).toBe(false)
    })
    it('returns true if no body', async () => {
      expect(await baseFunctions.validateHsmParams(undefined, [])).toBe(true)
    })
  })

  describe('getOrigin', () => {
    it('returns correct origin', () => {
      expect(baseFunctions.getOrigin({ hsm: true })).toBe('hsmChat')
      expect(baseFunctions.getOrigin({ hsmId: true })).toBe('hsmChat')
      expect(baseFunctions.getOrigin({ origin: 'campaign' })).toBe('hsmCampaign')
      expect(baseFunctions.getOrigin({ interactiveMessage: true })).toBe('interactive')
      expect(baseFunctions.getOrigin({})).toBe('normalChat')
    })
  })

  describe('getMessageType', () => {
    it('returns correct type', () => {
      expect(baseFunctions.getMessageType({ type: 'text' })).toBe('chat')
      expect(baseFunctions.getMessageType({ type: 'contacts' })).toBe('vcard')
      expect(baseFunctions.getMessageType({ type: 'location' })).toBe('location')
      expect(baseFunctions.getMessageType({ type: 'audio' })).toBe('audio')
      expect(baseFunctions.getMessageType({ type: 'document' })).toBe('document')
      expect(baseFunctions.getMessageType({ type: 'image' })).toBe('image')
      expect(baseFunctions.getMessageType({ type: 'video' })).toBe('video')
      expect(baseFunctions.getMessageType({ type: 'voice' })).toBe('audio')
      expect(baseFunctions.getMessageType({ type: 'sticker' })).toBe('sticker')
      expect(baseFunctions.getMessageType({ type: 'unknow' })).toBe('unknow')
      expect(baseFunctions.getMessageType({ type: 'button' })).toBe('chat')
      expect(baseFunctions.getMessageType({ type: 'interactive' })).toBe('chat')
      expect(baseFunctions.getMessageType({ type: 'invalid' })).toBe('chat')
      expect(baseFunctions.getMessageType({ type: 'caption', errors: [{ code: 501 }] })).toBe('caption')
    })
  })

  describe('isChargedMessage', () => {
    it('returns true if no lastChargedMessage', () => {
      expect(baseFunctions.isChargedMessage({ data: {} })).toBe(true)
    })
    it('returns true if difference >= 1440', () => {
      const oldDate = new Date(Date.now() - 1450 * 60 * 1000).toISOString()
      expect(baseFunctions.isChargedMessage({ data: { lastChargedMessage: oldDate } })).toBe(true)
    })
    it('returns false if difference < 1440', () => {
      const recentDate = new Date().toISOString()
      expect(baseFunctions.isChargedMessage({ data: { lastChargedMessage: recentDate } })).toBe(false)
    })
  })

  describe('getTypeOfMessageByMimetype', () => {
    it('returns correct type', () => {
      expect(baseFunctions.getTypeOfMessageByMimetype('chat')).toBe('text')
      expect(baseFunctions.getTypeOfMessageByMimetype('image')).toBe('media')
      expect(baseFunctions.getTypeOfMessageByMimetype('document')).toBe('media')
      expect(baseFunctions.getTypeOfMessageByMimetype('audio')).toBe('media')
      expect(baseFunctions.getTypeOfMessageByMimetype('sticker')).toBe('media')
      expect(baseFunctions.getTypeOfMessageByMimetype('video')).toBe('media')
      expect(baseFunctions.getTypeOfMessageByMimetype('hsm')).toBe('hsm')
      expect(baseFunctions.getTypeOfMessageByMimetype('vcard')).toBe('contacts')
      expect(baseFunctions.getTypeOfMessageByMimetype('interactive')).toBe('interactive')
      expect(baseFunctions.getTypeOfMessageByMimetype('other')).toBe('unknow')
    })
  })

  describe('formatTemplateCloudApi', () => {
    it('formats template for AUTHENTICATION', () => {
      const data = {
        components: [
          { type: 'BODY', add_security_recommendation: true },
          { type: 'BUTTONS' },
          { type: 'FOOTER', active_code_expiration_minutes: true, code_expiration_minutes: 10 },
          { type: 'HEADER' },
        ],
        category: 'AUTHENTICATION',
      }
      const result = baseFunctions.formatTemplateCloudApi({ ...data })
      expect(result.components.some((c) => c.type === 'BODY')).toBe(true)
      expect(result.components.some((c) => c.type === 'FOOTER')).toBe(true)
      expect(result.components.some((c) => c.type === 'HEADER')).toBe(true)
      expect(result.components.some((c) => c.type === 'BUTTONS')).toBe(true)
    })
    it('formats template for non-AUTHENTICATION', () => {
      const data = {
        components: [{ type: 'BODY' }, { type: 'BUTTONS' }, { type: 'FOOTER' }, { type: 'HEADER' }],
        category: 'OTHER',
      }
      const result = baseFunctions.formatTemplateCloudApi({ ...data })
      expect(result.components.some((c) => c.type === 'BODY')).toBe(true)
      expect(result.components.some((c) => c.type === 'FOOTER')).toBe(true)
      expect(result.components.some((c) => c.type === 'HEADER')).toBe(true)
      expect(result.components.some((c) => c.type === 'BUTTONS')).toBe(true)
    })
  })

  describe('getWebhookInGatewayByUrl', () => {
    beforeEach(() => {
      jest.clearAllMocks()
    })

    it('should call httpClient.get with correct params and return first webhook', async () => {
      jest.spyOn(baseFunctions.httpClient, 'get').mockResolvedValue({ data: [{ id: 1, webhookUrl: 'url1' }] })
      const result = await baseFunctions.getWebhookInGatewayByUrl('url1')
      expect(result).toEqual({ id: 1, webhookUrl: 'url1' })
    })

    it('should call httpClient.get Duplicated webhook on gateway', async () => {
      jest.spyOn(baseFunctions.httpClient, 'get').mockResolvedValue({
        data: [
          { id: 1, webhookUrl: 'url1' },
          { id: 2, webhookUrl: 'url1' },
        ],
        status: 200,
        statusText: 'OK',
        headers: {},
        config: { headers: {} },
      })
      const spyLog = jest.spyOn(baseFunctions, 'log').mockResolvedValue(undefined)
      await baseFunctions.getWebhookInGatewayByUrl('url1')
      const expectedArgs = [
        'Duplicated webhook on gateway\nResponse: %',
        'warn',
        [
          [
            { id: 1, webhookUrl: 'url1' },
            { id: 2, webhookUrl: 'url1' },
          ],
        ],
      ]
      const matchingCalls = spyLog.mock.calls.filter(
        (call) => call[0] === expectedArgs[0] && call[1] === expectedArgs[1],
      )
      expect(matchingCalls).toHaveLength(1)
      expect(spyLog).toHaveBeenCalledWith(...expectedArgs)
    })
  })

  describe('createOrUpdateDataInGateway', () => {
    const mockGet = jest.fn()
    const mockConfig = jest.fn()

    beforeEach(() => {
      jest.clearAllMocks()
      jest.mock('../../../../../../../core/config', () => mockConfig)
    })

    it('should return early if disableWabaWebhookUrlSet is true', async () => {
      mockConfig.mockImplementation((key) => (key === 'disableWabaWebhookUrlSet' ? true : 'mocked'))
      const result = await baseFunctions.createOrUpdateDataInGateway('serviceId', 'driverId')
      expect(result).toBeUndefined()
      expect(mockGet).not.toHaveBeenCalled()
    })
  })

  describe('authDialog', () => {
    const mockConfig = jest.fn()

    beforeEach(() => {
      jest.clearAllMocks()
      jest.mock('../../../../../../../core/config', () => mockConfig)
      mockConfig.mockImplementation((key) => {
        if (key === 'hub360Api') return { baseUrl: 'http://api', username: 'success', password: 'success' }
        return ''
      })
    })

    it('should call post with correct url and payload', async () => {
      jest.spyOn(baseFunctions.httpClient, 'post').mockResolvedValue({ data: { access_token: 'abc123' } })
      const result = await baseFunctions.authDialog('success', 'success')
      expect(result).toBe('Bearer abc123')
    })

    it('should return Bearer undefined if no access_token', async () => {
      jest.spyOn(baseFunctions.httpClient, 'post').mockResolvedValue({ data: {} })
      const result = await baseFunctions.authDialog(undefined, undefined)
      expect(result).toBe('Bearer undefined')
    })

    it('should throw if post fails', async () => {
      const error = new Error('fail')
      jest.spyOn(baseFunctions.httpClient, 'post').mockRejectedValue(error)
      await expect(baseFunctions.authDialog('fail', 'fail')).rejects.toThrow('fail')
    })
  })

  describe('mmliteStatusDialog', () => {
    beforeEach(() => {
      jest.clearAllMocks()
    })

    it('throws BadRequestHttpError if partnerId or wabaId is missing', async () => {
      await expect(baseFunctions.mmliteStatusDialog({})).rejects.toThrow('Invalid partner ID or waba ID')
    })

    it('returns mmLiteStatus when all parameters are present', async () => {
      jest.spyOn(baseFunctions.httpClient, 'post').mockResolvedValue({ data: { access_token: 'abc123' } })
      jest
        .spyOn(baseFunctions.httpClient, 'get')
        .mockResolvedValue({ data: { settings: { marketing_messages_lite_api_status: 'ONBOARDED' } } })
      const service = { internalData: { waba_account: { id: 'wabaIdSuccess' } } }
      const result = await baseFunctions.mmliteStatusDialog(service)
      expect(result).toEqual({ wabaInfo: { mmLiteStatus: 'ONBOARDED' } })
    })

    it('propagates error from getHttpClient', async () => {
      const error = new Error('fail')
      jest.spyOn(baseFunctions.httpClient, 'post').mockResolvedValue({ data: { access_token: 'abc123' } })
      jest.spyOn(baseFunctions.httpClient, 'get').mockRejectedValue(error)
      const service = { internalData: { waba_account: { id: 'wabaIdError' } } }
      await expect(baseFunctions.mmliteStatusDialog(service)).rejects.toThrow(error)
    })
  })

  describe('sendMessageMMLITEDialog', () => {
    const mockToken = 'undefined'
    const mockServiceId = 'service123'
    const mockReceivedData = { foo: 'bar' }

    beforeEach(() => {
      jest.clearAllMocks()
    })

    it('should send marketing message and return result', async () => {
      const spyPost = jest.spyOn(baseFunctions.httpClient, 'post').mockResolvedValue({ data: {} })
      await baseFunctions.sendMessageMMLITEDialog(mockReceivedData, mockServiceId)
      expect(spyPost).toHaveBeenCalledWith('https://waba-v2.360dialog.io/marketing_messages', mockReceivedData, {
        headers: {
          'Content-Type': 'application/json',
          'D360-Api-Key': mockToken,
        },
      })
    })

    it('should propagate error from httpClient.post', async () => {
      const error = new Error('fail')
      jest.spyOn(baseFunctions.httpClient, 'post').mockRejectedValue(error)
      await expect(baseFunctions.sendMessageMMLITEDialog(mockReceivedData, mockServiceId)).rejects.toThrow('fail')
    })
  })

  describe('dialogGetWabaAccount', () => {
    beforeEach(() => {
      jest.clearAllMocks()
    })

    it('should return waba account data when httpClient.get resolves', async () => {
      jest.spyOn(baseFunctions.httpClient, 'post').mockResolvedValueOnce({ data: { access_token: 'abc123' } })
      jest
        .spyOn(baseFunctions.httpClient, 'get')
        .mockResolvedValueOnce({
          data: {
            health_status: { entities: [{ entity_type: 'WABA', id: '123' }] },
          },
        })
        .mockResolvedValueOnce({ data: { partner_channels: [{ waba_account: { external_id: '123' } }] } })

      const result = await baseFunctions.dialogGetWabaAccount('wabaId123')
      expect(result).toEqual({ external_id: '123' })
      expect(baseFunctions.httpClient.get).toHaveBeenCalledWith(
        expect.stringContaining('testPartnerId'),
        expect.any(Object),
      )
    })

    it('should throw error when httpClient.get rejects', async () => {
      const error = new Error('fail')
      jest.spyOn(baseFunctions.httpClient, 'post').mockResolvedValueOnce({ data: { access_token: 'abc123' } })
      jest
        .spyOn(baseFunctions.httpClient, 'get')
        .mockResolvedValueOnce({
          data: {
            health_status: { entities: [{ entity_type: 'WABA', id: '123' }] },
          },
        })
        .mockRejectedValueOnce(error)

      await expect(baseFunctions.dialogGetWabaAccount('wabaId123')).rejects.toThrow('fail')
    })
  })
})
