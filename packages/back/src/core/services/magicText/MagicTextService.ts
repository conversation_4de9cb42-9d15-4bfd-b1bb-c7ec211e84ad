import { OpenAI } from 'openai'
import { Container, Service } from 'typedi'
import Logger from '../logs/Logger'
import config from '../../config'
import creditMovementResource from '../../resources/creditMovementResource'
import accountResource from '../../resources/accountResource'

const logger = Container.get(Logger)

@Service()
export default class MagicTextService {
  async limitReached(accountId: string): Promise<Boolean> {
    try {
      const account = await accountResource.findById(accountId, { attributes: ['plan'] })
      if (!account?.plan?.renewDate) {
        logger.log(`There is not a plan renew date for account id ${accountId}`, 'warn')
        return true
      }

      const credits = await creditMovementResource.balance(accountId, 'magic-text', account?.plan?.renewDate)
      return credits <= 0
    } catch (error) {
      logger.log(error)
      return true
    }
  }

  async transformer(message: string, tone: string, accountId: string, serviceId: string, userId: string) {
    if (config('blockAiConsumption')) {
      if (await this.limitReached(accountId)) {
        logger.log('The magic text limit has been reached', 'error')
        return 'MAGIC_TEXT_LIMIT_REACHED'
      }
    }

    const getPrompt = () => {
      if (tone == 'correct') return 'Corrija a ortografia, não traduzir o texto, não inclua o prompt na resposta.'
      return `Corrija a ortografia, não traduzir o texto, rescreva a frase para que tenha um tom ${tone}. Não inclua o prompt na resposta.\n`
    }

    const apiKey = config('apiKeyOpenIA')
    const openai = new OpenAI({
      apiKey: apiKey,
    })

    const messagePrompt = `${getPrompt()} :\n\n${message}`

    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: 'Você é um assistente útil.' },
          { role: 'user', content: messagePrompt },
        ],
      })

      await creditMovementResource.createDebit({
        accountId,
        serviceType: 'magic-text',
        amount: 1,
        origin: 'single',
        serviceId: serviceId,
        userId: userId,
      })

      return { answer: response?.choices[0]?.message?.content }
    } catch (error) {
      logger.log(error, 'error')
      throw new Error(`Magic text: Error when requesting the assistant ai`)
    }
  }
}
