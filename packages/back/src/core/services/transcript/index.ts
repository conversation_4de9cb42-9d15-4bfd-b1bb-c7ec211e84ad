import { Container, Inject, Service } from 'typedi'
import FormData from 'form-data'
import OpenAI, { toFile } from 'openai'
import Logger from '../logs/Logger'
import { MessageInstance } from '../../dbSequelize/models/Message'
import { AccountInstance } from '../../dbSequelize/models/Account'
import messageResource from '../../resources/messageResource'
import accountResource from '../../resources/accountResource'
import fileResource from '../../resources/fileResource'
import creditMovementResource from '../../resources/creditMovementResource'
import fileTransformer from '../../transformers/fileTransformer'
import config from '../../config'
import { getAudioMetadata } from '../../utils/parseAudio'
import { HttpClient } from '../httpClient/HttpClient'
import { CreditOrigin } from '../../dbSequelize/models/CreditMovement'

const logger = Container.get(Logger)

@Service()
export default class Transcript {
  protected resource = messageResource

  @Inject()
  protected httpClient: HttpClient

  async getAudioTime(url: string, fileId?: string): Promise<number> {
    const response = await this.httpClient.request({
      method: 'get',
      url: url,
      responseType: 'stream',
    })

    const metadata = await getAudioMetadata(response.data, fileId)

    return metadata?.duration
  }

  async limitReached(message: MessageInstance, audioTime: number): Promise<Boolean> {
    try {
      if (!config('blockAiConsumption')) {
        return false
      }

      const account = await accountResource.findById(message?.accountId, { attributes: ['plan'] })
      if (!account?.plan?.renewDate) {
        logger.log(`There is not a plan renew date for account id ${message?.accountId}`, 'warn')
        return true
      }

      const credits = await creditMovementResource.balance(
        message?.accountId,
        'transcription',
        account?.plan?.renewDate,
      )

      if (credits <= 0) return true

      return credits - audioTime < 0
    } catch (error) {
      logger.log(error)
      return true
    }
  }

  async transcribe(messageId: string, notCharge?: boolean, origin?: CreditOrigin): Promise<String | void> {
    try {
      const message = await this.resource.findOne({
        where: { id: messageId },
        include: ['file'],
      })

      const account = await accountResource.findById(message?.accountId, { attributes: ['plan', 'creditsControl'] })

      const file = await fileTransformer(message.file)
      if (!file) {
        logger.log(`Message id ${messageId} hasn't file`)
        return
      }

      const audioTime = await this.getAudioTime(file.url, message.file.id)
      if (!notCharge && (await this.limitReached(message, audioTime))) {
        logger.log('The transcriptions limit has been reached\n\n')
        await messageResource.updateById(
          message.id,
          {
            text: 'TRANSCRIPTION_LIMIT_REACHED',
            isTranscribing: false,
            transcribeError: true,
            data: { showMessage: true },
          },
          { mergeJson: ['data'] },
        )
        return 'TRANSCRIPTION_LIMIT_REACHED'
      }

      const apiUrl = config('apiTranscriberUrl')
      const apiToken = config('apiTranscriberToken')

      if (!apiUrl) {
        logger.log(`Message id ${messageId} is using Whisper OpenAI to transcribe`)
        return this.sendByWhisper(message, audioTime, account, notCharge)
      }

      const audioBuffer = Buffer.from(await fileResource.getBase64(message.file), 'base64')

      const dataSend = new FormData()
      const newNameFile = `${messageId}.${file?.extension}`
      dataSend.append('audio', audioBuffer, newNameFile)

      const transcription = await this.httpClient.post(apiUrl, dataSend, {
        headers: {
          Authorization: `Bearer ${apiToken}`,
        },
      })

      await this.updateMessage(message, transcription?.data?.text, audioTime, account, notCharge, origin)

      return transcription?.data?.text
    } catch (err) {
      if (err?.response?.status === 503) {
        logger.log(`Invalid audio to transcribe. Message id ${messageId}.`)
        await messageResource.updateById(messageId, {
          text: 'INVALID_AUDIO_TO_TRANSCRIBE',
          transcribeError: false,
          isTranscribing: false,
        })
        return err
      }
      logger.log(err)
      await messageResource.updateById(messageId, { transcribeError: true, isTranscribing: false })
      return err
    }
  }

  async sendByWhisper(message: MessageInstance, audioTime: number, account: AccountInstance, notCharge: boolean) {
    const apiKey = config('apiKeyOpenIA')
    const openai = new OpenAI({ apiKey })

    const file = message.file

    const audioBuffer = Buffer.from(await fileResource.getBase64(file), 'base64')

    const transcription = await openai.audio.transcriptions.create({
      file: await toFile(audioBuffer, file.name),
      model: 'whisper-1',
    })

    await this.updateMessage(message, transcription.text, audioTime, account, notCharge)

    return transcription.text
  }

  async updateMessage(
    message: MessageInstance,
    text: string,
    audioTime: number,
    account: AccountInstance,
    notCharge: boolean,
    origin?: CreditOrigin,
  ) {
    await messageResource.update(
      message,
      {
        text,
        transcribeError: false,
        isTranscribing: false,
        data: {
          showMessage: !notCharge,
        },
      },
      { mergeJson: ['data'] },
    )

    if (!notCharge) {
      await creditMovementResource.createDebit({
        accountId: account?.id ?? message?.accountId,
        serviceType: 'transcription',
        amount: audioTime,
        origin: origin ? origin : 'single',
        serviceId: message?.serviceId,
      })
    }
  }
}
