import { OpenAI } from 'openai'
import { Inject, Service } from 'typedi'
import Logger from '../logs/Logger'
import messageResource from '../../resources/messageResource'
import summaryResource from '../../resources/summaryResource'
import creditMovementResource from '../../resources/creditMovementResource'
import contactResource from '../../resources/contactResource'
import accountResource from '../../resources/accountResource'
import config from '../../config'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import { TicketInstance } from '../../dbSequelize/models/Ticket'
import { ContactInstance } from '../../dbSequelize/models/Contact'
import { SummaryInstance } from '../../dbSequelize/models/Summary'
import Transcript from '../transcript'
import QueueJobsDispatcher from '../jobs/queue/QueueJobsDispatcher'
import QueuedAudioTranscribeJob from '../../../microServices/workers/jobs/transcribe/QueuedAudioTranscribeJob'
import { language } from '../../utils/translateHeaderTicketHistory'
import { HttpClient } from '../httpClient/HttpClient'
import notificationResource from '../../resources/notificationResource'

@Service()
export default class SmartSummaryService {
  @Inject()
  protected transcript: Transcript

  @Inject()
  protected queueJobsDispatcher: QueueJobsDispatcher

  @Inject()
  protected logger: Logger

  @Inject()
  protected httpClient: HttpClient

  async start(
    ticket: TicketInstance,
    eventType: string,
    contact: ContactInstance,
    transactions?: any,
    previousSummary?: SummaryInstance,
  ) {
    const canSummarize = await this.canSummarize(ticket)
    const messages = await messageResource.getMessagesForSummary(ticket)
    const clientMessages = messages.filter((message) => message.isFromMe === false)

    if (!canSummarize || messages?.length === 0 || clientMessages?.length === 0) return

    const account =
      ticket?.account ||
      (await accountResource.findById(ticket?.accountId, {
        attributes: ['settings'],
      }))

    if (eventType === 'finalize' && !account?.settings?.autoGenerateSummaryOnClosure) return
    if (eventType === 'transfer' && !account?.settings?.autoGenerateSummaryOnTransfer) return

    const getMessageSummary = async () => {
      if (previousSummary) {
        return messageResource.update(previousSummary?.message, { isTranscribing: true, transcribeError: false })
      }
      return messageResource.create(
        {
          contact,
          timestamp: new Date(),
          type: 'summary',
          ticketId: ticket?.id,
          isFromMe: true,
          contactId: contact.id,
          serviceId: contact.serviceId,
          accountId: contact.accountId,
          isTranscribing: true,
          ticketDepartmentId: ticket?.departmentId,
        },
        { ...transactions },
      )
    }

    const messageSummary = await getMessageSummary()

    if (previousSummary) {
      await summaryResource.update(previousSummary, { finishedAt: null, isProcessing: false })
    } else {
      await summaryResource.create({
        eventType,
        ticketId: ticket?.id,
        messageId: messageSummary?.id,
        accountId: ticket?.accountId,
        isProcessing: false,
      })
    }

    const messageAudios = messages.filter(
      (me) => ['audio', 'ptt'].includes(me?.type) && (!me?.text || me?.transcribeError === true) && !me?.isTranscribing,
    )

    await queuedAsyncMap(messageAudios, async (messageAudio) => {
      await messageResource.updateById(messageAudio.id, { isTranscribing: true })

      await this.queueJobsDispatcher.dispatch<QueuedAudioTranscribeJob>(
        'queued-audio-transcribe',
        { idMessage: messageAudio?.id, notCharge: true },
        {
          hashKey: messageAudio?.id,
        },
      )
    })
  }

  async limitReached(accountId: string, notSendNotification: boolean): Promise<Boolean> {
    const account = await accountResource.findById(accountId, { attributes: ['plan', 'settings'] })
    if (!config('blockAiConsumption') || !account?.settings?.flags?.['enable-smart-summary']) {
      return false
    }

    if (!account?.plan?.renewDate) {
      this.logger.log(`There is not a plan renew date for account id ${accountId}`, 'warn')
      return true
    }

    const credits = await creditMovementResource.balance(accountId, 'summary', account?.plan?.renewDate)
    if (credits <= 0) {
      await this.limitReachedNotification(accountId, notSendNotification)
      return true
    }
    return false
  }

  async limitReachedNotification(accountId: string, notSendNotification: boolean): Promise<void> {
    if (notSendNotification) {
      return
    }
    const users = await summaryResource.getUsersAdminNotNotified(accountId, ['id', 'accountId'])
    await queuedAsyncMap(users, async (user) => {
      await notificationResource.create({
        accountId: user.accountId,
        userId: user.id,
        read: false,
        text: '',
        type: 'limit-reached',
      })
    })
  }

  async summarize(ticket: TicketInstance, summary: SummaryInstance) {
    try {
      const textLanguage = await language({ userId: ticket?.userId })
      const messages = await messageResource.getMessagesForSummary(ticket)

      if (messages?.length === 0) {
        throw new Error(`Summary: There are no messages for ticket id ${ticket.id} to summarize`)
      }

      const decryptedMessagesPromises = messages.map(async (m) => {
        const roleTalk = m.isFromMe === true ? textLanguage?.attendant : textLanguage?.customer
        const text = await messageResource.decryptMessageText(m)
        return `${roleTalk} ${text}`
      })

      const decryptedMessages = await Promise.all(decryptedMessagesPromises)

      if (decryptedMessages?.length === 0) {
        throw new Error(`Summary: There is a problem to decrypt messages of the ticket id ${ticket.id}`)
      }

      const prompt =
        summary?.eventType === 'transfer'
          ? messages?.[0]?.account.promptAiTransfer
          : messages?.[0]?.account.promptAiFinalize

      const response = await this.integrationByOpenAi(decryptedMessages.join('\n'), prompt)

      await creditMovementResource.createDebit({
        accountId: ticket?.accountId,
        serviceType: 'summary',
        amount: 1,
        origin: 'single',
        serviceId: messages?.[0]?.serviceId,
      })

      return await messageResource.updateById(summary.messageId, {
        text: response?.answer ?? response,
        isTranscribing: false,
        transcribeError: false,
        ticketDepartmentId: ticket?.departmentId,
      })
    } catch (err) {
      // this.logger.log(err, 'error')
      await messageResource.updateById(summary.messageId, { isTranscribing: false, transcribeError: true })
    } finally {
      await summaryResource.updateById(summary?.id, {
        finishedAt: new Date(),
        isProcessing: false,
      })
    }
  }

  async integrationByOpenAi(decryptedMessages: string, prompt: string) {
    const apiKey = config('apiKeyOpenIA')
    const openai = new OpenAI({
      apiKey: apiKey,
    })
    const messagePrompt = `${prompt} :\n\n${decryptedMessages}`
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: 'Você é um assistente útil.' },
          { role: 'user', content: messagePrompt },
        ],
      })

      const message = response?.choices[0]?.message?.content
      return message
    } catch (error) {
      // this.logger.log(error, 'error')
      throw new Error(`Summary: Error when requesting the assistant ia`)
    }
  }

  async canSummarize(ticket: TicketInstance) {
    const account = await accountResource.findById(ticket?.accountId, { attributes: ['settings'] })
    const contact = await contactResource.findById(ticket?.contactId, { attributes: ['data'] })

    if (!(account.settings.flags || {})['enable-smart-summary']) {
      return false
    }

    if (contact?.data?.botIsRunning) {
      return false
    }

    return true
  }
}
