/* eslint-disable complexity */
import { timeout } from 'promise-timeout'
import { isEmpty, omit } from 'lodash'
import Container from 'typedi'
import { subMilliseconds, isBefore, differenceInDays } from 'date-fns'
import getSize from 'image-size-from-base64'
import { Transaction } from 'sequelize'
import BaseResource, { CREATED, DESTROYED, EventTransaction, UPDATED } from './BaseResource'
import messageRepository from '../dbSequelize/repositories/messageRepository'
import answersResource from './answersResource'
import blockMessageRuleResource from './blockMessageRuleResource'
import contactResource from './contactResource'
import serviceResource, {
  serviceTypesAllowedToSendReactions,
  serviceProviderTypeEnabledReactions,
  serviceProviderTypeBlockReactionOwnMessage,
} from './serviceResource'
import ticketResource from './ticketResource'
import summaryResource from './summaryResource'
import ticketTransfersResource from './ticketTransfersResource'
import { decryptTextForAccount, encryptTextForAccount } from '../services/crypt/accountCryptor'
import { idToNumber, numberToId } from '../utils/whatsapp/numberParser'
import fileResource from './fileResource'
import stickerResource from './stickerResource'
import stickerUserResource from './stickerUserResource'
import BadRequestHttpError from '../utils/error/BadRequestHttpError'
import parseBase64Url from '../utils/base64/parseBase64Url'
import NotFoundHttpError from '../utils/error/NotFoundHttpError'
import driverService from '../services/driverService'
import { ContactInstance, ContactOrigin } from '../dbSequelize/models/Contact'
import { MessageInstance } from '../dbSequelize/models/Message'
import ticketService from '../services/ticket/ticketService'
import Logger from '../services/logs/Logger'
import { AccountInstance } from '../dbSequelize/models/Account'
import messageTransformer from '../transformers/messageTransformer'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import hasPermission from '../utils/hasPermission'
import getUrlFileSize from '../utils/getUrlFileSize'
import getDataMessage from '..//utils/getDataMessage'
import ValidationError from '../utils/error/ValidationError'
import accountResource from './accountResource'
import campaignMessageProgressResource from './campaignMessageProgressResource'
import { WhatsappBusinessTemplateInstance } from '../dbSequelize/models/WhatsappBusinessTemplate'
import { getTypeByMimetype } from '../../microServices/workers/jobs/whatsappBusiness/driver/baseFunctions'
import { getStorage } from '../services/storage'
import { mustConvertAudio } from '../utils/convertAudio'
import generatePdf from '../services/pdf'
import CreditsControlCacheService, {
  mustConsumeServiceTypes,
} from '../../core/services/creditMovement/CreditsControlCacheService'
import { ServiceType } from '../../microServices/workers/jobs/account/updateContractPlan/Types'
import comparableIdFromService from '../utils/whatsapp/comparableIdFromService'
import reportError from '../services/logs/reportError'
import MagicTextService from '../services/magicText/MagicTextService'
import SmartSummaryService from '../services/SmartSummary/SmartSummaryService'
import configValues from '../configValues'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'
import linkRepository from '../dbSequelize/repositories/linkRepository'
import sequelize from '../../core/services/db/sequelize'
import { TicketInstance } from '../dbSequelize/models/Ticket'
import getLinksFromText from '../../core/utils/getLinksFromText'

const logger = Container.get(Logger)

/**
 * @throws {Error}
 */
const validateSizeOfFiles = async (data): Promise<void | never> => {
  const { attachments, file, service } = data

  if (service?.data?.service === 'Hotmail') {
    if (attachments && attachments.length) {
      const filteredArray = []

      await attachments.map(async (attachment) => {
        const item = await getSize(attachment.base64)
        filteredArray.push(item)
      })

      const size = filteredArray.reduce((a, b) => parseInt(a) + parseInt(b))
      if (size > 3072) {
        throw new ValidationError(`File should contain lesser than 3mb but contain ${size}mb`)
      }

      return
    }
  }

  const getFileSize = async () => {
    const { url, base64 } = file
    return (url && (await getUrlFileSize(url))) || (base64 && (await getSize(base64)))
  }

  if (service.type === 'whatsapp-business' && file) {
    const fileSize = await getFileSize()
    if (
      !fileSize ||
      Math.ceil(fileSize / 1024) <= (['video', 'image'].some((i) => file.mimetype.includes(i)) ? 16 : 30)
    )
      return

    throw new BadRequestHttpError(`File should contain lesser than 16mb but contain ${fileSize}mb`)
  }

  if (service.type === 'whatsapp' && file) {
    const fileSize = await getFileSize()
    if (!fileSize || Math.ceil(fileSize / 1024) <= 64) return

    throw new BadRequestHttpError(`File should contain lesser than 64mb but contain ${fileSize}mb`)
  }
}

const parseFile = ({
  base64,
  mimetype,
  name,
  ...rest
}: { url?: string; base64?: string; mimetype: string; name: string } & any) => {
  base64 = base64 || ''
  name = String(name || rest?.fileName).replace(/ /g, '-')

  if (!base64?.startsWith('data:')) {
    return {
      ...rest,
      base64,
      mimetype,
      name,
    }
  }

  const parsed = parseBase64Url(base64 || rest.base64Url)

  return {
    ...rest,
    base64: parsed.base64,
    mimetype: mimetype || parsed.mimetype,
    name,
  }
}

export const handleWithNumber = async ({
  name,
  number,
  serviceId,
  accountId,
  tagIds,
}: {
  name?: string
  number?: string
  serviceId: string
  accountId: string
  tagIds?: string[]
}) => {
  const service = await serviceResource.findOne({
    where: { id: serviceId, accountId },
  })

  if (!service) {
    throw new NotFoundHttpError(`Service (${serviceId}) not found.`)
  }

  if (!service.data.status.isConnected) {
    throw new BadRequestHttpError(`Service (${service.id}) disconnected.`)
  }

  const idFromServiceRaw = numberToId(number)

  const validIdFromService = configValues.workersGoServices.includes(service.type)
    ? await Container.get(HttpJobsDispatcher).dispatch(
        'get-valid-contact-id',
        {
          serviceId: serviceId,
          contactId: idFromServiceRaw,
        },
        {
          timeout: 2 * 60_000,
          useWorkersGo: true,
        },
      )
    : await driverService.getValidId(serviceId, idFromServiceRaw)

  let idFromService = validIdFromService || idFromServiceRaw

  if (service.type === 'whatsapp-business') {
    idFromService = idToNumber(idFromService)
  }

  let contact = await contactResource.findOne({
    where: {
      accountId,
      serviceId,
      idFromService: comparableIdFromService(idFromService, service.type),
      archivedAt: null,
    },
    include: [
      'service',
      'account',
      {
        model: 'currentTicket',
        include: ['currentTicketTransfer'],
      },
      'tags',
    ],
  })

  if (!contact) {
    // @TODO
    // O correto é:
    // `WhatsApp contact (${number}) doesn't exist.`,
    // não podemos alterar na /v1 pois pode quebrar aplicações de clientes
    if (!validIdFromService) {
      throw new BadRequestHttpError(`WhatsApp contact (${number}) doest exists.`)
    }

    const contactData = {
      idFromService,
      name: name || number,
      isMe: false,
      data: { number },
      serviceId,
      accountId,
      tagIds,
      visible: true,
      origin: ContactOrigin.Driver,
    }

    contact = await contactResource.create(contactData, {
      include: ['service', 'account', 'tags', 'defaultUser'],
    })
  } else {
    contact = await contactResource.addTags(contact, tagIds, {
      include: ['service', 'account', 'tags'],
    })
  }

  return contact
}

const handleWithContactId = async ({ contactId, accountId }: { contactId?: string; accountId: string }) => {
  const contact = await contactResource.findOne({
    where: { id: contactId, accountId },
    include: [
      'service',
      'account',
      {
        model: 'currentTicket',
        include: ['currentTicketTransfer'],
      },
    ],
  })

  if (!contact) {
    throw new NotFoundHttpError(`Contact (${contactId}) not found.`)
  }

  const { service } = contact

  if (!service || (service && service.deletedAt)) {
    throw new NotFoundHttpError(`Service (${service.id}) not found.`)
  }

  if (!service.data.status.isConnected) {
    throw new BadRequestHttpError(`Service (${service.id}) disconnected.`)
  }

  return contact
}

export const WIDGET_MESSAGE_RECEIVED = 'widget_message_received'
export const USER_MESSAGE_SENT = 'user_message_sent'

const messageTypesNotAllowedToSendReactions = ['reaction', 'comment', 'ticket']

export class MessageResource extends BaseResource<MessageInstance> {
  constructor() {
    super(messageRepository)

    this.events = [CREATED, UPDATED, DESTROYED, WIDGET_MESSAGE_RECEIVED, USER_MESSAGE_SENT]
  }

  onWidgetMessageReceived(listener) {
    return this.on(WIDGET_MESSAGE_RECEIVED, listener)
  }

  emitWidgetMessageReceived(data, eventTransaction) {
    return this.emit(WIDGET_MESSAGE_RECEIVED, data, eventTransaction)
  }

  onUserMessageSent(listener) {
    return this.on(USER_MESSAGE_SENT, listener)
  }

  emitUserMessageSent(data, eventTransaction) {
    return this.emit(USER_MESSAGE_SENT, data, eventTransaction)
  }

  async verifyBlockMessageRule(message: Partial<MessageInstance>, contactId: string): Promise<boolean> {
    const isComment = message.isComment || message.type === 'comment'
    const isNotBlocked = ['ticket', 'unblock_message_rule', 'summary'].includes(message.type)
    if (isComment || isNotBlocked) return false

    const result = await blockMessageRuleResource.contactBlockedByMessageRule(contactId)

    return result?.isBlockedToSendMessage
  }

  mustConsumeCredits(message: Partial<MessageInstance>, isNew = true) {
    const isNotComment = !message.isComment && message.type !== 'comment'
    const isNotCampaign = message.origin !== 'campaign'
    const isPaidMessage = !['ticket', 'schedule', 'reaction'].includes(message.type) && isNew

    return isNotComment && isNotCampaign && isPaidMessage
  }

  async verifyMessageConsumeCredits(
    accountId: string,
    message: Partial<MessageInstance>,
    serviceType: ServiceType,
    isNew = true,
    isOut = true,
  ) {
    const creditsControlCacheService = Container.get(CreditsControlCacheService)

    if (!mustConsumeServiceTypes.includes(serviceType)) {
      return { mustConsumeCredits: false, insufficientCredits: false }
    }

    const hasCreditsControlEnabled = await creditsControlCacheService.isCreditsControlEnabled(accountId)
    if (!hasCreditsControlEnabled) return { mustConsumeCredits: false, insufficientCredits: false }

    const shouldConsumeCredits = this.mustConsumeCredits(message, isNew)
    if (!shouldConsumeCredits) return { mustConsumeCredits: false, insufficientCredits: false }

    const insufficientCredits = await creditsControlCacheService.insufficientCreditsToCreateMessage(accountId, isOut)

    return { mustConsumeCredits: true, insufficientCredits }
  }

  async send(
    data: {
      campaignMessageProgressId?: string
      campaignId?: string
      contact?: ContactInstance
      contactId?: string
      number?: string
      isFromMe?: boolean
      itemIndex?: number
      type?: string
      isComment?: boolean
      name?: string
      isPtt?: boolean
      fileId?: string
      filesIds?: string[]
      file?: {
        isPtt?: boolean
        base64?: string
        mimetype: string
        name: string
      }
      userId?: string
      departmentId?: string
      text?: string
      quotedMessageId?: string
      dontOpenTicket?: boolean
      dontSendUserName?: boolean
      origin?: string
      botId?: string
      accountId?: string
      serviceId?: string
      tagId?: string[]
      isFromSurvey?: boolean
      attachments?: {
        base64: string
        mimetype: string
        name: string
      }[]
      subject?: string
      ccs?: string
      hsm?: string & WhatsappBusinessTemplateInstance
      hsmId?: string
      hsmFileId?: string
      parameters?: string
      extraOptions?: any
      actions?: any
      mask?: string
      fileTemplate?: {
        url?: string
      }
      hsmParameters?: any
      vcard?: any
      interactiveMessage?: any
      outsideRequest?: boolean
      stickerId?: string
      mentionedList?: string[]
      scheduleId?: string
    },
    options: {
      eventTransaction?: EventTransaction
      include?: any
      transaction?: Transaction
    } = {},
  ): Promise<MessageInstance | {}> {
    const contact =
      data.contact ||
      // @ts-ignore
      (await (data.number ? handleWithNumber(data) : handleWithContactId(data)))

    const service = contact.service || (await serviceResource.findById(contact.serviceId, {}))

    if (!contact) throw new Error('Contact missing.')
    if (!contact.account && contact.accountId) {
      contact.account = await accountResource.findById(contact.accountId)
    }
    if (!contact.account) throw new Error('Contact account missing.')

    if (!contact.currentTicket && contact.currentTicketId) {
      contact.currentTicket = await ticketResource.findById(contact.currentTicketId, {
        include: ['currentTicketTransfer'],
      })
    }

    // Verifica bloqueio por DDD
    const isBlockedToSendMessage = await this.verifyBlockMessageRule(data, contact.id)
    if (isBlockedToSendMessage) {
      throw new Error('Contact blocked to send message by rule')
    }

    const { mustConsumeCredits, insufficientCredits } = await this.verifyMessageConsumeCredits(
      data.accountId,
      data,
      service?.type as ServiceType,
    )
    if (mustConsumeCredits && insufficientCredits) {
      throw new Error('Insufficient credits to send this message.')
    }

    await validateSizeOfFiles({
      contact,
      service,
      attachments: data.attachments,
      file: data.file,
    })

    const type = data.type ? data.type : !data.file ? 'chat' : undefined

    const { currentTicket } = contact

    const quotedMessage =
      data.quotedMessageId &&
      (await this.findOne({
        where: { id: data.quotedMessageId },
      }))

    if (
      !data.isFromSurvey &&
      currentTicket &&
      !data.itemIndex &&
      !data.dontOpenTicket &&
      currentTicket.userId !== data.userId &&
      data.origin !== 'bot' &&
      !contact.block &&
      !data.isComment &&
      !data.outsideRequest
    ) {
      await contactResource.transferTicket(
        {
          contact,
          userId: data.origin === 'campaign' ? data?.userId ?? currentTicket?.userId : data?.userId,
          departmentId:
            data.origin === 'campaign' ? data?.departmentId ?? currentTicket?.departmentId : currentTicket.departmentId,
          byUserId: data.userId,
          timestamp: subMilliseconds(Date.now(), 300),
        },
        {
          eventTransaction: options.eventTransaction,
          transaction: options.transaction,
        },
      )
    }

    // Mantendo para retrocompatibilidade com app mas já migrei para o front, agora só vem o fileId final
    if (type === 'sticker' && data.origin === 'user' && !data.fileId) {
      // Ao encaminhar uma figurinha, não é necessário realizar a busca nem a duplicação

      const sticker = await stickerResource.findOne({
        where: {
          id: data.stickerId,
          accountId: data.accountId,
        },
        include: ['file'],
      })

      if (!sticker?.file) {
        throw new Error('Sticker or file not found')
      }

      const duplicateFile = await fileResource.duplicateFile(sticker.file.id, 'message.file')

      data.fileId = duplicateFile.id
    }

    let fileId = data.fileId

    // Mantendo para retrocompatibilidade com app mas já migrei para o front, agora só vem o fileId final
    if (data.hsmFileId && !data.fileId) {
      const fileHsmInstance = await fileResource.findById(data.hsmFileId, {
        nest: true,
        raw: true,
      })
      const createdHsmFile = await fileResource.getRepository().create({
        ...omit(fileHsmInstance, ['id', 'createdAt', 'updatedAt', 'attachedId']),
        attachedType: 'message.file',
        storage: fileHsmInstance.storage,
      })
      await getStorage(fileHsmInstance.storage)
        .copy(
          `${fileHsmInstance.accountId}/${fileHsmInstance.id}.${fileHsmInstance.extension}`,

          `${createdHsmFile.accountId}/${createdHsmFile.filename}`,
        )
        .catch((error) => {
          logger.log(`Couldn't copy media file from fileId: ${fileHsmInstance?.id}`, 'error')
          reportError(error)
        })
      fileId = createdHsmFile.id
    }

    const sendData = {
      userId: ['user'].includes(data.origin) ? data.userId : ['campaign'].includes(data.origin) ? data.userId : null,
      departmentId: ['campaign'].includes(data.origin) ? data.departmentId : null,
      service,
      serviceId: service.id,
      serviceType: service.type,
      contact,
      contactId: contact.id,
      settings: {
        userNameInMessages: contact.account.settings.userNameInMessages,
        dontSendUserName: data.dontSendUserName,
      },
      message: {
        isChat: type === 'chat',
        type,
        text: data.text,
        ...(quotedMessage && {
          quotedMessageIdFromService: quotedMessage.idFromService,
        }),
        ...(data.vcard && {
          vcard: data.vcard,
        }),
        campaignMessageProgressId: data.campaignMessageProgressId,
        campaignId: data.campaignId,
        fileId,
        filesIds: data.filesIds,
        hsmFileId: data.hsmFileId,
        isPtt: data.file?.isPtt || data.isPtt,
        mentionedList: data.mentionedList || undefined,
      },
      subject: data.subject,
      ccs: data.ccs,
      hsmId: data.hsmId || data.hsm?.id,
      parameters: data.parameters,
      dontOpenTicket: data.dontOpenTicket,
      origin: data.origin || null,
      botId: data.botId || null,
      isFromSurvey: data.isFromSurvey,
      extraOptions: data.extraOptions,
      ...(data.actions && { actions: data.actions }),
      ...(data.mask && { mask: data.mask }),
      fileTemplate: data.fileTemplate,
      hsmParameters: data.hsmParameters,
      interactiveMessage: data.interactiveMessage,
      scheduleId: data.scheduleId,
    }

    let message = await (async () => {
      let createdFile = null

      const file = !isEmpty(data.file)
        ? data.file
        : !isEmpty(data.interactiveMessage)
        ? data.interactiveMessage.file?.id
          ? null
          : data.interactiveMessage.file
        : !isEmpty(data.fileTemplate)
        ? data.fileTemplate
        : null

      // Mantendo para retrocompatibilidade com app mas já migrei para o front, agora só vem o fileId final
      if (data.interactiveMessage?.file?.id && !data.fileId) {
        const fileInstance = await fileResource.findById(data.interactiveMessage?.file?.id || data.fileId, {
          nest: true,
          raw: true,
        })

        if (await mustConvertAudio(fileInstance.mimetype, true, contact.serviceId)) {
          const buffer = await fileResource.getBuffer({
            ...fileInstance,
            filepath: `${fileInstance.accountId}/${fileInstance.id}.${fileInstance.extension}`,
          })
          createdFile = await fileResource.create({
            ...omit(fileInstance, ['id', 'attachedId', 'createdAt', 'updatedAt']),
            data: buffer,
            attachedType: 'message.file',
            accountId: contact.accountId,
            serviceId: contact.serviceId,
            isPtt: true,
          })

          sendData.message.fileId = createdFile.id
        } else {
          createdFile = await fileResource.getRepository().create({
            ...omit(fileInstance, ['id', 'createdAt', 'updatedAt', 'attachedId']),
            attachedType: 'message.file',
            storage: fileInstance.storage,
          })

          await getStorage(fileInstance.storage)
            .copy(
              `${fileInstance.accountId}/${fileInstance.id}.${fileInstance.extension}`,
              `${createdFile.accountId}/${createdFile.filename}`,
            )
            .catch((error) => {
              logger.log(`Couldn't copy media file from fileId: ${fileInstance?.id}`, 'error')
              reportError(error)
            })
          sendData.message.fileId = createdFile.id
        }
      }

      // retrocompatibilidade
      if (file) {
        sendData.message.fileId = (
          await fileResource.create(
            {
              ...parseFile(file),
              accountId: contact.accountId,
              attachedType: 'message.file',
              serviceId: contact.serviceId,
              isPtt: data.isPtt,
            },
            {
              dontEmit: true,
              eventTransaction: options.eventTransaction,
              transaction: options.transaction,
            },
          )
        )?.id

        if (sendData.interactiveMessage?.file?.base64Url) {
          sendData.interactiveMessage.file.base64Url = null
        }

        data.file = null // Remove a referência para GC desalocar da memória
      }

      // retrocompatibilidade
      if (data.attachments?.length) {
        sendData.message.filesIds = await queuedAsyncMap(
          data.attachments,
          async (attachment) => {
            return fileResource
              .create(
                {
                  ...parseFile(attachment),
                  accountId: contact.accountId,
                  attachedType: 'message.file',
                  serviceId: contact.serviceId,
                  isPtt: data.isPtt,
                },
                {
                  dontEmit: true,
                  eventTransaction: options.eventTransaction,
                  transaction: options.transaction,
                },
              )
              .then((r) => r?.id)
          },
          1,
        )

        data.attachments = null // Remove a referência para GC desalocar da memória
      }

      // retrocompatibilidade
      if (data.isComment || type === 'comment') {
        const createdMessage = await this.create(
          {
            text: data.text,
            type:
              (sendData.message.fileId &&
                getTypeByMimetype((createdFile || (await fileResource.findById(sendData.message.fileId)))?.mimetype)) ||
              'chat',
            isComment: true,
            isFromMe: true,
            timestamp: new Date(),
            contactId: data.contactId,
            serviceId: service.id,
            accountId: data.accountId,
            userId: data.userId,
          },
          { ...options, dontEmit: true },
        )

        if (sendData.message.fileId) {
          await fileResource.attachFileToMessage(
            {
              accountId: data.accountId,
              attachedId: createdMessage.id,
              fileId: sendData.message.fileId,
            },
            {
              transaction: options.transaction,
              eventTransaction: options.eventTransaction,
            },
          )

          const reloadedMessage = await this.findById(createdMessage.id, {
            include: ['file', 'files'],
            transaction: options.transaction,
          })

          this.emitCreated(reloadedMessage)
          return reloadedMessage
        }

        this.emitCreated(createdMessage)
        return createdMessage
      }

      return timeout(driverService.sendAndSave(service.id, sendData, { ...options }), 2 * 60 * 1000)
    })()

    // removing survey when an user sends a message
    if ((data.origin === 'user' || data.origin === 'schedule') && contact.data.survey) {
      await answersResource.updateAnswerForContact(contact, '-1')
      await answersResource.unflagSurveyFromContact(contact)
    }

    if (message) {
      if (data.origin === 'user') {
        this.emitUserMessageSent(message, options.eventTransaction)
      }

      if (options.include) {
        message = await this.reload(message, options)
      }

      if (type === 'sticker') {
        stickerUserResource
          .createOrUpdate(data.stickerId, message.userId, message.accountId)
          .then((result) => result)
          .catch((error) => logger.log(`Error to create or update sticker user: ${error}`, 'error'))
      }

      logger.logEvent('msg_send_success', { messageId: message.id })
    }

    return message
  }

  async create(
    data: {
      contact?: ContactInstance
      account?: AccountInstance
      text?: string
      type?: string
      origin?: string
      sent?: boolean
      visible?: boolean
      isFromMe?: boolean
      timestamp?: Date
      previewUrl?: string
      previewMimetype?: string
      filename?: string
      ticketId?: string
      contactId: string
      serviceId: string
      userId?: string
      departmentId?: string
      accountId: string
      isFromSurvey?: boolean
      data?: MessageInstance['data']
      idFromService?: string
      fromId?: string
      hsmId?: string
      quotedMessageId?: string
      toId?: string
      isComment?: boolean
      campaignMessageProgressId?: string
      campaignId?: string
      isTranscribing?: boolean
      mentionedList?: string[]
      ticketDepartmentId?: string
    },
    options: {
      dontEmit?: boolean
      transaction?: any
      eventTransaction?: EventTransaction
    } = {},
  ): Promise<MessageInstance> {
    const creditsControlCacheService = Container.get(CreditsControlCacheService)

    return this.maybeEventTransaction(async (eventTransaction) => {
      const { userId, departmentId, type, origin } = data

      const { transaction } = options

      if (type === 'sticker') {
        // Ao criar figurinha, sempre manter o texto vazio
        // Exemplo: ao enviar por encaminhamento, o texto era preenchido com um espaço em branco
        data.text = ''
      }

      const contact =
        data.contact ||
        (await contactResource.findById(data.contactId, {
          include: ['account'],
          transaction,
        }))

      if (data.isFromMe && contact.block) return

      if (!contact) {
        console.trace('No contact instance given, had to fetch.')
      }

      contact.account = contact.account || data.account

      const account =
        data.account ||
        (contact && contact.account) ||
        (contact &&
          data.text &&
          (await contact.getAccount({
            transaction,
          })))

      const service = contact.service || (await serviceResource.findById(data.serviceId))

      if (contact && data.text && !contact.account) {
        console.trace('Contact came without account, had to fetch.')
      }

      if (data.text && !account) {
        console.trace('No account given.')
      }

      if (!data.timestamp) {
        throw new Error('Not timestamp given.')
      }

      // Verifica bloqueio por DDD
      const isBlockedToSendMessage = await this.verifyBlockMessageRule(data, contact.id)
      if (isBlockedToSendMessage && data.isFromMe) {
        // Envio de mensagem e o chat está bloqueado, realiza o bloqueio
        throw new Error('Contact blocked to send message by rule')
      }

      // verifica controle de créditos
      const { mustConsumeCredits, insufficientCredits } = await this.verifyMessageConsumeCredits(
        data.accountId,
        data,
        service?.type as ServiceType,
        data.data?.isNew,
        data.isFromMe,
      )

      if (mustConsumeCredits && insufficientCredits) {
        throw new Error('Insufficient credits to send this message.')
      }

      let surveyExpired = false
      if (contact.data.survey) {
        surveyExpired = isBefore(new Date(), new Date(contact.data.survey.expiresAt))
      }

      const message = await messageRepository.create(
        {
          ...data,
          userId,
          data: {
            ...data.data,
            ...(!!contact.data.survey && {
              isSurveyResponse: true,
              surveyExpired,
            }),
          },
          contactId: contact.id,
          text: data.text && encryptTextForAccount(account, data.text),
        },
        {
          transaction,
        },
      )

      const links = getLinksFromText(data.text)

      if (links.length) {
        const linksData = links.map(() => {
          return {
            messageId: message.id,
          }
        })
        await linkRepository.bulkCreate(linksData, { transaction })
      }

      if (data.previewUrl) {
        // Função anônima para evitar transaction e idle in transaction
        ;(async () =>
          fileResource.create(
            {
              name: data.filename,
              base64Url: data.previewUrl,
              mimetype: data.previewMimetype,
              attachedId: message.id,
              attachedType: 'message.preview',
              accountId: message.accountId,
            },
            { eventTransaction },
          ))()
      }

      const transferOptions =
        type === 'schedule' || (origin === 'campaign' && departmentId)
          ? {
              departmentId,
              userId,
            }
          : null

      const messageTicket = await ticketService.handleMessageCreated(
        message,
        {
          transaction,
          eventTransaction,
        },
        transferOptions,
      )

      if (origin === 'campaign') {
        await campaignMessageProgressResource.updateById(
          data.campaignMessageProgressId,
          {
            messageId: message.id,
            sentAt: new Date(),
          },
          {
            transaction,
            eventTransaction,
            dontEmit: true,
          },
        )
      }

      if (!options.dontEmit) {
        if (messageTicket) {
          message.ticketId = messageTicket.ticket.id
          message.ticketDepartmentId = messageTicket.ticket.departmentId
        }

        this.emitCreated(message, eventTransaction)
      }

      if (mustConsumeCredits) {
        await creditsControlCacheService.pushMessage(data.accountId, service.type as ServiceType, {
          messageId: message.id,
          serviceId: service.id,
        })
      }

      if (isBlockedToSendMessage && !message.isFromMe) {
        // Recebimento de mensagem e o chat está bloqueado, realiza o desbloqueio
        await blockMessageRuleResource.unblockContactUntilNextWorkPlan(message.contactId)
      }

      logger.logEvent('msg_created_success', { messageId: message.id })

      if (!message.isFromMe && !message.origin) {
        logger.logEvent('msg_received_success', { messageId: message.id })
      }

      return message
    }, options.eventTransaction).catch((error) => {
      //TODO tratar cada falha no recebimento em cada adapter
      if (!data.isFromMe && !data.origin) {
        logger.logEvent('msg_received_failed', error, 'error')
      }
      logger.logEvent('msg_created_fail', error, 'error')
      throw error
    })
  }

  async update(model, data, options = {}) {
    try {
      const account = model.account || (await model.getAccount({ transaction: options?.transaction }))
      const updatedData = data.data

      const newData = {
        ...data,
        ...(data.text && {
          text: encryptTextForAccount(account, data.text),
        }),
      }

      if (updatedData?.ack && updatedData?.ack !== model.data?.ack) {
        const { eventName, status, reason, level } = getDataMessage(updatedData)

        logger.logEvent(eventName, { messageId: model.id, status, ack: updatedData?.ack }, reason, level)
      }

      const updatedMessage = await super.update(model, newData, { ...options, dontEmit: true })

      if (!options?.dontEmit) {
        if (!['chat', 'text', 'vcard'].includes(model.type) && !model.file) {
          const fileAsoc = await updatedMessage.getFile({ transaction: options?.transaction })
          updatedMessage.setDataValue('file', fileAsoc)
        }

        this.emitUpdated(updatedMessage, options?.eventTransaction)
      }

      logger.logEvent('msg_update_success', { messageId: model.id })

      return updatedMessage
    } catch (error) {
      logger.logEvent('msg_update_failed', { messageId: model.id }, error, 'error')
      throw error
    }
  }

  destroy(model, options = {}) {
    return this.getRepository()
      .transaction(async (transaction) => {
        await fileResource.destroyMany(
          {
            where: {
              attachedType: {
                $in: ['message.file', 'message.preview', 'message.thumbnail'],
              },
              attachedId: model.id,
            },
          },
          { transaction, ...options },
        )

        const destroyedMessage = super.destroy(model, { transaction, ...options })

        logger.logEvent('msg_deleted_success', { messageId: model.id })

        return destroyedMessage
      })
      .catch((error) => {
        logger.logEvent('msg_deleted_failed', { messageId: model.id }, error, 'error')
        throw error
      })
  }

  async createTicketMessage(data, options) {
    return this.create(
      {
        timestamp: new Date(),
        type: 'ticket',
        origin: 'ticket',
        ...data,
      },
      options,
    )
  }

  async createTicketOpenMessage(data, options) {
    return this.createTicketMessage(
      {
        data: { ticketOpen: true },
        ...data,
      },
      options,
    )
  }

  async createTicketCloseMessage(data, options) {
    return this.createTicketMessage(
      {
        data: { ticketClose: true },
        ...data,
      },
      options,
    )
  }

  async createTicketTransferMessage(data, options = {}) {
    return this.createTicketMessage(
      {
        data: { ticketTransfer: true },
        ...data,
      },
      options,
    )
  }

  async decryptMessageText(message: MessageInstance): Promise<string> {
    // @ts-ignore
    const account = message.account || (await message.getAccount())

    return decryptTextForAccount(account, message.text)
  }

  revoke(message) {
    return driverService.revokeMessageById(message.serviceId, message.id)
  }

  async sendReactionByMessageId(
    messageId: string,
    accountId: string,
    reactionEmojiRendered: string,
    reactionCode?: string,
  ) {
    const message = await this.findById(messageId, {
      where: {
        accountId,
      },
      include: ['service'],
    })

    if (!message) {
      throw new Error(`Message (${messageId}) not found.`)
    }

    if (!serviceTypesAllowedToSendReactions.includes(message.service?.type)) {
      throw new Error(`The service type (${message.service?.type}) is not allowed to send reactions.`)
    }

    if (!message.service?.settings?.reactionsEnabled) {
      throw new Error(`The service (${message.service?.type}:${message.service?.id}) is not enabled to send reactions.`)
    }

    if (messageTypesNotAllowedToSendReactions.includes(message.type)) {
      throw new Error(`The message type (${message.type}) is not allowed to send reactions.`)
    }

    if (message.isComment) {
      throw new Error('Comment message is not allowed to send reactions.')
    }

    if (message.data?.softRevoked) {
      throw new Error('Deleted message is not allowed to send reactions.')
    }

    if (!message.idFromService) {
      throw new Error('The reaction has no ID from service.')
    }

    if (message.service?.type === 'whatsapp-business') {
      if (!serviceProviderTypeEnabledReactions.includes(message.service.data?.providerType)) {
        throw new Error(`The providerType (${message.service.data?.providerType}) cannot send reactions.`)
      }

      if (differenceInDays(new Date(), message.timestamp) >= 30) {
        throw new Error('The message is more than 30 days old')
      }

      if (message.isFromMe && serviceProviderTypeBlockReactionOwnMessage.includes(message.service.data?.providerType)) {
        throw new Error('Cannot react on own message')
      }
    }

    if (configValues.workersGoServices.includes(message.service.type)) {
      await Container.get(HttpJobsDispatcher).dispatch(
        'send-reaction',
        {
          messageId: message.id,
          reactionEmojiRendered: reactionEmojiRendered,
          reactionCode: reactionCode,
        },
        {
          timeout: 2 * 60_000,
          useWorkersGo: true,
        },
      )
      return true
    }

    await driverService.sendReactionByMessage(message.serviceId, message.id, reactionEmojiRendered, reactionCode)

    return true
  }

  async revokeReactionByMessageId(messageId: string, accountId: string) {
    const message = await this.findById(messageId, {
      where: {
        accountId,
      },
      include: ['service', 'reactionParentMessage'],
    })

    if (!message) {
      throw new Error(`Message (${messageId}) not found.`)
    }

    if (!serviceTypesAllowedToSendReactions.includes(message.service?.type)) {
      throw new Error(`The service type (${message.service?.type}) is not allowed to revoke reactions.`)
    }

    if (!message.service?.settings?.reactionsEnabled) {
      throw new Error(
        `The service (${message.service?.type}:${message.service?.id}) is not enabled to revoke reactions.`,
      )
    }

    if (message.type !== 'reaction') {
      throw new Error(`The message type (${message.type}) is not allowed to revoke reactions.`)
    }

    if (!message.isFromMe) {
      throw new Error('You can only revoke your reactions.')
    }

    if (!message.reactionParentMessageId || !message.reactionParentMessage) {
      throw new Error('The reaction does not have parent message.')
    }

    if (message.reactionParentMessage.data?.softRevoked) {
      throw new Error('Deleted parent message is not allowed to revoke reactions.')
    }

    if (!message.idFromService || !message.reactionParentMessage.idFromService) {
      throw new Error('The reaction or the parent message has no ID from service.')
    }

    if (message.service?.type === 'whatsapp-business') {
      if (!serviceProviderTypeEnabledReactions.includes(message.service.data?.providerType)) {
        throw new Error(`The providerType (${message.service.data?.providerType}) cannot revoke reactions.`)
      }

      if (differenceInDays(new Date(), message.reactionParentMessage.timestamp) >= 30) {
        throw new Error('The message is more than 30 days old')
      }

      if (
        message.reactionParentMessage.isFromMe &&
        serviceProviderTypeBlockReactionOwnMessage.includes(message.service.data?.providerType)
      ) {
        throw new Error('Cannot revoke react on own message')
      }
    }

    if (configValues.workersGoServices.includes(message.service.type)) {
      await Container.get(HttpJobsDispatcher).dispatch(
        'revoke-reaction',
        {
          messageId: message.id,
        },
        {
          timeout: 2 * 60_000,
          useWorkersGo: true,
        },
      )
      return true
    }

    await driverService.revokeReactionByMessage(message.serviceId, message.id)

    return true
  }

  syncFile(message) {
    return driverService.syncMessageFileById(message.serviceId, message.id)
  }

  userHasPermissionToSendStickers(userPermissions) {
    return hasPermission(userPermissions, 'stickers.send')
  }

  async userHasPermissionToOpenTickets(userPermissions) {
    return (
      hasPermission(userPermissions, 'messages.view.ticket.mine.current') ||
      hasPermission(userPermissions, 'messages.view.ticket.department.current') ||
      hasPermission(userPermissions, 'messages.view.out.queue')
    )
  }

  async userHasPermissionToTicketTransfer(userPermissions) {
    return (
      hasPermission(userPermissions, 'messages.view.ticket.mine') ||
      hasPermission(userPermissions, 'messages.view.ticket.department')
    )
  }

  includeTicketTransfer = async (messages: any, context: any): Promise<MessageInstance[]> => {
    messages = Array.isArray(messages) ? messages : [messages]
    let transfers = []
    let userHasPermission = await this.userHasPermissionToTicketTransfer(context.user.permissions)
    if (userHasPermission) {
      const ticketIds = messages.reduce((result: string[], message: MessageInstance) => {
        if (message.ticketId) {
          result.push(message.ticketId)
        }
        return result
      }, [])

      if (ticketIds.length > 0) {
        transfers = await ticketTransfersResource.getTicketTransfer(ticketIds)
      }
    }

    userHasPermission = await this.userHasPermissionToOpenTickets(context.user.permissions)
    const openTickets = userHasPermission
      ? await contactResource.getOpenTicketsByContactsIds(
          (messages.data || messages).map((m) => m.contactId),
          context.account.id,
        )
      : []
    context = { ...context, openTickets }

    const response = []
    await messages.reduce(async (promise, message: MessageInstance) => {
      if (message) {
        await promise
        const ticketTransfers = transfers.find((transfer) => transfer.ticketId === message.ticketId)
        message.transferUserIds = ticketTransfers?.transferUserIds || []
        message.transferDepartmentIds = ticketTransfers?.transferDepartmentIds || []
        response.push(await messageTransformer(message, { ...context, openTickets }))
      }
    }, Promise.resolve())
    return response
  }

  /**
   * Geração do histórico em pdf
   *
   * @param {string} ticketId Id do ticket
   * @param {object} context Contexto
   *
   * @returns {string} URL do arquivo
   */
  async generatePDF(ticketId: string, context: any) {
    const messages = await messageRepository.findMany({
      includeTicketTransfer: true,
      where: {
        ticketId: ticketId,
      },
      include: [
        'file',
        'files',
        'preview',
        'thumbnail',
        'ticket.department',
        'hsmFile',
        'hsm',
        {
          model: 'user',
          attributes: ['id', 'name'],
        },
        {
          model: 'ticketTransfer',
          include: ['fromUser', 'toUser', 'fromDepartment', 'toDepartment', 'byUser'],
        },
        {
          model: 'from',
          attributes: ['id', 'name', 'alternativeName', 'internalName'],
        },
        {
          model: 'contact',
          attributes: ['id'],
        },
        {
          model: 'quotedMessage',
          include: ['file', 'files', 'preview', 'thumbnail', 'user', 'contact', 'from', 'hsmFile', 'hsm'],
        },
      ],
      order: [
        ['timestamp', 'ASC'],
        ['id', 'ASC'],
      ],
    })

    let html = ''
    messages.map(async (message) => {
      if (message.text) {
        html += `<div>${await messageTransformer(message, context).text}</div>`
      }
    })

    const pdfBuffer = await generatePdf(html, 'teste', 'teste')
    const baseUrl = 'history'

    const file = await getStorage().write(`${baseUrl}/${ticketId}.pdf`, pdfBuffer, {})

    const oracleParams = {
      Key: file.Key,
    }

    const uploadFile = await getStorage().getSignedUrl('getObject', oracleParams)

    return uploadFile
  }

  async getMessagesForSummary(ticket: TicketInstance) {
    const contact = await contactResource.findOne({
      where: { id: ticket.contactId },
    })

    const messages = await this.findMany({
      where: {
        ...(contact.data?.botFinishedAt !== null &&
          contact.data?.botFinishedAt !== undefined && {
            createdAt: {
              $gte: contact.data.botFinishedAt,
            },
          }),
        ticketId: ticket.id,
        type: {
          $in: ['chat', 'audio', 'ptt'],
        },
        $or: [{ origin: { $ne: 'bot' } }, { origin: null }],
      },
      include: ['account', 'file'],
      order: ['createdAt'],
    })

    return messages
  }

  async summarize(id: string) {
    const smartSummaryService = Container.get(SmartSummaryService)
    const summary = await summaryResource.findOne({
      where: { messageId: id },
      include: [{ model: 'message', include: ['ticket', 'contact'] }],
    })
    if (summary && !(await smartSummaryService.limitReached(summary?.accountId, false))) {
      await smartSummaryService.start(
        summary?.message?.ticket,
        summary?.eventType,
        summary?.message?.contact,
        null,
        summary,
      )
    }
  }

  async magicText(message: string, tone: string, account: AccountInstance, serviceId: string, userId: string) {
    const magicTextService = Container.get(MagicTextService)

    try {
      const getPrompt = () => {
        if (tone == 'correct') {
          return `Corrija a ortografia, não traduzir o texto, não inclua o prompt na resposta.`
        }
        return `Corrija a ortografia, não traduzir o texto, rescreva a frase para que tenha um tom ${tone}. Não inclua o prompt na resposta.\n`
      }

      return await magicTextService.transformer(message, getPrompt(), account?.id, serviceId, userId)
    } catch (error) {
      const errorMessage = 'Failed to generate magic text: ' + error.message
      logger.log(errorMessage, 'error')
      return errorMessage
    }
  }

  async outOfRange(messageId: string, range: string) {
    const messageExist = await sequelize.query(`SELECT id from messages WHERE id = '${messageId}'`)

    if (!messageExist[0].length) throw new BadRequestHttpError(`Message not exists`)

    const outOfRangeMessage = await sequelize.query(`
      WITH baseMessage AS (
          SELECT "timestamp", "contactId"
          FROM messages
          WHERE id = '${messageId}'
      )
      SELECT
          CASE
              WHEN COUNT(m.id) >= ${range} THEN true
              ELSE false
          END AS hasmorethan${range}
      FROM messages m
      WHERE m."contactId" = (SELECT "contactId" FROM baseMessage)
          AND m."timestamp" > (SELECT "timestamp" FROM baseMessage);
    `)

    return { outOfRange: outOfRangeMessage?.[0][0][`hasmorethan${range}`] }
  }

  async hasSummaryMessage(ticketId: string): Promisse<boolean> {
    const count = await messageRepository.count({
      where: {
        ticketId,
        type: 'summary',
      },
    })

    return count > 0
  }

  /**
   * Busca a mensagem anterior do mesmo ticket que tenha ticketDepartmentId preenchido
   * @param {MessageInstance} message
   * @returns {Promise<MessageInstance|null>}
   */
  async findPreviousWithDepartmentId(message: MessageInstance): Promise<MessageInstance | null> {
    return await this.findOne({
      where: {
        ticketId: message.ticketId,
        createdAt: { $lt: message.createdAt },
        ticketDepartmentId: { $ne: null },
      },
      order: [['createdAt', 'DESC']],
    })
  }
}

export default new MessageResource()
