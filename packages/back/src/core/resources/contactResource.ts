/* eslint-disable no-param-reassign,global-require */
import { Container } from 'typedi'
import { groupBy, omit, uniq, pickBy, pick } from 'lodash'
import { Op, Transaction } from 'sequelize'
import sequelize from '../services/db/sequelize'
import { formatIdFromNumber, idToNumber, parseNumber } from '../utils/whatsapp/numberParser'
import BaseResource, { CREATED, DESTROYED, UPDATED, EventTransaction, Options, PaginatedOptions } from './BaseResource'
import contactRepository from '../dbSequelize/repositories/contactRepository'
import serviceResource from './serviceResource'
import messageResource from './messageResource'
import scheduleResource from './scheduleResource'
import tagRepository from '../dbSequelize/repositories/tagRepository'
import fileResource from './fileResource'
import { checksum } from '../utils/crypt/checksum'
import isDifferent from '../utils/isDifferent'
import BadRequestHttpError from '../utils/error/BadRequestHttpError'
import NotFoundHttpError from '../utils/error/NotFoundHttpError'
import ticketService from '../services/ticket/ticketService'
import ticketResource from './ticketResource'
import { ContactInstance, ContactOrigin } from '../dbSequelize/models/Contact'
import { MessageInstance } from '../../core/dbSequelize/models/Message'
import driverService, { getServiceRpc } from '../services/driverService'
import personResource from './personResource'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import { getCloseTicketQueue } from '../queues/tickets'
import ticketTransfersResource from './ticketTransfersResource'
import contactTransformer from '../transformers/contactTransformer'
import hasPermission from '../utils/hasPermission'
import iteratePaginated from '../utils/iteratePaginated'
import comparableIdFromService from '../utils/whatsapp/comparableIdFromService'
import reportError from '../services/logs/reportError'
import configValues from '../configValues'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'
import messageRepository from '../dbSequelize/repositories/messageRepository'
import fileTransformer from '../transformers/fileTransformer'
import messageTransformer from '../transformers/messageTransformer'
import { MessageTypeEnum } from '../enums/messageTypes.enum'
import RedisCacheStorage from '../../core/services/cache/RedisCacheStorage'
import ticketRepository from '../../core/dbSequelize/repositories/ticketRepository'
import getLinksFromText from '../../core/utils/getLinksFromText'
import SmartSummaryService from '../services/SmartSummary/SmartSummaryService'

const redisCache = Container.get(RedisCacheStorage)
const keyMarkForNoBan = (accountId, ticketId) => `mark-for-no-ban:${accountId}:${ticketId}`
const MARK_FOR_NO_BAN_TTL = 15 * 60 * 1000 //15 minutos

export const FIRST_INTERACTION = 'first-interaction'
export const FIRST_SYNC_BATCH = 'first-sync-batch'
export const INIT_CHAT_WEBCHAT = 'init-chat-webchat'
export const ANONYMOUS_USER_CREATED_WEBCHAT = 'anonymous-user-created-webchat'

const DEFAULT_ORDER = 'createdAt'
const DEFAULT_ORDER_DIRECTION = 'desc'
const DEFAULT_MEDIA_TYPE = 'media'
const DEFAULT_PERPAGE = 50

export interface IFindMediaByContactInput {
  cursor?: string
  name?: string
  perPage?: number
  type?: string
  order?: Array<Array<string>>
}

const findOrCreateTag = async (accountId: string, label: string, options = {}) =>
  tagRepository.findOrCreate({
    ...options,
    where: { accountId, label },
    defaults: { accountId, label },
  })

const handleTagsRelations = async (
  contact: ContactInstance,
  data: {
    accountId: string
    tags?: { label: string }[]
    tagIds?: string[]
    mergeRelations?: boolean
  },
  options = {},
) => {
  const tags = await (() => {
    if (data.tags && Array.isArray(data.tags)) {
      return Promise.all(data.tags.map((tag) => findOrCreateTag(data.accountId, tag.label)))
    }

    if (data.tagIds && Array.isArray(data.tagIds)) {
      return tagRepository.findMany({
        where: { id: { $in: data.tagIds } },
      })
    }
  })()

  if (!tags) return null

  return data.mergeRelations ? contact.addTags(tags, options) : contact.setTags(tags, options)
}

export const checkIfChanged = (contact: ContactInstance, data) =>
  isDifferent(contact, data, [
    'name',
    'internalName',
    'alternativeName',
    'visible',
    'hadChat',
    'lastMessageId',
    'unread',
    'info',
    'note',
    [
      'avatar',
      () => {
        if (!contact.avatar && data.avatar) return true
        if (!data.avatar) return false

        return contact.avatar?.checksum !== checksum(data.avatar?.data || '')
      },
    ],
    [
      'thumbAvatar',
      () => {
        if (!contact.thumbAvatar && data.thumbAvatar) return true
        if (!data.thumbAvatar) return false

        return contact.thumbAvatar?.checksum !== checksum(data.thumbAvatar?.data || '')
      },
    ],
  ])

export class ContactResource extends BaseResource<ContactInstance> {
  constructor() {
    super(contactRepository)

    this.events = [
      CREATED,
      UPDATED,
      DESTROYED,
      FIRST_SYNC_BATCH,
      FIRST_INTERACTION,
      INIT_CHAT_WEBCHAT,
      ANONYMOUS_USER_CREATED_WEBCHAT,
    ]
  }

  emitFirstSyncBatch(data, eventTransaction) {
    return this.emit(FIRST_SYNC_BATCH, data, eventTransaction)
  }

  async parseAndValidateIdFromService(data: { idFromService?: string; number?: string; serviceId?: string }) {
    const rawIdFromService = data.idFromService || formatIdFromNumber(parseNumber(data.number))

    const service = await serviceResource.findById(data.serviceId, {
      cache: true,
    })

    const validIdFromService = configValues.workersGoServices.includes(service.type)
      ? await Container.get(HttpJobsDispatcher).dispatch(
          'get-valid-contact-id',
          {
            serviceId: data.serviceId,
            contactId: rawIdFromService,
          },
          {
            timeout: 2 * 60_000,
            useWorkersGo: true,
          },
        )
      : await driverService.getValidId(data.serviceId, rawIdFromService)

    const idFromService = validIdFromService || rawIdFromService

    const number = idToNumber(idFromService)

    return {
      idFromService,
      valid: !!validIdFromService,
      number,
    }
  }

  // eslint-disable-next-line complexity
  async create(
    data: {
      idFromService?: string
      unsubscribed?: boolean
      name: string
      internalName?: string
      alternativeName?: string
      isGroup?: boolean
      isMe?: boolean
      number?: string
      tagIds?: string[]
      data?: {
        answered?: boolean
        hugmeStatus?: number
        raStatus?: number
        info?: any
        isOriginal?: boolean
        number?: string
        validNumber?: string
        webchat?: {
          userId: string
          roomId: string
          originAccessURL?: string
          followUpToken?: string
          foolowUpRedirectLink?: string
          contactUnread?: number
        }
        email?: string
      }
      status?: string
      visible?: boolean
      defaultDepartmentId?: string
      defaultUserId?: string
      serviceId: string
      personId?: string
      accountId: string
      mergeRelations?: boolean
      note?: string
      origin?: ContactOrigin
    },
    options: {
      dontEmit?: boolean
      include?: any
      eventTransaction?: EventTransaction
      skipValidation?: boolean
      transaction?: any
    } = {},
  ) {
    const service = await serviceResource.findById(data?.serviceId, {
      attributes: ['id', 'type'],
      cache: true,
    })

    if (!service) throw new BadRequestHttpError(`Invalid service #${data?.serviceId}.`)

    const email = service.type === 'email' && data.data?.email?.includes('@') ? String(data.data?.email).trim() : ''
    const number = parseNumber(data.number || data.data?.number) || ''

    const rawIdFromService = data.idFromService || number || email

    const validationResult =
      (!options.skipValidation || !number) && service.type === 'whatsapp'
        ? await this.parseAndValidateIdFromService({
            number,
            idFromService: data.idFromService,
            serviceId: data.serviceId,
          })
        : null

    const idFromService = validationResult?.idFromService || rawIdFromService

    const contactBlockListControl =
      ['whatsapp', 'whatsapp-business', 'sms-wavy'].includes(service.type) &&
      idFromService &&
      (await contactRepository.numberIsBlockedBySomeBlocklist(idFromService, data.serviceId))

    const contactData = {
      ...data,
      idFromService,
      data: {
        ...pickBy(data.data, (attr) => !!attr || attr === false),
        ...pickBy(
          {
            number: validationResult?.number || number,
            valid: validationResult?.valid,
            email,
          },
          (attr) => !!attr || attr === false,
        ),
        ...(data.data?.webchat && {
          webchat: {
            ...pick(data.data?.webchat, [
              'userId',
              'roomId',
              'originAccessURL',
              'followUpToken',
              'folowUpRedirectLink',
              'contactUnread',
            ]),
          },
        }),
      },
      ...(!!contactBlockListControl && {
        block: !!contactBlockListControl,
        dataBlock: {
          byUserId: contactBlockListControl.userId,
          description: contactBlockListControl.reason,
          date: new Date(),
          level: 1,
        },
        contactBlockListControlId: contactBlockListControl.id,
      }),
    }

    let contact = await super
      .create(contactData, {
        ...options,
        dontEmit: true,
      })
      .catch(async (e) => {
        if (
          e.message?.includes(
            'duplicate key value violates unique constraint "contacts_idfromservice_without_nine_idx"',
          )
        ) {
          const contact = await this.findOne({
            where: {
              accountId: contactData.accountId,
              serviceId: contactData.serviceId,
              idFromService: comparableIdFromService(contactData.idFromService, service.type),
              archivedAt: null,
            },
          })

          if (contact) return contact
        }

        throw e
      })

    await handleTagsRelations(contact, data)

    contact = await this.attachToOtherContactsOfTheSameNumber(contact, options)

    if (options.include) {
      contact = await super.reload(contact, options)
    }

    if (!options.dontEmit) this.emitCreated(contact, options.eventTransaction)

    return contact
  }

  async update(
    model: ContactInstance,
    data,
    options: {
      dontEmit?: boolean
      include?: any
      transaction?: any
      eventTransaction?: EventTransaction
      mergeJson?: string[]
    } = {},
  ) {
    return this.nestedTransaction(async (transaction) => {
      await handleTagsRelations(model, data, { ...options, transaction })

      // @ts-ignore
      model.changed('updatedAt', true)

      const oldPersonId = model.personId

      let contact = await super.update(model, data, {
        dontEmit: true,
        ...options,
        transaction,
      })

      if (!oldPersonId && data.personId) {
        contact = await this.attachToOtherContactsOfTheSameNumber(contact, { ...options, transaction })
      }

      if (options.include) {
        contact = await super.reload(contact, { ...options, transaction })
      }

      if (!options.dontEmit) this.emitUpdated(contact, options.eventTransaction)

      return contact
    }, options.transaction)
  }

  async bulkDestroy(options = {}) {
    const contacts = await this.findMany({
      ...options,
      attributes: ['id'],
    })
    const ids = contacts.map((contact) => contact.id)
    await scheduleResource.bulkDestroy({
      ...options,
      where: {
        contactId: { $in: ids },
        status: 'scheduled',
      },
    })
    await super.bulkDestroy(options)
  }

  async updateContactList(params: ContactInstance) {
    const { defaultDepartmentId, defaultUserId, selectedIds, accountId, allContactsSelected = false, filters } = params
    const ids = Object.keys(selectedIds).filter((item) => selectedIds[item])
    const fields = { defaultDepartmentId, defaultUserId }

    if (!allContactsSelected) {
      this.bulkUpdate(fields, {
        where: {
          id: { $in: ids },
          accountId,
        },
      })

      return true
    }

    iteratePaginated(
      ({ page }) =>
        this.findManyPaginated({
          attributes: ['id'],
          page,
          perPage: 200,
          raw: true,
          where: {
            ...filters?.where,
            accountId,
          },
          order: [['id', 'ASC']],
          ...(filters?.customFilter && { customFilter: filters.customFilter }),
        }),
      null,
      100,
      false,
      async (chunk) => {
        await this.bulkUpdate(fields, {
          where: {
            id: { $in: chunk.map((contact) => contact.id) },
            accountId,
          },
        })
      },
    )

    return true
  }

  async internalCreate(data, options: { dontEmit?: boolean; eventTransaction?: EventTransaction } = {}) {
    const rawNumber = data?.number || data?.data?.number

    const contactBlockListControl = await contactRepository.numberIsBlockedBySomeBlocklist(rawNumber, data.serviceId)

    const contact = await contactRepository.create({
      ...data,
      ...(!!contactBlockListControl && {
        block: !!contactBlockListControl,
        dataBlock: {
          byUserId: contactBlockListControl.userId,
          description: contactBlockListControl.reason,
          date: new Date(),
          level: 1,
        },
        contactBlockListControlId: contactBlockListControl.id,
      }),
    })

    const service = await serviceResource.findById(contact.serviceId, {
      attributes: ['id', 'type'],
      cache: true,
    })

    if (contact.isGroup && service.type === 'whatsapp') {
      const whatsappService = await getServiceRpc('whatsapp-remote')

      await whatsappService.syncGroupParticipants(contact.serviceId, contact.id)
      await whatsappService.syncGroupById(contact.serviceId, contact.id)
    }

    if (contact && data.avatar) {
      await fileResource.create({
        data: data.avatar.data,
        mimetype: data.avatar.mimetype,
        attachedId: contact.id,
        attachedType: 'contact',
        accountId: contact.accountId,
        ...options,
      })
    }

    if (contact && data.thumbAvatar) {
      await fileResource.create({
        data: data.thumbAvatar.data,
        mimetype: data.thumbAvatar.mimetype,
        attachedId: contact.id,
        attachedType: 'contact.thumbnail',
        accountId: contact.accountId,
        ...options,
      })
    }

    if (!options.dontEmit) this.emitCreated(contact, options.eventTransaction)

    return contact
  }

  async internalUpdate(
    model: ContactInstance,
    data,
    options: {
      ignoreNameUpdate?: boolean
      onlyIfChanged?: boolean
    } & Options<ContactInstance> = {},
  ) {
    return this.getRepository().transaction(async (transaction) => {
      const { eventTransaction, mergeJson } = options

      model.avatar = model.avatar || (await model.getAvatar({ transaction }))
      model.thumbAvatar = model.thumbAvatar || (await model.getThumbAvatar({ transaction }))

      if (data.lastMessage) {
        data.lastMessageId = data.lastMessageId || data.lastMessage.id
        data.lastMessageAt = data.lastMessageAt || new Date()
      }

      if (options.ignoreNameUpdate && model.name) {
        data = omit(data, ['name', 'alternativeName'])
      }

      if (options.onlyIfChanged) {
        const changed = checkIfChanged(model, data)
        if (!changed) return null

        //         log(
        //           `Contact #${model.id} is updating because "${changed}" changed. \
        // Before "${model[changed]}". After "${data[changed]}"`,
        //         )
      }

      const firstMessageNotFromMe =
        !model.lastMessageId && !!data.lastMessage && !data.lastMessage.isFromMe && data.lastMessage.data.isNew

      const contact = await contactRepository.update(
        model,
        omit(
          {
            ...data,
            isBroadcast: data?.isBroadcast ?? false,
            hadChat: typeof data.hadChat !== 'boolean' ? model.hadChat : data.hadChat,
            visible: typeof data.visible !== 'boolean' ? model.visible : data.visible,
          },
          ['idFromService', 'tags', 'avatar', 'thumbAvatar'],
        ),
        { transaction, mergeJson },
      )

      const createAvatar = (file, type) =>
        fileResource
          .create(
            {
              data: file.data,
              mimetype: file.mimetype,
              attachedId: contact.id,
              attachedType: type,
              accountId: contact.accountId,
            },
            { eventTransaction },
          )
          .catch(reportError)

      if (!data.destroyAvatar) {
        if (model.avatar && data.avatar && model.avatar.checksum !== checksum(data.avatar?.data || '')) {
          await fileResource.destroy(model.avatar, { transaction })
          createAvatar(data.avatar, 'contact')
        } else if (!model?.avatar && data.avatar) {
          createAvatar(data.avatar, 'contact')
        }

        if (
          model.thumbAvatar &&
          data.thumbAvatar &&
          model.thumbAvatar.checksum !== checksum(data.thumbAvatar?.data || '')
        ) {
          await fileResource.destroy(model.thumbAvatar, { transaction })
          createAvatar(data.thumbAvatar, 'contact.thumbnail')
        } else if (!model?.thumbAvatar && data.thumbAvatar) {
          createAvatar(data.thumbAvatar, 'contact.thumbnail')
        }
      } else {
        await fileResource.destroy(model.avatar, { transaction })
        await fileResource.destroy(model.thumbAvatar, { transaction })
      }

      if (!options.dontEmit) this.emitUpdated(contact, options.eventTransaction)

      if (firstMessageNotFromMe) {
        this.emit(FIRST_INTERACTION, contact, options.eventTransaction)
      }

      return contact
    })
  }

  async importFromGroup(data, options: Options<ContactInstance> = {}) {
    const { accountId, serviceId } = data

    const service = await serviceResource.findOne({
      where: { id: serviceId, accountId },
    })

    if (!service) {
      throw new NotFoundHttpError(`Service (${serviceId}) not found.`)
    }
    if (!service.data.status.isConnected) {
      throw new BadRequestHttpError(`Service (${serviceId}) disconnected.`)
    }

    const groupsHashMap = groupBy(data.groups, 'id')
    const groupIds = Object.keys(groupsHashMap)

    const groups = await this.findMany({
      where: {
        id: { $in: groupIds },
        accountId,
      },
    })

    const groupsWithParticipants = []

    await queuedAsyncMap(groups, async (group) => {
      const participants = await driverService.syncGroupParticipants(service.id, group.id)

      groupsWithParticipants.push({
        ...group.toJSON(),
        participants,
      })
    })

    const setVisiblePromises = groupsWithParticipants.map((group) =>
      this.bulkUpdate(
        { visible: true },
        {
          where: {
            id: { $in: group.participants.map((p) => p.id) },
          },
        },
      ),
    )
    const importedContacts = (await Promise.all(setVisiblePromises)).filter(Boolean)
    const addTagsPromises = groupsWithParticipants.map((group) =>
      group.participants.map((p) => handleTagsRelations(p, groupsHashMap[group.id][0])),
    )
    await Promise.all(addTagsPromises)

    if (!options.dontEmit) this.emitUpdated(importedContacts.flat(), options.eventTransaction)

    return importedContacts
  }

  async destroyById(id: string, options: Options<ContactInstance> = {}, data: any = {}) {
    return this.getRepository()
      .findById(id)
      .then((model) => this.destroy(model, options, data))
  }

  async destroy(model: ContactInstance, options: Options<ContactInstance> = {}, data: any = {}) {
    return this.nestedTransaction(async (transaction) => {
      const transactions = {
        transaction,
        eventTransaction: options.eventTransaction,
      }

      // destroy avatars
      await fileResource.destroyMany(
        {
          where: {
            attachedType: { $in: ['contact', 'contact.thumbnail'] },
            attachedId: model.id,
          },
        },
        transactions,
      )

      // close tickets
      const contact = await super.findById(model.id, {
        include: ['currentTicket'],
      })

      if (contact) {
        // destroy many schedules of contact
        await scheduleResource.destroyMany({ where: { contactId: contact.id } }, transactions)

        if (contact.currentTicket && contact.currentTicket.currentTicketTransfer) {
          await getCloseTicketQueue(model.id).run(() => this.closeTicketById(model.id, data, transactions))
        }
      }
      // destroy contact
      return super.destroy(model, { ...options, ...transactions })
    }, options.transaction)
  }

  async syncFromServiceById(contact) {
    return driverService.syncContact(contact.serviceId, contact.id)
  }

  async loadEarlierMessages(contact, timestamp) {
    if (configValues.workersGoServices.includes(contact.service?.type)) {
      return Container.get(HttpJobsDispatcher).dispatch(
        'load-earlier-messages',
        {
          accountId: contact.accountId,
          serviceId: contact.serviceId,
          contactId: contact.id,
          timestamp,
        },
        {
          timeout: 2 * 60_000,
          useWorkersGo: true,
        },
      )
    }

    return driverService.loadEarlierMessages(contact.serviceId, contact.id, timestamp)
  }

  async loadEarlierMessagesById(id, timestamp, query) {
    const contact = await this.findById(id, {
      include: ['service'],
      ...query,
    })

    return this.loadEarlierMessages(contact, timestamp)
  }

  // Requires sync zone
  async closeTicket(
    data: {
      contact: ContactInstance
      byUserId?: string
      comments?: string
      ticketTopicIds?: string[] | string
      transferredByBot?: Boolean
    },
    options: {
      dontEmit?: boolean
      eventTransaction?: EventTransaction
      transaction?: Transaction
    } = {},
  ) {
    return ticketService.closeTicket(data, options)
  }

  async closeTicketById(id: string, data, options = {}) {
    const contact = await super.findById(id, {
      include: [
        {
          model: 'currentTicket',
          include: ['currentTicketTransfer', 'user'],
          where: {
            isOpen: true,
          },
          required: false,
        },
        'service',
      ],
    })

    if (!contact) {
      throw new Error(`Contact #${id} is required to close ticket.`)
    }

    if (!contact.currentTicket) {
      throw new Error(`Ticket #${contact.currentTicketId} is required to close ticket.`)
    }

    if (!contact.currentTicket.currentTicketTransfer) {
      throw new Error(
        `CurrentTicketTransfer #${contact.currentTicket.currentTicketTransferId} is required to close ticket.`,
      )
    }

    return this.closeTicket({ contact, ...data }, options)
  }

  async summaryTicketById(id: string, isAdmin: boolean, userId: string = null) {
    const contact = await super.findById(id, {
      include: [
        {
          model: 'currentTicket',
          include: ['currentTicketTransfer', 'user'],
          where: {
            isOpen: true,
          },
          required: false,
        },
        'service',
      ],
    })

    if (!contact) {
      throw new BadRequestHttpError(`Contact #${id} is required to summary ticket.`)
    }

    if (!contact.currentTicket) {
      throw new BadRequestHttpError(`Ticket #${contact.currentTicketId} is required to summary ticket.`)
    }

    const smartSummaryService = Container.get(SmartSummaryService)
    if (await smartSummaryService.limitReached(contact.currentTicket?.accountId, isAdmin)) {
      return {
        success: false,
        limitReached: true,
      }
    }
    await smartSummaryService.start(contact.currentTicket, 'manual', contact, null, null, userId)
    return {
      success: true,
    }
  }

  async transferTicket(
    data: {
      contact: ContactInstance
      userId?: string
      departmentId: string
      byUserId?: string
      comments?: string
      timestamp?: Date
      transferredByBot?: Boolean
    },
    options: {
      dontEmit?: boolean
      eventTransaction?: EventTransaction
      transaction?: Transaction
    } = {},
  ) {
    return ticketService.transferTicket(data, options)
  }

  async transferTicketById(
    id: string,
    data: {
      userId?: string
      departmentId: string
      byUserId?: string
      comments?: string
    },
    options: Options<ContactInstance> = {},
  ) {
    const contact = await super.findById(id, {
      include: [
        {
          model: 'currentTicket',
          include: [
            {
              model: 'currentTicketTransfer',
              include: ['firstMessage'],
            },
          ],
        },
        'account',
      ],
    })

    if (!contact) {
      throw new Error(`Contact #${id} is required to transfer ticket.`)
    }

    return this.transferTicket({ contact, ...data }, options)
  }

  async updateTicket(contact: ContactInstance, data, options: Options<ContactInstance> = {}) {
    await ticketResource.update(
      contact.currentTicket,
      {
        departmentId: data.departmentId,
        contactId: data.contactId,
        userId: data.userId,
        comments: data.comments,
        ...data,
      },
      options,
    )

    if (!options.dontEmit) this.emitUpdated(contact, options.eventTransaction)

    return contact
  }

  async updateTicketById(id: string, data, options: Options<ContactInstance> = {}) {
    const contact = await super.findById(id, { include: ['currentTicket'] })

    return this.updateTicket(contact, data, options)
  }

  markRead(contact: ContactInstance, options: Options<ContactInstance>) {
    return this.update(contact, { unread: 0 }, options)
  }

  markMentionForMeRead(
    contact: ContactInstance,
    hasUnreadMentionForMe: boolean,
    options: Options<ContactInstance> = {},
  ) {
    return this.update(
      contact,
      {
        data: {
          hasUnreadMentionForMe,
        },
      },
      { ...options, mergeJson: ['data'] },
    )
  }

  async markMentionForMeReadById(id: string, hasUnreadMentionForMe: boolean, options: Options<ContactInstance> = {}) {
    const contact = await this.findById(id, options)

    if (hasUnreadMentionForMe === true && contact.unread === 0) return contact

    return this.markMentionForMeRead(contact, hasUnreadMentionForMe, options)
  }

  async markReadById(id: string, options: Options<ContactInstance> = {}) {
    const contact = await this.findById(id, options)

    const { isGroup, data, unread } = contact

    if (isGroup && data.hasUnreadMentionForMe) await this.markMentionForMeRead(contact, false)

    if (unread <= 0) return contact

    await this.markRead(contact, options)

    return contact
  }

  async addTags(contact: ContactInstance, tags = [], options: Options<ContactInstance> = {}) {
    await contact.addTags(tags)
    this.emitUpdated(contact, options.eventTransaction)
    return super.reload(contact, options)
  }

  async removeTags(contact: ContactInstance, tags = [], options: Options<ContactInstance> = {}) {
    await contact.removeTags(tags)
    this.emitUpdated(contact, options.eventTransaction)
    return super.reload(contact, options)
  }

  getContactName(contact: ContactInstance) {
    return (
      contact?.internalName ||
      contact?.name ||
      contact?.alternativeName ||
      contact?.data?.number ||
      contact?.idFromService ||
      ''
    )
  }

  protected async attachToOtherContactsOfTheSameNumber(
    contact: ContactInstance,
    options: Options<ContactInstance> = {},
  ) {
    // @ts-ignore
    if (!contact?.idFromService) return contact

    let personId = contact.personId

    const serviceType = (
      contact.service ||
      (await serviceResource.findById(contact.serviceId, {
        attributes: ['type'],
        where: { accountId: contact.accountId },
      }))
    )?.type

    if (!personId) {
      const person = await personResource.findOne({
        ...options,
        where: {
          accountId: contact.accountId,
        },
        include: [
          {
            model: 'contacts',
            where: {
              idFromService: serviceType.match(/^whatsapp.*$|^sms-wavy$/)
                ? [
                    ...comparableIdFromService(parseNumber(contact.idFromService) + '@c.us', 'whatsapp'),
                    ...comparableIdFromService(contact.idFromService, 'whatsapp-business'),
                  ]
                : contact.idFromService,
              archivedAt: null,
            },
          },
        ],
      })

      personId = person && person.id
    }

    if (personId) {
      contact.personId = personId
      await this.bulkUpdate(
        { personId },
        {
          ...options,
          where: {
            id: { $ne: contact.id },
            personId: { $ne: personId },
            idFromService: serviceType.match(/^whatsapp.*$|^sms-wavy$/)
              ? [
                  ...comparableIdFromService(parseNumber(contact.idFromService) + '@c.us', 'whatsapp'),
                  ...comparableIdFromService(contact.idFromService, 'whatsapp-business'),
                ]
              : contact.idFromService,
            accountId: contact.accountId,
            archivedAt: null,
          },
        },
      )
    }

    return contact
  }

  onInitChatWebchat(listener) {
    return this.on(INIT_CHAT_WEBCHAT, listener)
  }

  emitInitChatWebchat(data, evenTransaction?) {
    return this.emit(INIT_CHAT_WEBCHAT, data, evenTransaction)
  }

  onAnonymousUserCreatedWebchat(listener) {
    return this.on(ANONYMOUS_USER_CREATED_WEBCHAT, listener)
  }

  emitAnonymousUserCreatedWebchat(data, evenTransaction?) {
    return this.emit(ANONYMOUS_USER_CREATED_WEBCHAT, data, evenTransaction)
  }

  includeTicketTransfer = async (contacts: any, context: any): Promise<ContactInstance[]> => {
    contacts = Array.isArray(contacts) ? contacts : [contacts]
    let transfers = []
    let userHasPermission = await messageResource.userHasPermissionToTicketTransfer(context.user.permissions)
    if (userHasPermission) {
      const ticketIds = contacts.reduce((result: string[], contact: ContactInstance) => {
        if ((contact?.lastMessage || {}).ticketId) {
          result.push(contact.lastMessage.ticketId)
        }
        return result
      }, [])

      if (ticketIds.length > 0) {
        transfers = await ticketTransfersResource.getTicketTransfer(ticketIds)
      }
    }

    userHasPermission = await messageResource.userHasPermissionToOpenTickets(context.user.permissions)
    let openTickets = []
    if (userHasPermission) {
      openTickets = await this.getOpenTicketsByContactsIds(
        (contacts.data || contacts).filter((c) => c.currentTicketId).map((c) => c.id),
        context.account.id,
      )
    }
    context = { ...context, openTickets }
    return queuedAsyncMap(contacts, async (contact: ContactInstance) => {
      if (contact?.lastMessage) {
        const ticketTransfers = transfers.find((transfer) => transfer.ticketId === contact.lastMessage.ticketId)
        const message = contact.lastMessage
        message.transferUserIds = ticketTransfers?.transferUserIds || []
        message.transferDepartmentIds = ticketTransfers?.transferDepartmentIds || []
        contact.lastMessage = message
      }
      return await contactTransformer(contact, context)
    })
  }

  async getOpenTicketsByContactsIds(contactsIds, accountId) {
    return iteratePaginated(
      ({ page }) =>
        ticketResource.findManyPaginated({
          page,
          perPage: 500,
          where: {
            isOpen: true,
            contactId: { $in: uniq(contactsIds) },
            accountId,
          },
        }),
      async (ticket) => ticket,
      100,
      true,
    )
  }

  getTicketsCount(
    accountId: string,
    userId: string,
    departmentIds: string[],
    permissions: any,
  ): Promise<{ counts: [number, number] }> {
    const canViewAll = hasPermission(permissions, 'tickets.view.all')
    const canViewAllDepartments = hasPermission(permissions, 'tickets.view.all.departments')
    const canViewGroups = hasPermission(permissions, 'groups.view')

    return ticketRepository
      .getTicketsCount(accountId, {
        userId,
        departmentIds,
        canViewAll,
        canViewAllDepartments,
        canViewGroups,
      })
      .then((result) => {
        const [data] = result
        return {
          counts: [Number.parseInt(data?.queueCount) || 0, Number.parseInt(data?.mineCount) || 0],
        }
      })
  }

  async migrateIdFromServiceFromTelegramGroups(previousIdFromService: string, newIdFromService: string) {
    const telegramChatGroup = await this.findOne({
      where: {
        idFromService: previousIdFromService.toString(),
      },
    })

    if (!telegramChatGroup) return

    return this.update(telegramChatGroup, {
      idFromService: newIdFromService.toString(),
    })
  }

  findManyPaginated(query: PaginatedOptions<ContactInstance>): Promise<{
    data: ContactInstance[]
    total: number
    limit: number
    skip: number
    currentPage: number
    lastPage: number
    from: number
    to: number
  }> {
    return contactRepository.findManyPaginated(query)
  }

  async findMediasByContact(
    contactId: string,
    context: any,
    {
      cursor,
      order = [[DEFAULT_ORDER, DEFAULT_ORDER_DIRECTION]],
      perPage = DEFAULT_PERPAGE,
      type,
      name,
    }: IFindMediaByContactInput,
  ) {
    const DOCUMENT_TYPE = 'document'
    const LINK_TYPE = 'link'

    const getTypeCondition = (type: string): Record<string, string[]> => {
      if (type === DEFAULT_MEDIA_TYPE) {
        return { [Op.in]: [MessageTypeEnum.IMAGE.valueOf(), MessageTypeEnum.VIDEO.valueOf()] }
      }

      if (type === DOCUMENT_TYPE) {
        return { [Op.in]: [MessageTypeEnum.DOCUMENT] }
      }

      if (type === LINK_TYPE) {
        return { [Op.in]: [MessageTypeEnum.LINK] }
      }

      return { [Op.in]: [MessageTypeEnum.IMAGE.valueOf(), MessageTypeEnum.VIDEO.valueOf(), MessageTypeEnum.DOCUMENT] }
    }

    const hasLink = type === LINK_TYPE

    const orderCreateAt = cursor
      ? {
          createdAt: {
            [order[0][1].toLowerCase() === DEFAULT_ORDER_DIRECTION ? Op.lt : Op.gt]: new Date(cursor),
          },
        }
      : {}

    const whereClause = {
      contactId,
      type: getTypeCondition(type),
    }

    const linkModel = {
      include: [
        {
          model: 'link',
          required: true,
          where: {
            ...orderCreateAt,
          },
        },
      ],
      order: [[order[0][0], order[0][1]]],
    }

    const fileModel = {
      include: [
        { model: 'preview' },
        {
          model: 'file',
          where: {
            ...orderCreateAt,
            ...(name && { name: { [Op.iLike]: `%${name}%` } }),
          },
        },
      ],
      order: [['file', order[0][0], order[0][1]]],
    }

    const messageFiles: MessageInstance[] = await messageRepository.findMany({
      where: whereClause,
      ...(hasLink ? linkModel : fileModel),
      limit: Number(perPage) + 1,
    })

    if (!messageFiles?.length)
      return {
        data: [],
        limit: Number(perPage),
        hasNextPage: false,
        nextCursor: null,
      }

    const getNextCursor = (type: 'link' | 'file', elements: MessageInstance[]) => {
      if (Array.isArray(elements.at(-1)[type])) {
        return (hasNextPage && (elements?.at(-1)?.[type] || []).at(-1)?.createdAt) || null
      }
      return (hasNextPage && elements?.at(-1)?.[type]?.createdAt) || null
    }

    const hasNextPage = messageFiles.length > perPage
    const currentCursorItems = messageFiles.slice(0, perPage)
    const nextCursor = getNextCursor(hasLink ? 'link' : 'file', currentCursorItems)
    const UNAUTHORIZED_MESSAGE = 'UNAUTHORIZED_MESSAGE'

    const mappedResponse = await Promise.all(
      currentCursorItems.map(async (messageFile: MessageInstance) => {
        const { file, preview } = messageFile
        const messageInfo = await messageTransformer(messageFile, context)

        const isPdf = file?.mimetype === 'application/pdf' ? 'inline' : 'attachment'
        const fileInfo = await fileTransformer(file, undefined, isPdf)

        const previewInfo = messageFile.type === MessageTypeEnum.VIDEO ? preview : null
        const isAuthorizedMessage = messageInfo.text !== UNAUTHORIZED_MESSAGE
        const messageLink = hasLink && messageInfo.text ? getLinksFromText(messageInfo.text) : null
        const data = messageLink ? messageInfo : fileInfo

        return {
          id: !isAuthorizedMessage ? UNAUTHORIZED_MESSAGE : data?.id,
          attachedId: messageInfo.id,
          name: !isAuthorizedMessage ? UNAUTHORIZED_MESSAGE : data?.name,
          ...(!messageLink && { mimetype: !isAuthorizedMessage ? UNAUTHORIZED_MESSAGE : fileInfo?.mimetype }),
          createdAt: data?.createdAt,
          updatedAt: data?.updatedAt,
          accountId: !isAuthorizedMessage ? UNAUTHORIZED_MESSAGE : data?.accountId,
          url: !isAuthorizedMessage ? UNAUTHORIZED_MESSAGE : data?.url,
          ...(!messageLink && { previewUrl: previewInfo?.url }),
          isAuthorized: isAuthorizedMessage,
          ...(messageLink && { links: messageLink }),
        }
      }),
    )

    return {
      data: mappedResponse,
      limit: Number(perPage),
      hasNextPage,
      nextCursor,
    }
  }

  async countContactMedias(contactId: string) {
    const [countResults] = await sequelize.query(`
     SELECT 
        CASE 
            WHEN m.type IN ('image', 'video') THEN 'media'
            WHEN m.type = 'chat' AND l."messageId" IS NOT NULL THEN 'link'
            ELSE type
        END AS type_group,
        COUNT(*) AS count_per_group
    FROM 
        messages m
    LEFT JOIN files f
        ON m.id = f."attachedId" AND f."attachedType" = 'message.file'
    LEFT JOIN links l
        ON m.id = l."messageId"
    WHERE
        m."contactId" = '${contactId}' AND
        (
            m.type IN ('image', 'video', 'document') OR
            (m.type = 'chat' AND l."messageId" IS NOT NULL)
        ) AND
        m."deletedAt" IS NULL AND
        f."deletedAt" IS NULL
    GROUP BY 
        GROUPING SETS (
            (CASE 
                WHEN m.type IN ('image', 'video') THEN 'media'
                WHEN m.type = 'chat' AND l."messageId" IS NOT NULL THEN 'link'
                ELSE type 
            END),
            ()
        );
    `)

    return countResults.reduce((acc, { type_group, count_per_group }) => {
      const key = type_group || 'total'
      acc[key] = Number(count_per_group)
      return acc
    }, {})
  }

  async getContactsWhatsApp() {
    const contacts = await this.findMany({
      where: {
        id: {
          [Op.notIn]: sequelize.literal('(select m."contactId" from messages m)'),
        },
        idFromService: {
          $iLike: '%c.us',
        },
        archivedAt: null,
      },
      include: [
        {
          model: 'service',
          where: {
            archivedAt: null,
            type: 'whatsapp',
            'data.status.isStarted': true,
          },
          required: true,
        },
      ],
    })

    return contacts
  }

  async setMarkForNoBan(accountId: string, ticketIds: string[]) {
    return queuedAsyncMap(ticketIds, async (ticketId) => {
      await redisCache.set<Date>(keyMarkForNoBan(accountId, ticketId), 1, MARK_FOR_NO_BAN_TTL)
    })
  }

  async isMarkedForNoBan(accountId: string, ticketId: string) {
    return redisCache
      .get<Date>(keyMarkForNoBan(accountId, ticketId))
      .then(Boolean)
      .catch(() => false)
  }

  async getOrInsertContactByVcard(serviceId: string, accountId: string, vcards: any) {
    const phoneNumbers = vcards
      .map((v) => {
        const match = v.vcard.match(/waid=(\d+)/)
        return match ? match[1] : null
      })
      .filter((num) => num !== null)

    const contactsFound = await this.findMany({
      where: {
        data: {
          number: {
            $in: phoneNumbers.map((phone) => phone).flat(),
          },
        },
        serviceId: serviceId,
        accountId: accountId,
      },
    })

    if (contactsFound.length === vcards.length) {
      return contactsFound.map((contact) => contact.id)
    }

    const contacts = await queuedAsyncMap(vcards, async (vcard) => {
      const matchNumber = vcard.vcard.match(/waid=(\d+)/)
      if (!matchNumber) {
        return null
      }

      const number = matchNumber ? matchNumber[1] : null
      const matchName = vcard.vcard.match(/FN:(.+)/)
      const name = matchName ? matchName[1] : null

      const existContact = await contactsFound.find((contact) => contact.data.number === number)

      if (existContact) {
        return existContact.id
      }

      const contactCreated = await this.create({
        serviceId: serviceId,
        number: number,
        name: name,
        alternativeName: name,
        accountId: accountId,
        visible: true,
      })

      return contactCreated.id
    })

    return contacts.filter((contact) => contact !== null)
  }
}

export default new ContactResource()
