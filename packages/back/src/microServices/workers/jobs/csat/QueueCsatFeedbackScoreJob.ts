import Container, { Inject, Service } from 'typedi'
import { addMinutes } from 'date-fns'
import { Op } from 'sequelize'
import { OpenAI } from 'openai'
import Job from '../../../../core/services/jobs/interfaces/Job'
import messageResource from '../../../../core/resources/messageResource'
import { TranslateHeaderOutput, language } from '../../../../core/utils/translateHeaderTicketHistory'
import config from '../../../../core/config'
import ticketResource from '../../../../core/resources/ticketResource'
import QueueJobsDispatcher from '../../../../core/services/jobs/queue/QueueJobsDispatcher'
import answersResource from '../../../../core/resources/answersResource'
import { MessageInstance } from '../../../../core/dbSequelize/models/Message'
import Logger from '../../../../core/services/logs/Logger'
import { TicketInstance } from '../../../../core/dbSequelize/models/Ticket'
import accountResource from '../../../../core/resources/accountResource'
import { HttpClient } from '../../../../core/services/httpClient/HttpClient'
import wait from '../../../../core/utils/wait'
import creditMovementResource from '../../../../core/resources/creditMovementResource'
import { CsatMessageUtils } from './utils/CsatMessageUtils'

interface PayloadProps {
  userId: string
  ticketId: string
  accountId: string
}
@Service()
export class QueueCsatFeedbackScoreJob implements Job {
  static jobName = 'queued-csat-feedback-score'

  @Inject()
  protected queueJobsDispatcher: QueueJobsDispatcher

  @Inject()
  protected httpClient: HttpClient

  protected logger = Container.get(Logger)

  private async setRoleTalk(message: MessageInstance, userLanguage: TranslateHeaderOutput): Promise<string> {
    if (message.isFromBot || message.origin === 'bot') return userLanguage.bot
    if (message.isFromMe) return userLanguage.attendant
    return userLanguage.customer
  }

  public async generateAIScoreAndFeedback(messages: MessageInstance[], userId: string, prompt: string) {
    if (!messages || messages.length == 0) {
      this.logger.log('Messages are missing', 'error')
      return null
    }
    if (!prompt) {
      this.logger.log('Prompt is missing', 'error')
      return null
    }

    const languageUser = await language({ userId })

    const decryptedMessagesPromises = messages.map(async (m) => {
      const roleTalk = await this.setRoleTalk(m, languageUser)
      const text = await messageResource.decryptMessageText(m)
      return `${roleTalk}${text}`
    })

    const decryptedMessages = await Promise.all(decryptedMessagesPromises)
    const fullTicketHistory = decryptedMessages.join('\n')

    const promptPattern = `**OBRIGATÓRIO**: A nota final gerada deve respeitar o formato: FINALSCORE_aqui-deve-ser-um-numero-que-represente-a-nota-de-csat`
    const fullPrompt = `${promptPattern}\n${prompt}`

    this.logger.log(`Returning response from AI...`, 'info')

    return this.integrationByOpenAi(fullTicketHistory, fullPrompt)
  }

  async integrationByOpenAi(ticketHistory: string, prompt: string) {
    const apiKey = config('apiKeyOpenIA')
    const openai = new OpenAI({
      apiKey: apiKey,
    })
    const messagePrompt = `${prompt} :\n\n${ticketHistory}`
    try {
      const response = await openai.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: 'Você é um assistente útil.' },
          { role: 'user', content: messagePrompt },
        ],
      })

      let message = response?.choices[0]?.message?.content
      if (message) {
        message = message.replace(/Justificativa:/, '')
      }

      return message ?? null
    } catch (error) {
      this.logger.log(`Error generating AI score and feedback: ${error?.message}`, 'error')
      return null
    }
  }

  private async createScoreMessage(ticket: TicketInstance, messageText?: string) {
    if (!messageText) {
      this.logger.log('Invalid smart csat feedback', 'warn')
    }

    try {
      await CsatMessageUtils.createCsatMessage(ticket, messageText || 'INVALID_SMART_CSAT_SCORE')
    } catch (error) {
      this.logger.log(`Error creating score message: ${error.message}`, 'warn')
    }
  }

  private checkScoreClassification(score: number): string {
    const classifications: Record<number, string> = {
      1: 'Muito insatisfeito',
      2: 'Insatisfeito',
      3: 'Neutro',
      4: 'Satisfeito',
      5: 'Muito satisfeito',
    }

    return classifications[score] || 'Não avaliado'
  }

  async handle(payload: PayloadProps) {
    this.logger.log(`Run for: ${payload.ticketId}`)
    const { userId, ticketId, accountId } = payload

    if (!ticketId || !accountId) {
      this.logger.log(`Invalid payload`, 'error')
      return
    }

    const account = await accountResource.findById(accountId, {
      attributes: ['promptAiCsat', 'settings'],
    })

    if (!account.promptAiCsat) {
      this.logger.log(`Account ${accountId} does not have AI CSAT prompt. Skipping...`, 'error')
      return
    }

    const answer = await answersResource.findOne({
      where: {
        ticketId: ticketId,
        aiGenerated: false,
      },
      include: [{ model: 'question' }],
    })

    if (!answer) {
      this.logger.log(`No valid answer found for Ticket: ${ticketId}. Skipping...`, 'warn')
      return
    }

    const ticket = await ticketResource.findById(ticketId, {
      attributes: ['id', 'userId', 'contactId'],
      include: [{ model: 'contact', include: ['account'] }],
    })

    const messages = await messageResource.findMany({
      where: {
        ticketId: ticketId,
        type: {
          $in: ['audio', 'ptt', 'chat'],
        },
      },
      order: [['createdAt', 'ASC']],
    })

    await answersResource.updateById(answer.id, {
      aiGenerated: true,
    })

    this.logger.log(`Sending the ticket ${ticketId} to AI for summarization`, 'info')

    const response = await this.generateAIScoreAndFeedback(messages, userId, account?.promptAiCsat)

    if (!response) {
      this.logger.log(`Failed to generate AI score and feedback for Ticket ${ticketId}`, 'error')
      this.createScoreMessage(ticket)
      return
    }

    const regex = /FINALSCORE_[1-5]\.?/
    const pattern = response?.match(regex)

    if (!pattern?.length) {
      this.logger.log(
        `AI did not return a valid CSAT score for Ticket ${ticketId}. Response: ${JSON.stringify(response)}`,
        'warn',
      )
      this.createScoreMessage(ticket)
      return
    }

    const score = pattern[0].split('_')[1].replace('.', '')
    const feedback = response.replace(regex, '')

    this.logger.log(`AI summarized the ticket ${ticketId}: ${feedback}`, 'info')

    await answersResource.updateById(answer.id, {
      text: score,
      aiText: feedback,
    })

    const regexFeedback = /^(?:\*\*|\n)+/

    const messageText = `**Nota**: ${score} - ${this.checkScoreClassification(
      Number(score),
    )} \n **Justificativa**:${feedback.replace(regexFeedback, ' ')}`

    this.createScoreMessage(ticket, messageText)

    await creditMovementResource.createDebit({
      accountId: accountId,
      serviceType: 'csat',
      amount: 1,
      origin: 'single',
      serviceId: messages?.[0]?.serviceId,
      userId: userId,
    })
  }
}
