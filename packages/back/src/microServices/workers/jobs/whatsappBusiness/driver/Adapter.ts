import axios from 'axios'
import { differenceInSeconds, subDays } from 'date-fns'
import deepEquals from 'fast-deep-equal'
import fileType from 'file-type'
import omit from 'lodash/omit'
import pick from 'lodash/pick'
import omitDeep from 'omit-deep-lodash'
import { Container } from 'typedi'
import config from '../../../../../core/config'
import { MessageInstance } from '../../../../../core/dbSequelize/models/Message'
import { ServiceInstance } from '../../../../../core/dbSequelize/models/Service'
import WhatsappBusinessTemplateRepository from '../../../../../core/dbSequelize/repositories/WhatsappBusinessTemplateRepository'
import { processReceivedFileQueue } from '../../../../../core/queues/whatsappBusiness'
import accountResource from '../../../../../core/resources/accountResource'
import answersResource from '../../../../../core/resources/answersResource'
import contactResource from '../../../../../core/resources/contactResource'
import campaignResource from '../../../../../core/resources/campaignResource'
import fileResource from '../../../../../core/resources/fileResource'
import messageResource from '../../../../../core/resources/messageResource'
import serviceResource from '../../../../../core/resources/serviceResource'
import whatsappBusinessTemplateResource from '../../../../../core/resources/whatsappBusinessTemplateResource'
import QueueJobsDispatcher from '../../../../../core/services/jobs/queue/QueueJobsDispatcher'
import Logger from '../../../../../core/services/logs/Logger'
import reportError from '../../../../../core/services/logs/reportError'
import fileTransformer from '../../../../../core/transformers/fileTransformer'
import queuedAsyncMap from '../../../../../core/utils/array/queuedAsyncMap'
import iteratePaginated from '../../../../../core/utils/iteratePaginated'
import {
  getAutheticationText,
  getExpirationWarningText,
} from '../../../../../core/utils/whatsapp/whatsappBusinessFormatter'
import { deleteWebhookInGateway } from '../../facebookMessenger/driver/baseFunctions'
import QueuedWhatsappBusinessSendMessageToBrokerJob from '../QueuedWhatsappBusinessSendMessageToBrokerJob'
import QueuedWhatsappBusinessStatusJob from '../QueuedWhatsappBusinessStatusJob'
import connector, { ConnectorInterface } from './Connector'
import {
  createOrUpdateDataInGateway,
  getMessageDataByOrigin,
  getMessageDataByType,
  getOrigin,
  getSendMessageType,
  getWebhookInGatewayByUrl,
  getWebhookType,
  isChargedMessage,
  isTunnelClosed,
  phoneNumberIsValid,
  setWebhook,
  validateHsmAvailability,
  validateHsmParams,
  getReactionData,
} from './baseFunctions'
import {
  getWebhookType as gupShupGetWebhookType,
  getMessageDataByType as gupshupGetMessageDataByType,
  setupApp as setupGupshupApp,
  getReactionData as gupshupGetReactionData,
  updateWhatsappMessageId,
} from './gupshupFunctions'
import { errorsWebhook, messagesWebhook, reactionsWebhook, statusesWebhook } from './webhooks'
import {
  messagesWebhook as gupShupMessagesWebhook,
  reactionsWebhook as gupShupReactionsWebhook,
  syncReactions,
} from './webhooks/gupShup'
import { Reaction } from './webhooks/gupShup/ReactionsWebhook'
import scheduleResource from '../../../../../core/resources/scheduleResource'
import hsmError from '../../../../../core/resources/utils/hsmError'

const logger = Container.get(Logger)

const log = logger.log

const queueJobsDispatcher = Container.get(QueueJobsDispatcher)

const dispatchToSendMessageToBroker = async (data, options = {}) => {
  const defaultOptions = {
    hashKey: data.serviceId,
  }

  return queueJobsDispatcher.dispatch<QueuedWhatsappBusinessSendMessageToBrokerJob>(
    'queued-whatsapp-business-send-message-to-broker',
    data,
    { ...defaultOptions, ...options },
  )
}

let milliseconds = 1

export default class Adapter {
  private connector: ConnectorInterface

  private serviceId: string

  private service: ServiceInstance

  constructor(serviceId) {
    this.serviceId = serviceId
  }

  async getService() {
    this.service = await serviceResource.findById(this.serviceId)
  }

  async getCampaigns(template) {
    return campaignResource.findMany({
      where: {
        finishedAt: { $eq: null },
      },
      include: [
        {
          model: 'messages',
          where: {
            hsmId: template.id,
          },
        },
      ],
    })
  }

  async connect() {
    await this.getService()

    if (this.service.data.providerType === 'meta') {
      this.connector = connector({
        ...pick(this.service.data, ['providerType', 'driverId', 'businessId']),
        ...pick(this.service.internalData, ['token']),
        serviceId: this.serviceId,
      })
      return
    }
    this.connector = connector({
      ...pick(this.service.data, ['providerType', 'appName', 'phone']),
      ...pick(this.service.internalData, ['token', 'accountKey', 'apiKey']),
      ...pick(this.service.settings, ['tokenSandBox360', 'cloudApi']),
      serviceId: this.serviceId,
    })
  }

  async logout() {
    await this.getService()
    if (['360Dialog', 'positus'].includes(this.service.data.providerType)) return true

    await serviceResource.update(
      this.service,
      {
        data: {
          status: {
            ...this.service.data?.status,
            isStarting: true,
          },
        },
      },
      { mergeJson: ['data'] },
    )

    const url = `${config('publicUrl')}/whatsapp-business-webhook/${this.serviceId}`

    const webhook = await getWebhookInGatewayByUrl(url, this.service.data.driverId)

    if (webhook) {
      log(`Deletando webhook #id: ${webhook.id} do gateway.`, 'info')
      await deleteWebhookInGateway(webhook.id)
    }

    await serviceResource.update(this.service, {
      data: {
        driverId: null,
        businessId: null,
        numberId: null,
        status: {
          isStarting: false,
          isStarted: false,
          isConnected: false,
        },
      },
      internalData: {},
    })

    return true
  }

  async restart() {
    return true
  }

  async setup() {
    await this.getService()
    if (
      this.service.data.providerType === '360Dialog' &&
      this.service.internalData.token &&
      !this.service.data.status.isConnected
    ) {
      await setWebhook(this.service)
    }

    if (this.service.data.providerType === 'meta' && this.service.internalData.token) {
      await serviceResource.update(
        this.service,
        {
          data: {
            status: {
              ...this.service.data.status,
              isStarting: true,
              isStarted: true,
              isConnected: true,
            },
          },
        },
        { mergeJson: ['data'] },
      )

      log(`Refreshing ${this.service.type} ${this.service.id}}`, 'info', [this.service.data])
      await createOrUpdateDataInGateway(this.serviceId, this.service.data.driverId)
    }

    let partnerAppData = null
    let mergeJson = ['data']
    if (this.service.data.providerType === 'gupshup') {
      const response = (await setupGupshupApp(this.service)) as any
      if (response?.error) {
        return this.destroy()
      }
      log(
        `Retrieved ParnerApp: ${JSON.stringify(pick(response, ['id', 'name', 'walletId', 'customerId']))} for service ${
          this.serviceId
        }`,
      )
      partnerAppData = pick(response, ['id', 'name', 'phone', 'walletId', 'appToken', 'customerId', 'partnerToken'])
      mergeJson.push('internalData')
    }

    const isConnected = !!(this.service.data.providerType !== '360Dialog' || this.service.internalData.token)

    await serviceResource.update(
      this.service,
      {
        data: {
          status: {
            ...this.service.data.status,
            isStarting: false,
            isStarted: isConnected,
            isConnected,
          },
        },
        ...(partnerAppData && { internalData: { ...partnerAppData } }),
      },
      { mergeJson },
    )

    if (isConnected) {
      await this.refreshTemplates()
    }
  }

  async refresh() {
    await this.setup()
  }

  async start() {
    await this.getService()
    log(`Starting ${this.service.type} ${this.service.id} ${this.service.data.providerType}`, 'info')

    const isIntegrated = ['meta', '360Dialog'].includes(this.service.data.providerType)

    await serviceResource.update(
      this.service,
      {
        data: {
          status: {
            ...this.service.data.status,
            isStarting: !isIntegrated,
            isStarted: !isIntegrated,
            isConnected: !isIntegrated,
          },
        },
      },
      { mergeJson: ['data'] },
    )
    await this.setup()
  }

  async newToken() {
    await this.getService()
    if (this.service.data.providerType !== 'positus') return

    const username = process.env.POSITUS_USERNAME
    const password = process.env.POSITUS_PASSWORD

    if (!username || !password) throw new Error('POSITUS_USERNAME or POSITUS_PASSWORD does not exist on env!')

    // login
    const auth = await axios.post('https://api.positus.global/v2/login', {
      username,
      password,
    })

    const url = process.env.PUBLIC_URL

    const name = `${
      url.replace('http://', '').replace('https://', '').replace('www.', '').split('/')[0].split('.')[0]
    }.${this.service.accountId}.${this.service.id}`

    // token
    const { data: token } = await axios.post(
      'https://api.positus.global/v2/user/tokens',
      {
        name,
      },
      {
        headers: {
          Authorization: `Bearer ${auth.data.access_token}`,
        },
      },
    )
    // logout
    await axios.post(
      'https://api.positus.global/v2/logout',
      {},
      {
        headers: {
          Authorization: `Bearer ${auth.data.access_token}`,
        },
      },
    )

    // token
    await serviceResource.update(this.service, {
      data: {
        ...this.service.data,
        tokenData: {
          name: token.data.name,
          createdAt: token.data.created_at,
          expiresAt: token.data.expires_at,
        },
      },
      internalData: {
        ...this.service.internalData,
        token: token.access_token,
      },
    })
  }

  async refreshTemplates() {
    log(`Refreshing templates service #${this.serviceId}`, 'info')

    await this.connect()

    const authenticationComponents = (components, language) => {
      const body = components.find((component) => component.type === 'BODY')
      const buttons = components.find((component) => component.type === 'BUTTONS')
      const footer = components.find((component) => component.type === 'FOOTER')
      const header = components.find((component) => component.type === 'HEADER')

      const payloadComponets = []

      if (body) {
        payloadComponets.push({
          ...body,
          ...{
            text: getAutheticationText(language),
            params: ['VERIFICATION_CODE'],
          },
        })
      }

      if (buttons) {
        payloadComponets.push(buttons)
      }

      if (footer) {
        payloadComponets.push({
          ...footer,
          ...{ text: getExpirationWarningText(language) },
        })
      }

      if (header) {
        payloadComponets.push(header)
      }

      return payloadComponets
    }

    const response = await this.connector.getTemplates(this.service.internalData)

    if (!response) throw new Error(`Error when getting templates.`)

    const getTemplates = () => {
      if (this.service.data.providerType === 'meta') {
        return response.message_templates?.data || response.waba_templates
      }

      if (this.service.data.providerType === 'gupshup') {
        return (response.templates || []).map((template) => {
          const getContainerMeta = () => {
            if (template?.containerMeta) {
              return JSON.parse(template?.containerMeta)
            }
            if (template?.meta) {
              return JSON.parse(template?.meta)
            }
            return {}
          }
          const containerMeta = getContainerMeta()

          return {
            ...template,
            name: template.elementName,
            language: template.languageCode,
            rejected_reason: template.reason,
            components: [
              ...(template.templateType !== 'TEXT'
                ? [
                    {
                      example: {
                        header_handle: [containerMeta?.sampleMedia],
                      },
                      format: template.templateType,
                      type: 'HEADER',
                    },
                  ]
                : [
                    ...(containerMeta?.header
                      ? [
                          {
                            ...(() => {
                              if (!containerMeta?.sampleHeader) return {}

                              const regex = /\[([^\>]+)\]/
                              const match = (containerMeta.sampleHeader || '').match(regex)?.[1]

                              if (!match) return {}

                              return {
                                example: {
                                  header_text: [match],
                                },
                              }
                            })(),
                            format: 'TEXT',
                            text: containerMeta?.header,
                            type: 'HEADER',
                          },
                        ]
                      : []),
                  ]),
              {
                text: containerMeta?.data,
                type: 'BODY',
              },
              ...(containerMeta?.footer
                ? [
                    {
                      text: containerMeta?.footer,
                      type: 'FOOTER',
                    },
                  ]
                : []),
              ...(containerMeta?.buttons?.length
                ? [
                    {
                      buttons: containerMeta?.buttons,
                      type: 'BUTTONS',
                    },
                  ]
                : []),
            ],
          }
        })
      }

      return response?.waba_templates
    }

    const wabaTemplates = getTemplates() ?? []

    if (!wabaTemplates.length) return null

    await iteratePaginated(
      ({ page }) =>
        whatsappBusinessTemplateResource.findManyPaginated({
          attributes: ['id', 'name', 'archivedAt', 'language', 'quality', 'accountId'],
          where: {
            serviceId: this.serviceId,
            accountId: this.service.accountId,
            status: { $notIn: ['', 'SENDING'] },
          },
          page,
          perPage: 100,
        }),

      async (template) => {
        try {
          const templateExistsOnResponseAndChangedQualityToLowOrMedium = wabaTemplates.some(
            ({ name, status, quality }) =>
              name === template.name &&
              status.toUpperCase() === 'APPROVED' &&
              quality !== template.quality &&
              (quality === 'LOW' || quality === 'MEDIUM'),
          )

          const templateExistsOnResponseAndItIsApproved = wabaTemplates.some(
            ({ name, status, language }) =>
              name === template.name && status.toUpperCase() === 'APPROVED' && language === template.language,
          )

          if (!templateExistsOnResponseAndItIsApproved) {
            if (!template.archivedAt) {
              await WhatsappBusinessTemplateRepository.updateById(template.id, { archivedAt: new Date() })
              log(`Archiving template "${template.name}" to service #${this.serviceId}.`, 'info')
            }
            return
          }

          if (template.archivedAt) {
            await WhatsappBusinessTemplateRepository.updateById(template.id, { archivedAt: null })
            log(`Unarchiving template "${template.name}" to service #${this.serviceId}.`, 'info')
          }

          if (templateExistsOnResponseAndChangedQualityToLowOrMedium) {
            await this.autoPauseCampaigns(template)
          }

          return
        } catch (error) {
          reportError(error)
          return
        }
      },
      10,
    )

    const namespace =
      wabaTemplates[0].namespace ||
      response.message_template_namespace ||
      (this.service.data.providerType === 'meta' && (await this.connector.getNamespaces()))

    // support languages
    const languages = ['pt', 'pt_BR', 'es', 'en', 'en_US']

    const createdTemplates = await queuedAsyncMap(
      wabaTemplates,
      async (template: any) => {
        try {
          if (!languages.includes(template.language)) {
            return
          }

          const serverTemplate = await whatsappBusinessTemplateResource.findOne({
            where: {
              name: template.name,
              language: template.language,
              serviceId: this.serviceId,
              accountId: this.service.accountId,
              archivedAt: {
                $or: [null, { $gte: subDays(new Date(), 30) }],
              },
            },
            order: [['archivedAt', 'DESC']],
          })

          const serverPick = pick(serverTemplate, [
            'language',
            'name',
            'category',
            'namespace',
            'status',
            'rejectedReason',
            'components',
            'messageType',
            'quality',
          ])

          const remotePick = {
            ...pick(template, ['language', 'name', 'category', 'components', 'messageType']),
            namespace,
            status: template.status?.toUpperCase() || serverPick.status,
            rejectedReason: template.rejected_reason,
            quality: (template?.quality ?? template?.quality_score?.score ?? serverPick?.quality)?.toUpperCase(),
          }

          const isDiff = !deepEquals(serverPick, remotePick)

          const providerType = this.service.data.providerType
          if (serverTemplate) {
            if (isDiff) {
              const serverComponents = serverTemplate.components
              const remoteComponents = template.components

              const body = serverComponents.find((component) => component.type === 'BODY')
              const header = serverComponents.find((component) => component.type === 'HEADER')
              const buttons = serverComponents.find((component) => component.type === 'BUTTONS')
              const buttonsItems = buttons?.buttons

              const newComponents = remoteComponents

              const newHeaderIndex = newComponents.findIndex((component) => component.type === 'HEADER')
              const newBodyIndex = newComponents.findIndex((component) => component.type === 'BODY')
              const newButtonsIndex = newComponents.findIndex((component) => component.type === 'BUTTONS')

              if (newHeaderIndex !== -1) {
                newComponents[newHeaderIndex] = { ...header, ...newComponents[newHeaderIndex] }
              }

              if (newBodyIndex !== -1) {
                newComponents[newBodyIndex] = { ...body, ...newComponents[newBodyIndex] }
              }

              if (newButtonsIndex !== -1) {
                newComponents[newButtonsIndex] = {
                  ...buttons,
                  ...newComponents[newButtonsIndex],
                  buttons: newComponents[newButtonsIndex].buttons?.map((newButton, index) => ({
                    ...(buttonsItems && buttonsItems[index]),
                    ...newButton,
                  })),
                }
              }

              log(`Updating template ${template.name} to service #${this.serviceId}`, 'info')
              return await whatsappBusinessTemplateResource.update(serverTemplate, {
                ...remotePick,
                components: newComponents,
                ...(providerType === 'gupshup' ? { idGupshup: template?.id } : {}),
              })
            }
            return serverTemplate
          }

          log(`Creating new template ${template.name} to service #${this.serviceId}`, 'info')

          return await whatsappBusinessTemplateResource.create({
            ...omit(template, 'rejected_reason', 'id'),
            namespace,
            rejectedReason: template.rejected_reason,
            serviceId: this.serviceId,
            accountId: this.service.accountId,
            name: template.name,
            language: template.language,
            components:
              template.category === 'AUTHENTICATION'
                ? authenticationComponents(template.components, template.language)
                : template.components,
            idGupshup: template.id,
            messageType: template.components.find((c) => ['HEADER', 'FOOTER', 'BUTTONS'].includes(c.type))
              ? 'interactive'
              : 'text_only',
            quality: (template?.quality ?? template?.quality_score?.score ?? 'UNKNOWN')?.toUpperCase(),
          })
        } catch (error) {
          reportError(error)
          return
        }
      },
      1,
    )

    log(`All templates service #${this.serviceId} are refreshed.`, 'info')
    return createdTemplates
  }

  async createTemplate(templateId: string, isRetry = false) {
    if (!templateId) {
      throw new Error(`TemplateId is required to create a new WABA template.`)
    }

    await this.connect()

    const template = await whatsappBusinessTemplateResource.findById(templateId)

    const getData = () => {
      if (this.service.data.providerType === 'gupshup') {
        return {
          ...template.dataValues,
          appId: this.service.internalData?.id,
          appToken: this.service.internalData?.appToken,
          partnerToken: this.service.internalData?.partnerToken,
        }
      }

      return {
        ...omitDeep(pick(template.dataValues, ['category', 'components', 'language', 'name']), ['params']),
        allow_category_change: true,
        ...(isRetry && {
          category: 'MARKETING',
        }),
      }
    }

    const data = getData()

    try {
      const response = await this.connector.createTemplate({
        ...data,
        allow_category_change: true,
      })

      await whatsappBusinessTemplateResource.update(template, {
        namespace: response.namespace,
        status: response.status,
        rejectedReason: response?.rejected_reason,
        category: response.category,
        idGupshup: this.service.data.providerType === 'gupshup' ? response?.id : null,
      })

      return response
    } catch (error) {
      if (
        !isRetry &&
        error.response?.data?.error?.message === '(#100) Param category must be one of {TRANSACTIONAL, OTP, MARKETING}.'
      ) {
        log(`Retrying create template ${templateId}.`, 'info')
        return this.createTemplate(templateId, true)
      }
      log('Error: %o\nPayload: %o', 'error', [error?.response ? error?.response?.data : error, data])
      if (this.service.data.providerType === 'gupshup') {
        return { error: true }
      }
      return error?.response?.data
    }
  }

  async deleteTemplate(templateId: string) {
    await this.connect()

    const template = await whatsappBusinessTemplateResource.findById(templateId)

    try {
      log(`Deleting template ${template.name} for serviceId #${this.serviceId}`, 'info')
      return await this.connector.deleteTemplate(
        template.name,
        this.service.internalData?.id,
        this.service.internalData?.appToken,
      )
    } catch (error) {
      log('Response: %o %s', 'error', [error?.response?.data ?? '', template.name])
      return error?.response?.data
    }
  }

  async webhook(data) {
    const { request } = data

    await this.getService()

    if (!this.service || this.service.archivedAt !== null) return true

    const isMeta = this.service.data.providerType === 'meta'

    const getType = () => {
      if (this.service.data.providerType === 'gupshup') {
        return gupShupGetWebhookType(request)
      }
      return getWebhookType(request, isMeta)
    }

    const type = getType()

    if (isMeta || this.service.settings?.cloudApi) {
      data.request = request?.entry?.[0]?.changes?.[0]?.value
    }

    switch (type) {
      case 'messages': {
        if (!milliseconds || milliseconds >= 999) {
          milliseconds = 1
          data.request.milliseconds = 1
        } else {
          data.request.milliseconds = milliseconds + 1
          milliseconds++
        }

        if (this.service.data.providerType === 'gupshup') {
          return gupShupMessagesWebhook(data)
        }

        return messagesWebhook(data)
      }
      case 'reaction': {
        if (this.service.data.providerType === 'gupshup') {
          return gupShupReactionsWebhook(data)
        }

        if (this.service.data.providerType === '360Dialog' || this.service.data.providerType === 'meta') {
          return reactionsWebhook(data)
        }

        return new Error('Invalid webhook reaction')
      }
      case 'enqueued': {
        if (this.service.data.providerType === 'gupshup') {
          return updateWhatsappMessageId(data)
        }

        return new Error('Invalid webhook enqueued')
      }
      case 'statuses':
        // Mantendo somente para retrocompatibilidade dos jobs da fila, no futuro remover daqui pois já está na rota '/whatsapp-business-webhook'
        await queueJobsDispatcher.dispatch<QueuedWhatsappBusinessStatusJob>('queued-whatsapp-business-status', data, {})
        return
      case 'template_status':
      case 'template_quality':
        const template = await whatsappBusinessTemplateResource.findOne({
          where: { idGupshup: request?.id ?? request?.payload?.id },
        })
        const newData = {
          ...(type === 'template_quality' && { quality: request?.new_quality_score ?? request?.payload?.quality }),
          ...(type === 'template_status' && {
            status: request?.new_status ?? request?.payload?.status,
            rejectedReason: request?.rejected_reason ?? request?.payload?.rejectedReason,
          }),
        }
        await whatsappBusinessTemplateResource.update(template, newData)
        return await this.autoPauseCampaigns(await whatsappBusinessTemplateResource.findById(template.id))
      case 'errors':
        return errorsWebhook(data)
      case 'unknow':
      default:
        return new Error('Invalid webhook type')
    }
  }

  async syncMessageFileById(messageId, options = {}) {
    const startedAt = new Date()
    const queue = processReceivedFileQueue(this.serviceId)
    return queue.run(async () => {
      const message = await messageResource.findById(messageId)

      const fileDownload = message?.data?.fileDownload

      if (
        !message ||
        !fileDownload?.startedAt ||
        (fileDownload?.isDownloading &&
          differenceInSeconds(new Date(), new Date(fileDownload.startedAt)) <
            config('whatsappBusinessWebhookProcessReceivedFileQueueTimeout') / 1000)
      )
        return

      const mediaId = message.data.mediaId

      if (mediaId && !message.isFromMe) {
        const systemFile = await fileResource.findOne({
          where: { name: mediaId, accountId: message.accountId },
          include: ['account'],
        })

        if (!systemFile) {
          try {
            await this.connect()

            const response = await this.connector.getMedia(mediaId)

            const mime = (await fileType.fromBuffer(response))?.mime || 'document'

            const file = {
              data: response,
              name: mediaId,
              mimetype: mime,
              isPtt: mime.indexOf('voice') != -1 || mime.indexOf('audio') != -1,
            }

            const createdFile = await fileResource.create({
              ...file,
              attachedId: messageId,
              attachedType: 'message.file',
              accountId: this.service.accountId,
            })

            await fileResource.createMessageThumbnail(createdFile, messageId, message.accountId)

            if (this.service.data.providerType === '360Dialog' && !this.service.settings.cloudApi) {
              await this.connector.deleteMedia(mediaId)
            }
          } catch (error) {
            console.trace(error.message)
            if (['400', '404'].includes(error.response?.status?.toString())) {
              return await messageResource.update(
                message,
                {
                  data: {
                    fileDownload: {
                      ...message.data.fileDownload,
                      isDownloading: false,
                      startedAt,
                      endedAt: new Date(),
                      error: true,
                    },
                  },
                },
                { ...options, mergeJson: ['data'] },
              )
            }
          }
        }
      }

      return messageResource.update(
        message,
        {
          data: {
            ...message.data,
            fileDownload: {
              ...message.data.fileDownload,
              isDownloading: false,
              startedAt,
              endedAt: new Date(),
            },
          },
        },
        options,
      )
    })
  }

  async contactExistsById(contactId: string) {
    await this.connect()
    const number = contactId.split('@')[0]
    return phoneNumberIsValid(number)
  }

  async destroy() {
    await this.getService()
    await serviceResource.update(this.service, {
      data: {
        ...this.service.data,
        status: {
          ...this.service.data.status,
          isStarting: false,
          isStarted: false,
          isConnected: false,
        },
      },
    })
  }

  async loadEarlierMessagesById() {
    return true
  }

  // eslint-disable-next-line complexity
  async sendAndSave(data, options = {}) {
    const { contactId, serviceId, message, userId, hsmId, interactiveMessage, scheduleId } = data
    const { fileId, hsmFileId, campaignId } = message

    let campaign = null
    if (campaignId) {
      campaign = await campaignResource.findById(campaignId)
    }

    data.hsm =
      hsmId &&
      (await whatsappBusinessTemplateResource.findById(hsmId, {
        ...(!hsmFileId && {
          include: [
            {
              model: 'fileExample',
              order: [['createdAt', 'DESC']],
              limit: 1,
            },
          ],
        }),
      }))

    const contact = await contactResource.findById(contactId, {
      include: [
        {
          model: 'currentTicket',
        },
        {
          model: 'account',
          attributes: ['settings'],
        },
      ],
    })

    const { accountId } = contact

    const origin = getOrigin(data)

    const messageData = await getMessageDataByOrigin(origin, data, contact)

    if (!messageData) throw new Error('Invalid message data')

    const findFileId = hsmId ? hsmFileId : fileId

    const file = findFileId && (await fileResource.findById(findFileId))

    const type = getSendMessageType(message, origin, file)

    const isHsm = ['hsmChat', 'hsmCampaign'].includes(origin)

    const account = await accountResource.findById(accountId)

    if (isHsm) validateHsmAvailability(account)

    const tunnelClosed = origin === 'normalChat' && (await isTunnelClosed(contact.id))

    log(' ==> messageData: %o', 'info', [messageData])

    const localMessageData: MessageInstance = {
      ...data,
      ...message,
      text: messageData.text || '',
      contact,
      type,
      account,
      accountId,
      contactId,
      serviceId,
      isFromMe: true,
      sent: true,
      visible: true,
      data: {
        ack: tunnelClosed ? 'error' : 0,
        ...(tunnelClosed && { error: 'HSM_WINDOW_ERROR' }),
        isNew: true,
        isFirst: !contact.lastMessageId,
        dontOpenTicket: data.dontOpenTicket || !!data.isFromSurvey,
        ...(interactiveMessage && {
          interactive: interactiveMessage.interactive,
        }),
        hsmParameters: messageData.parameters,
        ...(type === 'vcard' && {
          vcard: message.vcard,
        }),
        ...(scheduleId && { scheduleId }),
      },
      timestamp: new Date(),
      hsmId: data.hsmId || data.hsm?.id || null,
      hsmFileId: data.hsm ? hsmFileId || data.hsm.fileExample?.[0]?.id : null,
    }

    const localMessage = await messageResource.eventTransaction(async (eventTransaction) =>
      messageResource
        .transaction(async (transaction) => {
          const createdMessage = await messageResource.create(localMessageData, {
            transaction,
            eventTransaction,
          })

          if (tunnelClosed && contact.data.survey) {
            await answersResource.unflagSurveyFromContact(contact, {
              transaction,
              eventTransaction,
            })
          }

          return createdMessage
        })
        .then(async (createdMessage) => {
          if (fileId) {
            await fileResource.attachFileToMessage(
              {
                fileId,
                accountId,
                attachedId: createdMessage.id,
              },
              {
                eventTransaction,
              },
            )
          }
          return createdMessage
        }),
    )
    const isValidHsmParams = validateHsmParams(data.hsm, data?.parameters)

    if (localMessage?.data?.error || !isValidHsmParams) {
      localMessage.data.error = 'Parâmetros inválidos'
      return localMessage
    }

    await dispatchToSendMessageToBroker(
      {
        serviceId,
        payload: {
          fileId,
          hsmFileId,
          data,
          messageId: localMessage.id,
          userId,
          account,
          messageData,
          type,
          contact,
          fileEndedAt: new Date(),
          marketingIntegration: campaign?.marketingIntegration || false,
        },
        scheduleId,
      },
      {
        hashKey: contactId,
      },
    )

    if (isHsm) {
      await accountResource.incrementUsedHsm(account)
    }

    return localMessage
  }

  async processReceivedFile({ payload }) {
    const { request, accountId, messageId } = payload

    const { messages } = request

    await this.connect()

    await queuedAsyncMap(messages, async (message: any) => {
      const content = message[message.type]
      const mediaResponse = await this.connector.getMedia(content.id).catch(async (error) => {
        console.trace(`Failed to download media by id ${content.id}`, error)
        if (['400', '404'].includes(error.response?.status?.toString())) {
          return null
        }
        throw error.response?.data?.error || error
      })

      if (!mediaResponse) {
        log(`Media not found by id: ${content.id}`, 'error')
        return
      }

      await fileResource.eventTransaction(async (eventTransaction) => {
        await fileResource
          .getRepository()
          .transaction(async (transaction) => {
            try {
              const file = {
                data: mediaResponse,
                name: `${content.filename || content.id}${
                  message.type !== 'document'
                    ? message.type === 'voice'
                      ? '.oga'
                      : `.${content.mime_type.split('/')[1]}`
                    : ''
                }`,
                mimetype: content.mime_type,
                isPtt: message.type === 'voice',
              }

              const createdFile = await fileResource.create(
                {
                  ...file,
                  attachedId: messageId,
                  attachedType: 'message.file',
                  accountId,
                },
                { transaction, eventTransaction },
              )

              await messageResource.updateById(
                messageId,
                {
                  data: {
                    fileDownload: {
                      startedAt: new Date(),
                      endedAt: new Date(),
                      isDownloading: false,
                    },
                  },
                },
                { mergeJson: ['data'], transaction, eventTransaction },
              )

              return createdFile
            } catch (error) {
              log('Create file error: %o', 'error', [error])
              throw error
            }
          })
          .then(async (createdFile) => {
            // Coloquei fora da transaction pois deleteMedia não tem rollback e pode causar dead lock se demorar
            // Quando é host cloudApi, não precisa deletar midias
            if (this.service.data.providerType === '360Dialog' && !this.service.settings.cloudApi) {
              await this.connector.deleteMedia(content.id)
            }

            await fileResource.createMessageThumbnail(createdFile, messageId, accountId, {
              eventTransaction,
            })

            return createdFile
          })
      })
    })
  }

  async sendMessageToBroker({ payload }) {
    const { data, userId, messageData, contact, fileEndedAt, fileId, hsmFileId, marketingIntegration } = payload

    const messageId = payload.messageId || payload.localMessage?.id
    const type = payload.type || payload.mimetype // Retrocompatível

    await this.connect()

    const file = (fileId || hsmFileId) && (await fileTransformer(await fileResource.findById(fileId || hsmFileId)))

    const getBody = async () => {
      if (this.service.data.providerType === 'gupshup') {
        return gupshupGetMessageDataByType(
          type,
          data,
          messageData,
          contact,
          userId,
          this.service,
          file,
          marketingIntegration,
        )
      }

      return getMessageDataByType(
        type,
        data,
        messageData,
        contact,
        userId,
        this.service.data.providerType,
        file,
        marketingIntegration,
      )
    }

    const body = await getBody()

    if (!body) {
      reportError(`Invalid payload to send message: ${JSON.stringify(payload)}`)
      if (payload.data.scheduleId) {
        await scheduleResource.updateById(payload.data.scheduleId, {
          status: 'error',
          reason: 'INVALID_PAYLOAD',
        })
      }
      return
    }

    const mediaId = body?.[type]?.id

    const response = await this.connector.sendMessage(body, { serviceId: this.service.id })

    let currentMessage = null

    if (response.error || response.errors) {
      const messageError = response.error || response.errors[0]

      log('There was an error sending the message: \nBody: %o', 'error', [body])

      if (this.service.data.providerType === 'meta') {
        log(`Error messageId #${messageId}: %o`, 'error', [messageError])
      }
      // para não pegar estado anterior da fila
      currentMessage = await messageResource.findById(messageId)

      const localMessage = await messageResource.updateById(
        messageId,
        {
          data: {
            hsmParameters: messageData.parameters,
            ...(mediaId && { mediaId }),
            ack: 'error',
            error: messageError,
            ...(currentMessage.type === 'hsm' && !currentMessage.data.hsmDecremented && { hsmDecremented: true }),
          },
        },
        { mergeJson: ['data'] },
      )

      if (type === 'hsm' && !currentMessage.data.hsmDecremented) {
        const account = await accountResource.findById(localMessage.accountId)
        await accountResource.decrementUsedHsm(account)
      }

      if (payload.data.scheduleId) {
        await scheduleResource.updateById(payload.data.scheduleId, {
          status: 'error',
          reason: messageError?.code ?? 'SCHEDULE_UNKNOWN_REASON',
        })
      }

      return localMessage
    }

    log('Message sent to contact %s successfully! Message created %s', 'info', [contact?.id, messageId])

    if (!Array.isArray(response.messages) || !response.messages.length) {
      log(`Unknown response messages. Response: %o\n Payload: %o`, 'error', [response, body])
      throw new Error(JSON.stringify(response))
    }

    let mustUpdateHsmDecrementedToFalse = false

    if (!currentMessage || (type === 'hsm' && !currentMessage.account)) {
      currentMessage = await messageResource.findById(messageId, {
        include: ['account'],
      })
    }

    if (type === 'hsm') {
      if (currentMessage.data.hsmDecremented) {
        mustUpdateHsmDecrementedToFalse = true
        await accountResource.incrementUsedHsm(currentMessage.account)
      }
    }

    const getIdFromService = (): string => {
      const messages = response?.messages?.[0]

      if (this.service.data.providerType === 'gupshup') {
        return messages?.messageId
      }

      return messages?.id
    }

    currentMessage = await messageResource.updateById(
      messageId,
      {
        idFromService: getIdFromService(),
        data: {
          hsmParameters: messageData.parameters,
          ...(mediaId && { mediaId }),
          ...(mustUpdateHsmDecrementedToFalse && { hsmDecremented: false }),
          fileDownload: {
            ...currentMessage.data.fileDownload,
            isDownloading: false,
            endedAt: fileEndedAt,
          },
        },
      },
      {
        mergeJson: ['data'],
      },
    )

    const updatedContact = await contactResource.updateById(currentMessage.contactId, {
      lastMessageAt: currentMessage.timestamp,
      lastMessageId: currentMessage.id,
    })

    // Precisa utilizar o updatedContact porque ele sempre estará atualizado
    if (isChargedMessage(updatedContact || contact)) {
      await contactResource.updateById(
        (updatedContact || contact).id,
        {
          data: {
            lastChargedMessage: new Date(),
          },
        },
        { mergeJson: ['data'] },
      )
    }

    return currentMessage
  }

  async sendReactionByMessage(messageId: string, reactionEmojiRendered: string): Promise<boolean> {
    const message = await messageResource.findById(messageId)

    if (!message?.idFromService) {
      throw new Error('whatsappBusiness.sendReactionByMessage: invalid message')
    }

    await this.connect()

    const getBody = async () => {
      if (this.service.data.providerType === 'gupshup') {
        return gupshupGetReactionData(message, reactionEmojiRendered)
      }

      if (this.service.data.providerType === '360Dialog' || this.service.data.providerType === 'meta') {
        return getReactionData(message, reactionEmojiRendered)
      }

      throw new Error('whatsappBusiness.sendReactionByMessage: invalid provider type')
    }

    const body = await getBody()

    const response = await this.connector.sendMessage(body)

    if (!response) {
      throw new Error('whatsappBusiness.sendReactionByMessage: invalid response')
    }

    const action = reactionEmojiRendered ? 'react' : 'unreact'

    let idFromService = null

    if (this.service.data.providerType === 'gupshup') {
      idFromService = response.messages[0].messageId
    }

    if (this.service.data.providerType === '360Dialog' || this.service.data.providerType === 'meta') {
      idFromService = response.messages[0].id
    }

    if (!idFromService) {
      throw new Error('whatsappBusiness.sendReactionByMessage: invalid idFromService')
    }

    const saveReaction: Reaction = {
      timestamp: Date.now(),
      action,
      idFromService,
      emoji: reactionEmojiRendered ? reactionEmojiRendered : '',
      parentMessageId: message.id,
      isFromMe: true,
      accountId: this.service.accountId,
      serviceId: this.service.id,
      contactId: message.contactId,
    }

    const result = await syncReactions(saveReaction)

    if (typeof result === 'string') {
      throw new Error(result)
    }

    return true
  }

  async revokeReactionByMessage(messageId: string): Promise<boolean> {
    const message = await messageResource.findById(messageId, {
      attributes: ['reactionParentMessageId'],
    })

    if (!message?.reactionParentMessageId) {
      throw new Error('whatsappBusiness.revokeReactionByMessage: invalid message')
    }

    return this.sendReactionByMessage(message.reactionParentMessageId, '')
  }

  async autoPauseCampaigns(template) {
    const autoPauseModes = {
      LOW_HEALTH: 'low-health',
      MEDIUM_HEALTH: 'medium-health',
      DISABLED: 'disabled',
    }

    const templateQuality = {
      LOW: 'LOW',
      MEDIUM: 'MEDIUM',
    }

    const account = await accountResource.findById(template.accountId)
    const autoPauseMode = account?.settings?.campaign?.['auto-pause-mode']

    const shouldPauseForMedium =
      autoPauseMode === autoPauseModes.MEDIUM_HEALTH && template.quality === templateQuality.MEDIUM
    const shouldPauseForLow = autoPauseMode === autoPauseModes.LOW_HEALTH && template.quality === templateQuality.LOW

    if (shouldPauseForMedium || shouldPauseForLow) {
      const campaigns = await this.getCampaigns(template)

      const reason = shouldPauseForMedium ? 'medium health' : 'low health'

      await queuedAsyncMap(campaigns, async (campaign) => {
        if (!['paused', 'canceled', 'done', 'import_error', 'error'].includes(campaign.status)) {
          log(`Pausing campaign #${campaign.id} for ${reason} template...`)
          await campaignResource.internalUpdate(campaign, { status: 'paused' })
        }
      })
    }
  }
}
