import Qs from 'qs'
import { omit, capitalize } from 'lodash'
import { addMinutes, format, differenceInMinutes } from 'date-fns'
import { Container } from 'typedi'
import config from '../../../../../core/config'
import contactResource from '../../../../../core/resources/contactResource'
import whatsappBusinessTemplateResource from '../../../../../core/resources/whatsappBusinessTemplateResource'
import userResource from '../../../../../core/resources/userResource'
import serviceResource from '../../../../../core/resources/serviceResource'
import { ContactInstance, ContactOrigin } from '../../../../../core/dbSequelize/models/Contact'
import { verifyIsNumberOrEmail } from '../../../../../core/utils/validator/validateNumberFormat'
import { WhatsappBusinessTemplateInstance } from '../../../../../core/dbSequelize/models/WhatsappBusinessTemplate'
import { ServiceInstance } from '../../../../../core/dbSequelize/models/Service'
import { AccountInstance } from '../../../../../core/dbSequelize/models/Account'
import { MessageInstance } from '../../../../../core/dbSequelize/models/Message'
import Logger from '../../../../../core/services/logs/Logger'
import reportError from '../../../../../core/services/logs/reportError'
import HsmLimitExceededError from '../../../../../core/utils/error/HsmLimitExceededError'
import getTimezoneMinutesOffset from '../../../../../core/utils/getTimezoneMinutesOffset'
import comparableIdFromService from '../../../../../core/utils/whatsapp/comparableIdFromService'
import wait from '../../../../../core/utils/wait'
import { HttpClient } from '../../../../../core/services/httpClient/HttpClient'
import BadRequestHttpError from '../../../../../core/utils/error/BadRequestHttpError'

const logger = Container.get(Logger)

export const log = logger.log

const getHttpClient = () => Container.get(HttpClient)
export const httpClient = getHttpClient()

export const getWebhookInGatewayByUrl = async (webhookUrl, driverId?) => {
  const response = await httpClient.get(`${config('driversGatewayUrl')}/webhooks`, {
    paramsSerializer: (params) => Qs.stringify(params),
    params: {
      where: {
        webhookUrl,
        type: 'whatsapp',
        ...(driverId && { driverId }),
      },
    },
  })

  if (response.data.length > 1) {
    log('Duplicated webhook on gateway\nResponse: %', 'warn', [response.data])
  }

  return response.data[0]
}

export const getMediaType = (type) => {
  switch (type) {
    case 'audio':
      return 'audio'
    case 'image':
      return 'image'
    case 'video':
      return 'video'
    case 'file':
      return 'document'
    default:
      return 'unknow'
  }
}

export const createOrUpdateDataInGateway = async (serviceId, driverId) => {
  try {
    if (config('disableWabaWebhookUrlSet')) return

    const response = await httpClient.get(`${config('driversGatewayUrl')}/webhooks`, {
      params: {
        where: {
          driverId,
          type: 'whatsapp',
        },
        limit: 1,
      },
      paramsSerializer: (params) => Qs.stringify(params),
    })

    if (response.data[0]) {
      await httpClient.put(`${config('driversGatewayUrl')}/webhooks/${response.data[0].id}`, {
        type: 'whatsapp',
        driverId,
        webhookUrl: `${config('publicUrl')}/whatsapp-business-webhook/${serviceId}`,
      })
      return
    }

    await httpClient.post(`${config('driversGatewayUrl')}/webhooks`, {
      type: 'whatsapp',
      driverId,
      webhookUrl: `${config('publicUrl')}/whatsapp-business-webhook/${serviceId}`,
    })
    return
  } catch (error) {
    reportError(error)
  }
}

export const isChargedMessage = (contact: ContactInstance): boolean =>
  !!(
    !contact.data.lastChargedMessage ||
    differenceInMinutes(new Date(), new Date(contact.data.lastChargedMessage)) >= 1440
  )

export const getSendMessageType = (message, origin, file?) =>
  message.vcard
    ? 'vcard'
    : message.type === 'sticker'
    ? 'sticker'
    : origin === 'normalChat'
    ? getTypeByMimetype(file?.mimetype)
    : origin === 'interactive'
    ? 'interactive'
    : 'hsm'

export const getMessageType = (message: { type: string; errors?: Array<any> }) => {
  // O código de erro 501 ocorre quando o cliente envia um documento com legenda para a plataforma
  if ((message?.errors || []).some((error) => error.code === 501)) {
    return 'caption'
  }
  const type = message.type
  switch (type) {
    case 'text':
      return 'chat'
    case 'contacts':
      return 'vcard'
    case 'location':
      return 'location'
    case 'audio':
      return 'audio'
    case 'document':
      return 'document'
    case 'image':
      return 'image'
    case 'video':
      return 'video'
    case 'voice':
      return 'audio'
    case 'sticker':
      return 'sticker'
    case 'unknow':
      return 'unknow'
    case 'button':
      return 'chat'
    case 'interactive':
      return 'chat'
    default:
      log(`Invalid type detected: ${type}`, 'warn')
      return 'chat'
  }
}

export const getTypeByMimetype = (mimetype: string) => {
  if (mimetype) {
    if (mimetype.indexOf('audio') !== -1) {
      return 'audio'
    }

    if (mimetype.indexOf('image') !== -1) {
      return 'image'
    }

    if (mimetype.indexOf('video') !== -1) {
      return 'video'
    }

    return 'document'
  }
  return 'chat'
}

export const getDateByTimestamp = (timestamp) => {
  if (timestamp) {
    if (+timestamp <= **********) {
      return new Date(+timestamp * 1000)
    }
    return new Date(+timestamp)
  }
  return new Date()
}

export const getWebhookType = (
  request,
  isMeta = false,
): 'messages' | 'statuses' | 'reaction' | 'errors' | 'template_quality' | 'template_status' | 'unknow' => {
  if (isMeta || request?.object === 'whatsapp_business_account') {
    if (request.entry[0].changes[0].value?.messages?.[0]?.type === 'reaction') return 'reaction'
    if (request.entry[0].changes[0].value?.messages) return 'messages'
    if (request.entry[0].changes[0].value?.statuses) return 'statuses'
    if (request.entry[0].changes[0].value?.errors) return 'errors' // @TODO validar formato
    return 'unknow'
  }

  if (request?.code) return 'errors'
  if (request?.statuses) return 'statuses'
  if (request?.messages) return 'messages'
  if (request?.new_quality_score) return 'template_quality'
  if (request?.new_status) return 'template_status'
  return 'unknow'
}

export const getSendersContact = (contacts) => {
  if (!contacts) return
  return contacts.map((contact) => ({
    name: contact.profile.name,
    number: contact.wa_id,
  }))
}

export const createContactIfNotExist = async (sender, serviceId, accountId): Promise<ContactInstance> => {
  if (!sender) return

  const contactExists = await contactResource.findOne({
    where: {
      accountId,
      serviceId,
      idFromService: comparableIdFromService(sender.number, 'whatsapp-business'),
      archivedAt: null,
    },
    include: ['account'],
  })

  if (!contactExists) {
    return contactResource.create(
      {
        idFromService: sender.number,
        name: sender.name || sender.number,
        visible: true,
        serviceId,
        accountId,
        number: sender.number,
        data: { isOriginal: true, validNumber: sender.number },
        origin: ContactOrigin.Driver,
      },
      {
        include: ['account'],
      },
    )
  }

  // Contato sem nome ou com nome diferente do que está vindo do whatsapp
  if (!contactExists.name || (sender.name && sender.name !== contactExists.name)) {
    log('Updating contact name from "%s" to "%s" for contact id "%s".', 'info', [
      contactExists.name,
      sender.name || sender.number,
      contactExists.id,
    ])

    await contactResource.update(contactExists, {
      name: sender.name || sender.number,
    })
  }

  if (!contactExists.data?.isOriginal) {
    return contactResource.update(
      contactExists,
      {
        data: {
          isOriginal: true,
          number: sender.number,
          validNumber: sender.number,
        },
      },
      { mergeJson: ['data'] },
    )
  }

  return contactExists
}

export const validatePayload = (request) => {
  if (!request) {
    log('Error: Request undefined', 'error')
    return false
  }

  if (!request.messages) {
    log('Error: Message undefined! payload: %o', 'error', [request])
    return false
  }

  if (Array.isArray(request.messages) && request.messages.length) {
    let isValid = true
    request.messages.forEach((msg) => {
      if (msg.errors || msg.type === 'unknown') {
        // O código de erro 501 ocorre quando o cliente envia um documento com legenda para a plataforma
        if (!(msg?.errors || []).some((error) => error.code === 501)) {
          isValid = false
          log('Error: %o\nPayload: %o', 'error', [msg.errors || msg, request])
        }
      }
    })
    return isValid
  }

  return true
}

export const getVcard = (contact) => {
  if (!contact.phones.length) return null

  const first_name = contact.name.first_name || ''
  const last_name = contact.name.last_name || ''
  const phone = contact.phones[0].wa_id || contact.phones[0].phone.replace(/[^0-9]/g, '') || ''
  const wa_id = phone
  const phone_number = contact.phones[0].phone.replace(/[^0-9]/g, '') || contact.phones[0].wa_id || ''
  return `BEGIN:VCARD\nVERSION:3.0\nN:${last_name};${first_name};;;\nFN:${first_name} ${last_name}\nitem1.TEL;waid=${wa_id}:${phone_number}\nitem1.X-ABLabel:Mobile\nEND:VCARD`
}

// Verifica se o tunel de 24h está fechado
export const isTunnelClosed = async (contactId) => {
  //Tempo adicional para evitar pegar o tunnel fechado, no caso da primeira mensagem do chamado
  await wait(1000)
  const contact = await contactResource.findById(contactId, {
    attributes: ['lastContactMessageAt', 'lastMessageAt'],
  })
  const last = new Date(contact?.lastContactMessageAt).getTime()
  const now = new Date().getTime()
  const LATER_THEN_24_HOURS = now - last > 86400 * 1000

  if (!contact.lastMessageAt) {
    log('Tunnel was closed! Because lastMessageAt is null for contact %s', 'warn', [contactId])
    return true
  }

  if (LATER_THEN_24_HOURS) {
    log('Last tunnel was opened at "%s" and closed at "%s" for contact %s. Current time: "%s"', 'warn', [
      new Date(last).toISOString(),
      new Date(last + 86400 * 1000).toISOString(),
      contactId,
      new Date(now).toISOString(),
    ])
    return true
  }

  return false
}

export const getOrigin = (data) => {
  if (data.hsm || data.hsmId) return 'hsmChat'
  if (data.origin === 'campaign') return 'hsmCampaign'
  if (data.interactiveMessage) return 'interactive'
  return 'normalChat'
}

// interpola o texto com os parametros
// altera parametros com contactName se existir {{contact_name}}
export const interpolateParameters = (text, parameters: String[] = [], contactName) => {
  const matches = text.match(/(\{{\d{0,}\}\})/g)
  if (!parameters.length || !matches) return { text }
  const params = parameters.filter((p) => String(p).trim()).length
    ? parameters.map((param, idx) => {
        if (param == '{{contact_name}}') param = contactName
        text = text.split(matches[idx]).join(param)
        return param
      })
    : []
  return { text, parameters: params }
}

/**
 * Retorna a lista de parametros para envio de HSM
 *
 * @param contact object
 * @param parameters array
 * @returns array
 */
const getHSMParameters = (contact, parameters = []) => {
  if (parameters.length === 0) {
    return []
  }

  return parameters.map((params) => {
    params.parameters = params.parameters.map((p) => {
      if (p.type === 'text') p.text = p.text?.replace('{{contact_name}}', contact.name)
      return p
    })
    return params
  })
}

const getGreeting = (date: Date): string => {
  const hours = Number(format(date, 'H'))
  if (hours >= 6 && hours < 12) return 'bom dia'
  if (hours >= 12 && hours < 18) return 'boa tarde'
  return 'boa noite'
}

const interpolate = (text, params) => {
  const replaceValueMap = {
    '{{contact_name}}': params.contactName,
    '{{protocol_number}}': params.protocolNumber,
    '{{contact_number}}': params.contactNumber,
    '{{nome_contato}}': params.customContactName,
    '{{nome_contato_digisac}}': params.contactDigisacName,
    '{{nome_contato_agenda}}': params.contactPhonebookName,
    '{{nome_contato_whatsapp}}': params.contactName,
    '{{numero_protocolo}}': params.protocolNumber,
    '{{numero_contato}}': params.contactNumber,
    '{{saudacao}}': params.greeting,
    '{{saudacao_maiuscula}}': params.capitalizedGreeting,
  }

  let newText = text || ''

  Object.keys(replaceValueMap).forEach((searchValue) => {
    if (!replaceValueMap[searchValue]) return

    const regex = new RegExp(searchValue, 'g')

    if (searchValue === '{{contact_name}}') {
      replaceValueMap[searchValue] = replaceValueMap[searchValue].slice(0, 15).split(' ', 1)
    }

    newText = newText.replace(regex, replaceValueMap[searchValue])
  })

  return newText
}

export const getMessageDataByOrigin = async (origin, data, contact): Promise<any> => {
  switch (origin) {
    case 'hsmChat': {
      const hsm = data.template || data.hsm

      return {
        hsm,
        parameters: getHSMParameters(contact, data.parameters),
        phoneNumber: contact.idFromService,
      }
    }
    case 'hsmCampaign': {
      const hsm =
        data.hsm ||
        (await whatsappBusinessTemplateResource.findById(
          data.extraOptions.hsm?.id || data.extraOptions.hsm || data.hsmId, // retrocompatibilidade para deploy, futuramente remover
        ))
      return {
        message: {
          hsm,
          ...interpolateParameters(
            hsm.templateText,
            data.hsmParameters ? Object.values(data.hsmParameters) : [],
            contact.name,
          ),
          fileId: data.message.fileId,
          fileTemplate: data.extraOptions.fileTemplate,
        },
        phoneNumber: contact.idFromService,
      }
    }
    case 'normalChat':
      return {
        text: data.message.text,
        fileId: data.message.fileId,
        phoneNumber: contact.idFromService,
      }
    case 'interactive': {
      const customContactName =
        contact?.internalName || contact?.name || contact?.alternativeName || contact.data.number || ''
      const contactNumber = contact?.data?.number || ''
      const contactDigisacName = contact?.internalName || contactNumber
      const contactPhonebookName = contact?.alternativeName || contactNumber
      const accountTimezoneOffset = getTimezoneMinutesOffset(contact?.account?.settings?.timezone)
      const zonedDate = addMinutes(new Date(), accountTimezoneOffset)
      const greeting = getGreeting(zonedDate)
      const capitalizedGreeting = capitalize(getGreeting(zonedDate))

      const replaceValues = {
        contactName: contact?.name || contactNumber,
        protocolNumber: contact?.currentTicket?.protocol,
        contactNumber: contact?.data?.number,
        customContactName,
        contactDigisacName,
        contactPhonebookName,
        greeting,
        capitalizedGreeting,
      }
      const { interactive } = data.interactiveMessage

      const newInteractive = interactive

      newInteractive.body.text = interpolate(interactive.body.text, replaceValues)

      if (interactive.footer?.text?.trim().length > 0) {
        newInteractive.footer.text = interpolate(interactive.footer.text, replaceValues)
      }

      if (interactive.header?.text?.trim().length > 0) {
        newInteractive.header.text = interpolate(interactive.header.text, replaceValues)
      }

      return { ...data.interactiveMessage, interactive: newInteractive }
    }
    default:
      return null
  }
}

export const getTypeOfMessageByMimetype = (type) =>
  ({
    chat: 'text',
    image: 'media',
    document: 'media',
    audio: 'media',
    sticker: 'media',
    video: 'media',
    hsm: 'hsm',
    vcard: 'contacts',
    interactive: 'interactive',
  }[type] || 'unknow')

export const getMessageDataByType = async (
  type,
  data,
  messageData,
  contact,
  userId,
  providerType,
  file,
  marketingIntegration,
) => {
  const { mediaId } = messageData

  const link = file?.url || messageData.link // Retrocompatível

  const typeOfMessage = getTypeOfMessageByMimetype(type)

  // dependendo do jeito/momento em que a requisição é feita pelo front pode não ter validNumber ainda
  const phoneNumber =
    contact?.data?.validNumber ||
    messageData.phoneNumber ||
    data.contact?.data?.validNumber ||
    data.contact?.data?.number

  const hasCaption = ['document', 'video', 'image'].includes(type) && messageData.text

  const hasFilename = ['document'].includes(type)

  switch (typeOfMessage) {
    case 'text': {
      const text = await getTextWithUser(data, userId)
      return { to: phoneNumber, type: 'text', text: { body: text } }
    }
    case 'media':
      return {
        to: phoneNumber,
        type,
        [type]: {
          ...(hasCaption && { caption: messageData.text }),
          ...(hasFilename && { filename: file.name }),
          ...(mediaId ? { id: mediaId } : { link }),
        },
      }
    case 'hsm':
      const components = messageData.parameters

      if (link || mediaId) {
        const index = components.findIndex((c) => c.type === 'header')

        const position = data.hsm.components.findIndex((c) => c.type === 'HEADER')
        const format = data.hsm.components[position]?.format?.toLowerCase()

        // Caption não é suportado para hsm
        // https://developers.facebook.com/docs/whatsapp/cloud-api/reference/messages#components-object
        components[index].parameters[0][format][mediaId ? 'id' : 'link'] = mediaId || link
      }

      if (data?.hsm?.category === 'AUTHENTICATION') {
        const index = components.findIndex((c) => c.type === 'body')
        const codeText = components[index].parameters[0].text

        components.push({
          type: 'button',
          sub_type: 'url',
          index: 0,
          parameters: [
            {
              type: 'text',
              text: codeText,
            },
          ],
        })
      }

      return {
        to: phoneNumber,
        marketingIntegration: marketingIntegration,
        type: 'template',
        template: {
          ...(providerType !== 'meta' && {
            namespace: data.hsm.namespace,
          }),
          name: data.hsm.name,
          language: {
            policy: 'deterministic',
            code: data.hsm.language,
          },
          components,
        },
      }
    case 'contacts': {
      const { vcard } = data.message

      const removeCharacters = vcard.replace(/(;)/gm, '')
      const formatBreak = removeCharacters.split('\n')

      const vcardToJson = formatBreak.reduce((acc, cur) => {
        const string = cur.split(':')
        const types = ['FN', '.X-ABLabel', '.TEL']

        const isValid = types.some((type) => string[0].search(type) >= 0)

        if (!isValid) return acc

        let type = null
        const obj = {}

        if (string[0].search('FN') >= 0) type = 'formatted_name'
        if (string[0].search('.TEL') >= 0) type = 'phone'
        if (string[0].search('.X-ABLabel') >= 0) type = 'type'

        obj[type] = string[1]

        return { ...acc, ...obj }
      }, {})

      const { formatted_name = '', phone = '', type = 'Mobile' } = vcardToJson

      const contact = {
        name: {
          formatted_name,
        },
        phones: [
          {
            phone,
            type,
          },
        ],
      }
      return { to: phoneNumber, type: 'contacts', contacts: [contact] }
    }
    case 'interactive': {
      const { interactive } = messageData
      return {
        recipient_type: 'individual',
        to: phoneNumber,
        type: 'interactive',
        interactive: {
          ...omit(interactive, ['header']),
          ...(interactive.header &&
            (interactive.header?.text?.trim()?.length || mediaId || link) && {
              header: {
                ...interactive.header,
                [interactive.header.type]:
                  interactive.header.type === 'text'
                    ? interactive.header.text
                    : {
                        ...(interactive.header.type === 'document' && {
                          filename:
                            file.name ||
                            messageData.file?.fileName ||
                            messageData.file?.name ||
                            data.message?.file?.name,
                        }),
                        ...(mediaId ? { id: mediaId } : { link }),
                      },
              },
            }),
        },
      }
    }
    default:
      return null
  }
}

export const getReactionData = async (message: MessageInstance, reactionEmojiRendered: string) => {
  const contact = await contactResource.findById(message.contactId, {
    attributes: ['data', 'idFromService'],
  })

  // dependendo do jeito/momento em que a requisição é feita pelo front pode não ter validNumber ainda
  const phoneNumber = contact?.data?.validNumber || contact?.data?.number || contact?.idFromService

  return {
    to: phoneNumber,
    type: 'reaction',
    reaction: {
      message_id: message.idFromService,
      emoji: reactionEmojiRendered,
    },
  }
}

export const getTextWithUser = async (data, userId) => {
  if (data.settings.dontSendUserName || !data.settings.userNameInMessages || !userId) {
    return data.message.text
  }

  const user = await userResource.findById(userId)
  return `*${user.name}*:
${data.message.text}`
}
/**
 * Make webhook url
 *
 * @param service object
 * @returns string
 */
const webhookUrlForService = (service) => {
  const useCloudApi = service.settings?.cloudApi
  const useSandbox = service.settings?.tokenSandBox360

  const url = useCloudApi ? 'https://waba-v2.360dialog.io' : 'https://waba.360dialog.io'
  const baseUrl = useSandbox ? 'https://waba-sandbox.360dialog.io' : url

  return baseUrl + '/v1/configs/webhook'
}

export const setWebhook = async (service) => {
  if (config('disableWabaWebhookUrlSet')) return

  log(`Setting webhook ${config('publicUrl')}/whatsapp-business-webhook/${service.id}...`)

  const webhookUrl = webhookUrlForService(service)
  const token = service.settings?.tokenSandBox360 || service.internalData.token

  await httpClient
    .post(
      webhookUrl,
      {
        url: `${config('publicUrl')}/whatsapp-business-webhook/${service.id}`,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'D360-Api-Key': token,
        },
      },
    )
    .then(async () => {
      await serviceResource.update(
        service,
        {
          data: {
            status: {
              ...service.data.status,
              isStarting: false,
              isStarted: true,
              isConnected: true,
            },
          },
        },
        { mergeJson: ['data'] },
      )
    })
    .catch(async (error) => {
      if (error?.response?.status === 401) {
        await serviceResource.update(
          service,
          {
            data: {
              status: {
                ...service.data.status,
                isStarting: false,
                isStarted: false,
                isConnected: false,
              },
            },
          },
          { mergeJson: ['data'] },
        )

        throw new Error('Invalid token')
      }

      await serviceResource.update(
        service,
        {
          data: {
            status: {
              ...service.data.status,
              isStarting: false,
              isStarted: true,
              isConnected: false,
            },
          },
        },
        { mergeJson: ['data'] },
      )

      throw error
    })
}

export const health = async (service: ServiceInstance, handleNotificationWabaHealth: any) => {
  const token = service?.internalData?.token
  if (!token) {
    log(`There is no access token for waba service name ${service?.name}`)
    return
  }

  const getParams = () => {
    if (service?.data?.providerType === 'meta') {
      return {
        url: `https://graph.facebook.com/v21.0/${service?.data?.businessId}?fields=health_status`,
        header: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      }
    }
    return {
      url: 'https://waba-v2.360dialog.io/health_status',
      header: {
        'Content-Type': 'application/json',
        'D360-Api-Key': token,
      },
    }
  }

  const params = getParams()
  await httpClient
    .get(params?.url, {
      headers: params?.header,
    })
    .then(async (response) => {
      const status = response?.data?.health_status?.can_send_message
      if (service?.health?.status !== status) {
        const info =
          response?.data?.health_status?.entities?.find((entity) => entity?.additional_info)?.additional_info || []
        const errors = response?.data?.health_status?.entities
          ?.find((entity) => entity?.errors)
          ?.errors?.map((error) => ({
            description: error?.error_description,
            solution: error?.possible_solution,
          }))

        await serviceResource.updateById(service.id, { health: { status, info, errors } })

        if (status !== 'AVAILABLE') {
          await handleNotificationWabaHealth(service.accountId, status)
        }
      }
    })
}

// Receber o número nesse formato:
// 55*********** ou 55**********
export const getFullNumber = (number: string) => {
  // Generate full number
  switch (number?.length) {
    // full
    case 13: {
      return number
    }
    // without 9
    case 12: {
      return `${number.slice(0, 4)}9${number.slice(4)}`
    }
    // without 55
    case 11: {
      return `55${number}`
    }
    // without 55 and 9
    case 10: {
      return `55${number.slice(0, 2)}9${number.slice(2)}`
    }
    default: {
      return number
    }
  }
}

// Remove espaços e caracteres especiais
export const parseNumber = (number) => String(number || '').replace(/\D/g, '')

// Receber o número nesse formato:
// 55*********** ou 55********** ou *********** ou **********
export function getPossibleNumbers(number) {
  const fullNumber = getFullNumber(number)

  // Generate all number possibilities
  const number1 = fullNumber // full
  const number2 = fullNumber.slice(0, 4) + fullNumber.slice(5) // without 9
  const number3 = fullNumber.substr(2) // without 55
  const number4 = fullNumber.slice(2, 4) + fullNumber.slice(5) // without 55 and 9

  return [number1, number2, number3, number4]
}

// Receber o número nesse formato:
// 55*********** ou 55********** ou +55*********** ou +55**********
export const formatNumberToSendMessage = (providerType: string, number: string): string => {
  if (!number) return
  const hasPlus = number.startsWith('+')

  switch (providerType) {
    case 'positus':
      if (!hasPlus) number = `+${number}`
      break
    case '360Dialog':
      if (hasPlus) number = number.substr(1)
      break
  }
  return number
}

/**
 * Formatação dos componentes para serem enviados
 *
 * @param data object
 * @returns object
 */
export const formatTemplateCloudApi = (data) => {
  const body = data?.components.find((component) => component.type === 'BODY')
  const buttons = data?.components.find((component) => component.type === 'BUTTONS')
  const footer = data?.components.find((component) => component.type === 'FOOTER')
  const header = data?.components.find((component) => component.type === 'HEADER')

  const payloadComponets = []

  if (buttons) {
    payloadComponets.push(buttons)
  }

  if (body && data.category === 'AUTHENTICATION') {
    payloadComponets.push({
      type: 'BODY',
      add_security_recommendation: body.add_security_recommendation,
    })
  } else {
    payloadComponets.push(body)
  }

  if (footer && data.category !== 'AUTHENTICATION') {
    payloadComponets.push(footer)
  }

  if (footer && data.category === 'AUTHENTICATION' && footer?.active_code_expiration_minutes) {
    payloadComponets.push({
      type: 'FOOTER',
      code_expiration_minutes: footer?.code_expiration_minutes,
    })
  }

  if (header) {
    payloadComponets.push(header)
  }

  data.components = payloadComponets

  return data
}

const httpStatusCodesOfErros = [400, 401, 403, 404, 405, 412, 420, 429, 500, 504]

export const hasError = (providerType, resData) => {
  if (providerType === 'positus') {
    return (
      (resData.contacts &&
        resData.contacts.contacts &&
        Array.isArray(resData.contacts.contacts) &&
        resData.contacts.contacts.length &&
        resData.contacts.contacts.some((c) => c.status !== 'valid')) ||
      (!resData?.meta?.success && httpStatusCodesOfErros.includes(resData?.meta?.http_code))
    )
  }
  if (providerType === '360Dialog') {
    return (
      (resData.errors && Array.isArray(resData.errors) && resData.errors.length) ||
      (!resData?.meta?.success && httpStatusCodesOfErros.includes(resData?.meta?.http_code))
    )
  }
  return false
}

export const handleError = async (providerType, resData, data = {}, showLog = true) => {
  let status = providerType === 'positus' ? resData.contacts?.contacts?.[0]?.status : resData?.errors?.[0]?.code

  if (['invalid', 'failed'].includes(status)) status = 1013

  if (showLog) {
    log('Whatsapp Business Error!\nPayload: %o \nResponse: %o', 'error', [data, resData])
  }

  return { error: status || 'UNKNOWN' }
}

export const phoneNumberIsValid = (number) => {
  return verifyIsNumberOrEmail(null, null, number)
}

export const validateHsmAvailability = (account: AccountInstance) => {
  const {
    plan: { services, hsmLimit, hsmUsedLimit },
  } = account

  if ((account.settings.flags || {})?.['disable-hsm-limit']) return true

  const hsmAvailable = hsmLimit - hsmUsedLimit

  const hasHsmAvailable = services['whatsapp-business'] && hsmAvailable > 0

  if (!hasHsmAvailable) throw new HsmLimitExceededError('Hsm limit exceeded')
}

const hasBody = (hsm: WhatsappBusinessTemplateInstance) => {
  if (!hsm) return

  const hsmBody = hsm.components.filter((item) => item.type === 'BODY')

  if (hsmBody) return hsmBody.shift().text
}

const hasBodyParameters = (parameters) => {
  if (parameters && parameters.length) {
    const hsmBodyParam = parameters.filter((item) => item.type === 'body')

    if (hsmBodyParam.length) {
      return hsmBodyParam.shift().parameters
    }
  }
}

const validateParams = (hsm: string, hsmParameters?: any) => {
  if (!hsm) return false

  const count = (hsm.match(/{{/g) || []).length

  if (count === 0) return true

  if (!hsmParameters || count !== hsmParameters.length) return false

  return !hsmParameters.some((param) => !param?.text || !param?.type || !param?.type.trim())
}

export const validateHsmParams = async (hsm: WhatsappBusinessTemplateInstance, parameters) => {
  // extrai o complemento BODY do template HSM
  const hsmBodyMessage = hasBody(hsm)

  if (!hsmBodyMessage) return true

  const paramsBodyMesssage = hasBodyParameters(parameters)

  // validação do parâmetros com o template cadastrado
  return validateParams(hsmBodyMessage, paramsBodyMesssage)
}

interface Component {
  type: string
  format: string
  text: string
}

export const findParamsInComponent = (components: [Component], type: string, format: string): any[] => {
  const component = components?.find((item) => item.type === type && item.format === format)
  return component?.text?.match(/{{(100|[0-9][0-9]?)}}/g) || []
}

export const authDialog = async (username: string, password: string) => {
  const baseUrl = config('hub360Api').baseUrl || ''
  const result = await httpClient
    .post(
      `${baseUrl}/token`,
      {
        username: username,
        password: password,
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      },
    )
    .then((res) => res.data)
  return `Bearer ${result?.access_token}`
}

export const mmliteStatusDialog = async (service: ServiceInstance) => {
  const baseUrl = config('hub360Api').baseUrl || ''
  const hub360Api = config('hub360Api') ?? false
  const partnerId = hub360Api.partnerId || ''
  const wabaId = service?.internalData?.waba_account?.id
  if (!partnerId || !wabaId) {
    throw new Error(`Invalid partner ID or waba ID`)
  }
  const token = await authDialog(hub360Api.username, hub360Api.password)
  const url = `${baseUrl}/partners/${partnerId}/waba_accounts/${wabaId}"`
  const result = await httpClient
    .get(url, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    })
    .then((res) => res.data)
  return { wabaInfo: { mmLiteStatus: result?.settings?.marketing_messages_lite_api_status } }
}

export const sendMessageMMLITEDialog = async (receivedData, serviceId) => {
  const service = await serviceResource.findById(serviceId, {
    attributes: ['data', 'internalData'],
  })
  const token = service?.internalData?.token
  const result = await httpClient
    .post(`https://waba-v2.360dialog.io/marketing_messages`, receivedData, {
      headers: {
        'Content-Type': 'application/json',
        'D360-Api-Key': `${token}`,
      },
    })
    .then((res) => res.data)
  return result
}

export const dialogGetWabaAccount = async (service: ServiceInstance) => {
  const configHub360Api = config('hub360Api')
  const token = await authDialog(configHub360Api.username, configHub360Api.password)
  const tokenApi = service?.internalData?.token
  const partnerId = config('hub360Api').partnerId || ''
  const baseUrl = config('hub360Api').baseUrl || ''
  const wabaId = await httpClient
    .get(`https://waba-v2.360dialog.io/health_status`, {
      headers: {
        'Content-Type': 'application/json',
        'D360-Api-Key': `${tokenApi}`,
      },
    })
    .then((res) => {
      const waba = res.data?.health_status?.entities.find((entity) => entity?.entity_type === 'WABA')?.id
      if (!waba) {
        throw new BadRequestHttpError(`WABA not found for service ${service.id}`)
      }
      return waba
    })
  const result = await httpClient
    .get(`${baseUrl}/partners/${partnerId}/channels`, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: token,
      },
    })
    .then((res) => {
      return res.data?.partner_channels.find((channel) => channel?.waba_account?.external_id === wabaId)?.waba_account
    })
  return result || {}
}
