import FormData from 'form-data'
import Qs from 'qs'
import { Container } from 'typedi'
import { v4 as uuid } from 'uuid'
import config from '../../../../../core/config'
import { ContactInstance } from '../../../../../core/dbSequelize/models/Contact'
import { FileInstance } from '../../../../../core/dbSequelize/models/File'
import { MessageInstance } from '../../../../../core/dbSequelize/models/Message'
import { ServiceInstance } from '../../../../../core/dbSequelize/models/Service'
import contactResource from '../../../../../core/resources/contactResource'
import fileResource from '../../../../../core/resources/fileResource'
import messageResource from '../../../../../core/resources/messageResource'
import serviceResource from '../../../../../core/resources/serviceResource'
import Logger from '../../../../../core/services/logs/Logger'
import reportError from '../../../../../core/services/logs/reportError'
import { getTextWithUser } from './baseFunctions'
import LinkedApp from './interfaces/gupshup/linkApp.interface'
import MediaData from './interfaces/gupshup/mediaData.interface'
import { GetAllSubscriptionsResponse, SetSubscriptionResponse } from './interfaces/gupshup/subscription.interface'
import Row from './interfaces/gupshup/row.interface'
import Section from './interfaces/gupshup/section.interface'
import TextMessage from './interfaces/gupshup/textMessage.interface'
import WebhookPayload from './interfaces/gupshup/webhookPayload.interface'
import configValues from '../../../../../core/configValues'
import BadRequestHttpError from '../../../../../core/utils/error/BadRequestHttpError'
import { HttpClient } from '../../../../../core/services/httpClient/HttpClient'
import queuedAsyncMap from '../../../../../core/utils/array/queuedAsyncMap'

const logger = Container.get(Logger)
const log = logger.log

const partnerUrl = 'https://partner.gupshup.io/partner'
const clientUrl = config('gupshupClientUrl')
const mmliteUrl = 'https://api.gupshup.io/wa/app'

export interface GupshupError {
  error: boolean
}

export interface Templates {
  status: string
  templates: Array<any>
}

const getHttpClient = () => Container.get(HttpClient)

const isGupshupError = (payload: any): payload is GupshupError => {
  return payload && typeof payload === 'object' && 'error' in payload
}

const getAccessToken = async (): Promise<string | GupshupError> => {
  const url = `${partnerUrl}/account/login`
  const payload = Qs.stringify({
    email: config('gupshupEmail'),
    password: config('gupshupPassword'),
  })
  return getHttpClient()
    .post(url, payload)
    .then((response) => response?.data?.token)
    .catch((error) => {
      log(`Error to get PartnerAccessToken ${error}`, 'error')
      return { error: true }
    })
}

export const getAppToken = async (appId: string, partnerToken: string): Promise<string | GupshupError> => {
  const url = `${partnerUrl}/app/${appId}/token`
  const header = {
    headers: {
      token: partnerToken,
    },
  }
  return getHttpClient()
    .get(url, header)
    .then((response) => {
      return response?.data?.token?.token
    })
    .catch((error) => {
      log(`Error to get AppToken ${error}`, 'error')
      return { error: true }
    })
}

export const setupApp = async (service: ServiceInstance) => {
  log(`Get PartnerAccessToken for service ${service.id}`, 'info')
  const partnerToken = await getAccessToken()
  if (isGupshupError(partnerToken)) {
    return partnerToken
  }

  log(`Get PartnerApp for service ${service.id}`, 'info')
  const partnerApps =
    (service.internalData?.id && service.internalData) ||
    (await getPartnerAppLink(service.data.appName, partnerToken)) ||
    (await setPartnerAppLink(service.internalData.apiKey, service.data.appName, partnerToken))
  if (isGupshupError(partnerApps)) {
    return partnerApps
  }

  log(`Get AppToken for service ${service.id}`, 'info')
  const appToken = await getAppToken(partnerApps.id, partnerToken)
  if (isGupshupError(appToken)) {
    return appToken
  }

  if (!configValues.disableWabaWebhookUrlSet) {
    log(`Setup Webhook for service ${service.id}`, 'info')

    const responseWebhook = await setWebhook(partnerApps.id, service.id, appToken)

    if (isGupshupError(responseWebhook)) {
      return responseWebhook
    }
  }

  return {
    ...service.internalData,
    ...partnerApps,
    partnerToken,
    appToken,
  }
}

export const getPartnerAppLink = async (appName: string, partnerToken: string): Promise<LinkedApp | GupshupError> => {
  const url = `${partnerUrl}/account/api/partnerApps`
  const header = {
    headers: {
      token: partnerToken,
    },
  }
  return getHttpClient()
    .get(url, header)
    .then((response) => {
      return response?.data?.partnerAppsList?.find((item) => item.name === appName)
    })
    .catch((error) => {
      log(`Error to get PartnerAppLink`, error)
      return { error: true }
    })
}

export const setPartnerAppLink = async (apiKey, appName, partnerToken: string): Promise<LinkedApp | GupshupError> => {
  const url = `${partnerUrl}/account/api/appLink`
  const payload = Qs.stringify({ apiKey, appName })

  return getHttpClient()
    .post(url, payload, {
      headers: {
        Authorization: partnerToken,
      },
    })
    .then(async (res) => {
      return {
        ...res?.data?.partnerApps,
      }
    })
    .catch((error) => {
      log(`Error to set PartnerAppLink ${error}`, 'error')
      return { error: true }
    })
}

const setSubscription = async (
  appId: string,
  appToken: string,
  serviceId: string,
): Promise<SetSubscriptionResponse> => {
  const url = `${partnerUrl}/app/${appId}/subscription`

  const callbackUrl = `${config('publicUrl')}/whatsapp-business-webhook/${serviceId}`

  const encodedParams = new URLSearchParams()

  encodedParams.set(
    'modes',
    'TEMPLATE,ACCOUNT,PAYMENTS,FLOWS_MESSAGE,MESSAGE,OTHERS,ALL,BILLING,FAILED,DELETED,SENT,DELIVERED,READ,ENQUEUED',
  )
  encodedParams.set('tag', 'default_callback')
  encodedParams.set('url', callbackUrl)
  encodedParams.set('version', '2')
  encodedParams.set('showOnUI', 'true')

  const response = await getHttpClient()
    .post(url, encodedParams, {
      headers: {
        Authorization: appToken,
        accept: 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    .catch((error) => {
      log(`Error to set Webhook ${error.message}`, 'error')
      throw error
    })
    .then((r) => r.data)

  return response
}

const getAllSubscriptions = async (appId: string, appToken: string): Promise<GetAllSubscriptionsResponse> => {
  const url = `${partnerUrl}/app/${appId}/subscription`

  return getHttpClient()
    .get(url, {
      headers: {
        Authorization: appToken,
      },
    })
    .catch((error) => {
      log(`Error to get all subscriptions ${error.message}`, 'error')
      throw error
    })
    .then((r) => r.data)
}

const deleteSubscription = async (appId: string, appToken: string, subscriptionId: string): Promise<number> => {
  const url = `${partnerUrl}/app/${appId}/subscription/${subscriptionId}`

  return getHttpClient()
    .delete(url, {
      headers: {
        Authorization: appToken,
      },
    })
    .catch((error) => {
      log(`Error to delete subscription ${error.message}`, 'error')
      throw error
    })
    .then((r) => r.data)
}

export const setWebhook = async (
  appId: string,
  serviceId: string,
  appToken: string,
): Promise<void | { error: boolean }> => {
  try {
    const allSubscriptions = await getAllSubscriptions(appId, appToken)

    if (allSubscriptions?.subscriptions?.length) {
      await queuedAsyncMap(allSubscriptions.subscriptions, async (subscription) => {
        if (subscription.tag !== 'default_callback') {
          return
        }

        await deleteSubscription(appId, appToken, subscription.id)
      })
    }

    const response = await setSubscription(appId, appToken, serviceId)

    log(`Webhook setted for service ${serviceId} response %o`, 'info', [response])

    return null
  } catch (error) {
    log(`Error to set Webhook serviceId ${serviceId} error ${error.message} responseError %o`, 'error', [
      error?.response?.data,
    ])
    return { error: true }
  }
}

export const getTypeOfMessageByMimetype = (type: string) =>
  ({
    chat: 'text',
    image: 'image',
    interactive: 'interactive',
    document: 'file',
    audio: 'audio',
    sticker: 'sticker',
    video: 'video',
    hsm: 'hsm',
    vcard: 'contacts',
    list_reply: 'list_reply',
  }[type] || 'unknow')

/**
 * Tipo de mensagem para ser enviar em quick reply
 *
 * @param messageType string
 * @returns string
 */
const quickReplyMessageType = (messageType: string): string =>
  ({
    document: 'file',
    text: 'text',
    image: 'image',
    video: 'video',
  }[messageType])

const hsmMessageFormated = (messageData, data, file, link = null) => {
  const { hsm } = data
  const { parameters = [] } = messageData

  link = link || messageData.link

  const header = parameters.find((i) => i.type === 'header')

  const headerParams = header?.parameters?.map((p) => p?.text)

  const bodyParams = parameters.find((i) => i.type === 'body')?.parameters?.map((p) => p.text)

  const buttonParams = parameters.find((i) => i.type === 'button')?.parameters?.map((p) => p.text)

  if (hsm.category === 'AUTHENTICATION') {
    bodyParams.push(parameters.find((i) => i.type === 'body')?.parameters?.map((p) => p.text))
  }

  const getParams = () =>
    [...(headerParams || []), ...(bodyParams || []), ...(buttonParams || [])].flat().filter(Boolean)

  return {
    ...(link && {
      message: JSON.stringify({
        type: header?.parameters?.[0].type,
        [header?.parameters?.[0].type]: { link },
      }),
    }),
    ...(file && {
      message: JSON.stringify({
        type: header?.parameters?.[0].type,
        [header?.parameters?.[0].type]: { link: file?.url },
      }),
    }),
    template: JSON.stringify({
      id: data.hsm.idGupshup,
      params: getParams(),
    }),
  }
}

const contactMessageFormated = (vcard) => {
  const removeCharacters = vcard.replace(/(;)/gm, '')
  const formatBreak = removeCharacters.split('\n')

  const vcardToJson = formatBreak.reduce((acc, cur) => {
    const string = cur.split(':')
    const types = ['FN', '.X-ABLabel', '.TEL']

    const isValid = types.some((type) => string[0].search(type) >= 0)

    if (!isValid) return acc

    let type = null
    const obj = {}

    if (string[0].search('FN') >= 0) type = 'formattedName'
    if (string[0].search('.TEL') >= 0) type = 'phone'
    if (string[0].search('.X-ABLabel') >= 0) type = 'type'

    obj[type] = string[1]

    return { ...acc, ...obj }
  }, {})

  const { formattedName = '', phone = '', type = 'Mobile' } = vcardToJson

  return {
    type: 'contact',
    contact: {
      addresses: [],
      birthday: '',
      emails: [],
      name: {
        firstName: '',
        formattedName,
        lastName: '',
      },
      org: {},
      phones: [
        {
          phone,
          type,
        },
      ],
      urls: [],
    },
  }
}

export const interactiveMessageSectionCreate = (sections: Section[]) => {
  return sections.map((section: Section) => {
    return {
      title: section.title,
      options: section.rows.map((row: Row) => {
        return {
          type: 'text',
          title: row.title,
        }
      }),
    }
  })
}

export const interactiveMessageFormat = (messageData, interactive, link, file) => {
  const { name, mediaId } = messageData

  if (interactive.type === 'list') {
    return {
      type: 'list',
      title: name,
      body: interactive?.body?.text,
      footer: interactive?.footer?.text,
      globalButtons: [
        {
          type: 'text',
          title: interactive?.action?.button,
        },
      ],
      items: interactiveMessageSectionCreate(interactive?.action?.sections),
    }
  }

  // tipo da mensagem a ser enviada
  const typeMessage = quickReplyMessageType(interactive?.header?.type)

  return {
    type: 'quick_reply',
    content: {
      type: typeMessage,
      header: interactive?.header?.text,
      text: interactive?.body?.text,
      url: mediaId || link,
      caption: interactive?.footer?.text,
      ...(typeMessage !== 'text' && { filename: file?.name || messageData?.file?.name }),
    },
    options: interactive?.action?.buttons.map((button) => {
      return {
        type: 'text',
        title: button.reply.title,
      }
    }),
  }
}

export const setBodyMarketingIntegration = (messageData) => {
  const { hsm } = messageData

  return {
    marketingIntegration: true,
    recipient_type: 'individual',
    messaging_product: 'whatsapp',
    to: messageData?.phoneNumber,
    type: 'template',
    template: {
      language: {
        policy: 'deterministic',
        code: hsm?.language || 'pt_BR',
      },
      namespace: hsm?.namespace,
      name: hsm.name,
      components: messageData?.parameters || [],
    },
  }
}

export const getMessageDataByType = async (
  type: string,
  data: any,
  messageData: any,
  contact: ContactInstance,
  userId: string,
  service: ServiceInstance,
  file: FileInstance,
  marketingIntegration?: boolean,
): Promise<any> => {
  const { interactive } = messageData
  const link = file?.url || messageData.link

  const typeOfMessage = getTypeOfMessageByMimetype(type)
  const { vcard } = data.message

  const phoneNumber =
    contact?.data?.validNumber ||
    messageData.phoneNumber ||
    data.contact?.data?.validNumber ||
    data.contact?.data?.number

  const hasCaption = ['document', 'video', 'image'].includes(type) && messageData.text

  let messageBody = null
  let resHsmMessageFormated = null
  let templateBody = null

  if (typeOfMessage === 'hsm' && marketingIntegration) {
    return setBodyMarketingIntegration(messageData, data, file, link)
  }
  switch (typeOfMessage) {
    case 'text':
      messageBody = await getTextWithUser(data, userId)
      break

    case 'audio':
    case 'image':
    case 'sticker':
    case 'video':
      messageBody = {
        type: typeOfMessage,
        [typeOfMessage === 'image' ? 'originalUrl' : 'url']: link,
        ...(hasCaption && { caption: messageData.text }),
      }
      break

    case 'file':
      messageBody = {
        type: typeOfMessage,
        url: link,
        ...(hasCaption && { caption: messageData.text }),
        filename: file?.name ?? messageData?.file?.name ?? '',
      }
      break

    case 'interactive':
      messageBody = interactiveMessageFormat(messageData, interactive, link, file)
      break

    case 'hsm':
      resHsmMessageFormated = hsmMessageFormated(messageData, data, file, link)
      messageBody = resHsmMessageFormated.message
      templateBody = resHsmMessageFormated.template
      break

    case 'contacts':
      messageBody = contactMessageFormated(vcard)
      break

    default:
      break
  }

  return {
    message: messageBody,
    template: templateBody,
    destination: phoneNumber,
    channel: 'whatsapp',
    'src.name': service.data.appName,
    source: service.data.phone,
  }
}

export const getReactionData = async (message: MessageInstance, reactionEmojiRendered: string): Promise<any> => {
  const [contact, service] = await Promise.all([
    contactResource.findById(message.contactId, { attributes: ['idFromService'] }),
    serviceResource.findById(message.serviceId, { attributes: ['data'] }),
  ])

  return {
    message: `{"type":"reaction","emoji":"${reactionEmojiRendered}","msgId":"${message.idFromService}"}`,
    destination: contact.idFromService,
    channel: 'whatsapp',
    'src.name': service.data.appName,
    source: service.data.phone,
  }
}

const callMessageApi = async (
  message: any,
  sufix: string,
  serviceId: string,
): Promise<{ messages: Array<any> } | GupshupError> => {
  const service = await serviceResource.findById(serviceId, {
    attributes: ['data', 'internalData'],
  })

  const headers = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      apikey: service?.internalData?.apiKey,
    },
  }

  const payload = Qs.stringify(message)

  return getHttpClient()
    .post(`${clientUrl}/${sufix}`, payload, headers)
    .then((response) => {
      return { messages: [response.data] }
    })
    .catch((error) => {
      if (error?.code === 'ECONNRESET') {
        log(`ECONNRESET error to call MessageAPI: Erro em comunicação com o provedor, tente novamente`, 'error')
        return { error: 'Erro em comunicação com o provedor, tente novamente' }
      }
      log(`Error to call MessageAPI ${error}`, 'error')
      return { error }
    })
}

export const sendTemplate = async (
  template: any,
  serviceId: string,
): Promise<{ messages: Array<any> } | GupshupError> => {
  const suffix = 'template/msg'
  return callMessageApi(template, suffix, serviceId)
}

export const sendTextMessage = async (
  message: TextMessage,
  serviceId: string,
): Promise<{ messages: Array<any> } | GupshupError> => {
  const suffix = 'msg'
  return callMessageApi(message, suffix, serviceId)
}

export const sendMediaMessage = async (
  message: TextMessage,
  serviceId: string,
): Promise<{ messages: Array<any> } | GupshupError> => {
  const suffix = 'msg'
  message.message = JSON.stringify(message.message)
  return callMessageApi(message, suffix, serviceId)
}

export const sendMMLite = async (
  template: any,
  serviceId: string,
): Promise<{ messages: Array<any> } | GupshupError> => {
  const service = await serviceResource.findById(serviceId, {
    attributes: ['data', 'internalData'],
  })
  const appId = service?.internalData?.id
  const apikey = service?.internalData?.apiKey
  const headers = {
    headers: {
      'Content-Type': 'application/json',
      apikey: apikey,
    },
  }
  return getHttpClient()
    .post(`${mmliteUrl}/${appId}/v3/marketing/msg`, template, headers)
    .then((response) => {
      return { messages: [{ status: 'submitted', messageId: response.data?.messages[0]?.id || '' }] }
    })
    .catch((error) => {
      if (error?.code === 'ECONNRESET') {
        log(`ECONNRESET error to call MessageAPI: Erro em comunicação com o provedor, tente novamente`, 'error')
        return { error: 'Erro em comunicação com o provedor, tente novamente' }
      }
      log(`Error to call MessageAPI ${error}`, 'error')
      return { error }
    })
}

export const sendMessage = async (receivedData: any, data: any) => {
  if (receivedData?.marketingIntegration) {
    return sendMMLite(receivedData, data?.serviceId)
  }
  if (receivedData?.template) {
    return sendTemplate(receivedData, data?.serviceId)
  }

  if (typeof receivedData?.message === 'string') {
    return sendTextMessage(receivedData, data?.serviceId)
  }

  if (receivedData?.message?.type) {
    return sendMediaMessage(receivedData, data?.serviceId)
  }

  return { error: 'Invalid type message' }
}

export const getTemplates = async (token: string, appId: string): Promise<Templates> => {
  const headers = {
    headers: {
      token,
    },
  }
  return getHttpClient()
    .get(`${partnerUrl}/app/${appId}/templates`, headers)
    .then((res) => res.data)
}

export const getWebhookType = (request: {
  payload: {
    type: string
  }
  type: string
}): string => {
  if (request.type === 'error') {
    return 'errors'
  }

  if (request.type === 'message' && request?.payload?.type === 'reaction') {
    return 'reaction'
  }

  if (request.type === 'message-event' && request?.payload?.type === 'enqueued') {
    return 'enqueued'
  }

  if (request?.type === 'template-event' && request?.payload?.type === 'status-update') {
    return 'template_status'
  }

  if (request?.type === 'template-event' && request?.payload?.type === 'quality-update') {
    return 'template_quality'
  }

  if (request.type === 'message') {
    return 'messages'
  }

  const statuses = ['sent', 'delivered', 'read', 'deleted', 'failed']
  if (statuses.includes(request?.payload?.type)) {
    return 'statuses'
  }

  return 'unknow'
}

export const validateWebhookPayload = (request: WebhookPayload): boolean => {
  if (!request.type) return false

  if (!request.payload) return false

  if (!request.payload.type) return false

  if (!request.payload.payload) return false

  if (request.payload.type === 'text' && !request.payload.payload.text) return false

  if (!request.payload.sender) return false

  if (!request.payload.sender.phone) return false

  return true
}

export const getVcard = (contact) => {
  const firstName = contact?.name?.first_name || ''
  const lastName = contact?.name?.last_name || ''
  const phone = contact?.phones?.[0]?.wa_id || contact?.phones?.[0]?.phone.replace(/[^0-9]/g, '') || ''
  const waId = phone
  const phoneNumber = contact?.phones?.[0]?.phone.replace(/[^0-9]/g, '') || contact?.phones?.[0]?.wa_id || ''
  return `BEGIN:VCARD\nVERSION:3.0\nN:${lastName};${firstName};;;\nFN:${firstName} ${lastName}\nitem1.TEL;waid=${waId}:${phoneNumber}\nitem1.X-ABLabel:Mobile\nEND:VCARD`
}

/**
 * Retorna o tipo de mensagem recebida pelo webhook
 *
 * @param string typeMessage
 * @returns
 */
export const getMessageTypeFromWebhook = (messageType: string): string => {
  const typesOfWebhook = {
    text: 'chat',
    quick_reply: 'chat',
    contact: 'vcard',
    location: 'location',
    audio: 'audio',
    file: 'document',
    image: 'image',
    video: 'video',
    sticker: 'sticker',
    unknow: 'unknow',
    button: 'button',
    list_reply: 'list_replay',
    button_reply: 'button_reply',
  }

  if (typesOfWebhook[messageType]) {
    return typesOfWebhook[messageType]
  }

  log(`Invalid message type detected: ${messageType}`, 'error')

  return 'chat'
}

export const getMedia = async (url: string) => {
  return getHttpClient()
    .get(url, { responseType: 'arraybuffer' })
    .then((res) => res.data)
}

export const processReceivedFile = async ({ payload }) => {
  const { request, accountId, messageId } = payload

  const { url, contentType, name } = request?.payload?.payload
  const { type } = request?.payload
  const ext = contentType.split('/')[1]

  const response = await getMedia(encodeURI(url))

  await fileResource.eventTransaction(async (eventTransaction) => {
    await fileResource
      .getRepository()
      .transaction(async (transaction) => {
        const file = {
          data: response,
          name: name ?? `${uuid()}${type !== 'document' ? (type === 'voice' ? '.oga' : `.${ext}`) : ''}`,
          mimetype: contentType,
          isPtt: contentType === 'voice',
        }

        const createdFile = await fileResource.create(
          {
            ...file,
            attachedId: messageId,
            attachedType: 'message.file',
            accountId,
          },
          { transaction, eventTransaction },
        )

        await messageResource.updateById(
          messageId,
          {
            data: {
              fileDownload: {
                startedAt: new Date(),
                endedAt: new Date(),
                isDownloading: false,
              },
            },
          },
          { mergeJson: ['data'], transaction, eventTransaction },
        )

        return createdFile
      })
      .then(async (createdFile) => {
        await fileResource.createMessageThumbnail(createdFile, messageId, accountId, { eventTransaction })
        return createdFile
      })
  })
}

export const updateWhatsappMessageId = async (data) => {
  const idFromService = data.request.payload.id
  const { whatsappMessageId } = data.request.payload.payload

  const message = await messageResource.findOne({
    where: {
      idFromService,
      serviceId: data.serviceId,
      accountId: data.accountId,
    },
  })

  if (!message) {
    log('The message was not found to update whatsappMessageId', 'error')
    return
  }

  await messageResource.update(
    message,
    {
      data: {
        whatsappMessageId,
      },
    },
    { mergeJson: ['data'] },
  )
}

const sendMedia = async (data: MediaData): Promise<string> => {
  const { hsmId, appId, appToken } = data

  const file = await fileResource.findOne({
    where: {
      attachedType: 'hsm.file',
      attachedId: hsmId,
    },
  })

  if (!file) throw new Error('Template not found.')

  const b64Data = await fileResource.getBase64(file)

  const dataSend = new FormData()
  dataSend.append('file_type', file?.mimetype)
  dataSend.append('file', b64Data, file?.name)

  const header = {
    headers: {
      'Content-Type': `multipart/form-data; boundary=${dataSend.getBoundary()}`,
      Authorization: appToken,
    },
  }

  return getHttpClient()
    .post(`${partnerUrl}/app/${appId}/upload/media`, dataSend, header)
    .then((res) => res.data?.handleId?.message)
    .catch((error) => {
      log(error, 'error')
      throw new Error(error)
    })
}

const getTemplatePayload = async (data, body, footer, example, buttons) => {
  const payload = {
    elementName: data?.name,
    languageCode: data?.language,
    category: data?.category,
    content: body?.text,
    footer: footer?.text,
    enableSample: true,
    example,
  }

  const templateHeader = data.components.find((component) => component.type === 'HEADER')

  payload.templateType = templateHeader?.format ?? 'TEXT'
  payload.vertical = templateHeader?.format ?? 'TEXT'

  if (templateHeader?.format && templateHeader?.format !== 'TEXT') {
    payload.exampleMedia = await sendMedia({
      hsmId: data.id,
      fileUrl: templateHeader?.example?.header_handle?.[0],
      appId: data?.appId,
      appToken: data?.appToken,
    })
    payload.templateType = templateHeader?.format
    payload.vertical = templateHeader?.format
  }

  if (templateHeader?.format === 'TEXT' && templateHeader?.text) {
    payload.header = templateHeader?.text
    payload.exampleHeader = templateHeader?.text
  }

  if (buttons) {
    payload.buttons = JSON.stringify(buttons)
  }

  // body
  const templateBody = data.components.find((component) => component.type === 'BODY')

  if (templateBody) {
    if (templateBody.add_security_recommendation) {
      payload.addSecurityRecommendation = templateBody.add_security_recommendation
    }
  }

  // footer
  const templateFooter = data.components.find((component) => component.type === 'FOOTER')

  if (templateFooter && templateFooter.code_expiration_minutes) {
    payload.codeExpirationMinutes = templateFooter.code_expiration_minutes
  }

  return payload
}

export const createTemplate = async (data: {}) => {
  const header = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      token: data.appToken,
    },
  }

  const footer = data?.components.find((component) => component.type === 'FOOTER')

  const buttons = data?.components.find((component) => component.type === 'BUTTONS')?.buttons

  const body = data?.components.find((component) => component.type === 'BODY')

  let example = body?.text ?? ''
  body?.params.forEach((param, index) => {
    example = example.replace(`{{${index + 1}}}`, param)
  })

  const payload = await getTemplatePayload(data, body, footer, example, buttons)

  const response = await getHttpClient()
    .post(`${partnerUrl}/app/${data.appId}/templates`, Qs.stringify(payload), header)
    .then((res) => res.data)
    .catch((error) => reportError(error))

  return response?.template
}

export const deleteTemplate = async (name: string, appId: string, appToken: string) => {
  const header = {
    headers: {
      Authorization: appToken,
    },
  }

  return getHttpClient()
    .delete(`${partnerUrl}/app/${appId}/template/${name}`, header)
    .then((res) => (res.status !== 200 ? res : res?.data))
    .catch((error) => {
      reportError(error)
      throw error
    })
}

export const health = async (service: ServiceInstance, handleNotificationWabaHealth: any) => {
  const token = service?.internalData?.appToken
  const appId = service?.internalData?.id
  if (!token) {
    log(`There is no access token for waba service name ${service?.name}`)
    return
  }
  if (!appId) {
    log(`There is no app id for waba service name ${service?.name}`)
    return
  }
  const url = `${partnerUrl}/app/${appId}/waba/info`
  await getHttpClient()
    .get(url, {
      headers: {
        Authorization: token,
      },
    })
    .then(async (response) => {
      const status = response?.data?.wabaInfo?.canSendMessage
      if (service?.health?.status !== status) {
        const info = response?.data?.wabaInfo?.additionalInfo
        const errors = response?.data?.wabaInfo?.errors

        await serviceResource.updateById(service.id, { health: { status, info, errors } })

        if (status !== 'AVAILABLE') {
          await handleNotificationWabaHealth(service.accountId, status)
        }
      }
    })
}

export const mmliteStatus = async (service: ServiceInstance) => {
  const token = service?.internalData?.appToken
  const appId = service?.internalData?.id
  if (!token) {
    throw new BadRequestHttpError(`There is no access token for waba service name ${service?.name}`)
  }
  if (!appId) {
    throw new BadRequestHttpError(`There is no app id for waba service name ${service?.name}`)
  }
  const url = `${partnerUrl}/app/${appId}/waba/info`
  const result = await getHttpClient()
    .get(url, {
      headers: {
        Authorization: token,
      },
    })
    .then((res) => res.data)
  return result
}
