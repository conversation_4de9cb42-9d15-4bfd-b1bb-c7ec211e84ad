import { Service, Inject } from 'typedi'
import TranscriptService from '../../../../core/services/transcript'
import Job from '../../../../core/services/jobs/interfaces/Job'

@Service()
export default class QueuedAudioTranscribeJob implements Job {
  static jobName = 'queued-audio-transcribe'

  @Inject()
  service: TranscriptService

  async handle({ idMessage, notCharge = false, origin = null }) {
    await this.service.transcribe(idMessage, notCharge, origin)
  }
}
