import { Container } from 'typedi'
import { Router } from 'express'
import validate from '../../../core/middlewares/validate'
import accountContext from '../../../core/middlewares/accountContext'
import { string, required, hasLengthLesserThanOrEqual } from '../../../core/utils/validator/rules'
import { KnowledgeBaseResource } from '../../../core/resources/knowledgeBaseResource'
import resourceController from '../../../core/utils/routing/resourceController'
import knowledgeBaseTransformer from '../../../core/transformers/knowledgeBaseTransformer'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'
import HaystackIaApi from '../../../core/services/haystackIa'
import knowledgeBaseRepository from '../../../core/dbSequelize/repositories/knowledgeBaseRepository'

const includeWhitelist = ['account', 'items']
Container.set('knowledgeBaseRepository', knowledgeBaseRepository)
const knowledgeBaseResource = new KnowledgeBaseResource(
  Container.get(HaystackIaApi),
  Container.get('knowledgeBaseRepository'),
)

const controller = {
  ...resourceController(knowledgeBaseResource, knowledgeBaseTransformer, includeWhitelist),
  suggestByMessage: promiseHandler(async (req, res) => {
    const { messageId, outsideTheKnowledgeBase } = req.body
    return knowledgeBaseResource.suggestResponseByMessage(
      messageId,
      res.locals?.account?.id,
      res.locals?.user?.id,
      outsideTheKnowledgeBase,
    )
  }),
  suggestByTicket: promiseHandler(async (req, res) => {
    const { ticketId, outsideTheKnowledgeBase } = req.body
    return knowledgeBaseResource.suggestResponseByTicket(
      ticketId,
      res.locals?.account?.id,
      res.locals?.user?.id,
      outsideTheKnowledgeBase,
    )
  }),
}

const router = Router()

const validateId = validate({
  params: {
    id: [string, required],
  },
})

const validateSuggestByMessage = validate({
  body: {
    messageId: [string, required],
  },
})

const validateSuggestByTicket = validate({
  body: {
    ticketId: [string, required],
  },
})

export const validateKnowledgeBase = validate({
  body: {
    name: [string, required, hasLengthLesserThanOrEqual(60)],
    description: [string, hasLengthLesserThanOrEqual(600)],
  },
})

router.use(accountContext())

router.get('/', controller.index)
router.get('/:id', validateId, controller.show)
router.post('/', validateKnowledgeBase, controller.create)
router.put('/:id', validateId, validateKnowledgeBase, controller.update)
router.delete('/:id', validateId, controller.destroy)
router.post('/suggest-by-message', validateSuggestByMessage, controller.suggestByMessage)
router.post('/suggest-by-ticket', validateSuggestByTicket, controller.suggestByTicket)

export default router
