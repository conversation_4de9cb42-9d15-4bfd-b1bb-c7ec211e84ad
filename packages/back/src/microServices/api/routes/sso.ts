import msal from '@azure/msal-node'
import { Router } from 'express'
import { model as oAuthModel } from '../../../core/services/auth/oAuthServer'
import oAuthClientResource from '../../../core/resources/oAuthClientResource'
import userResource from '../../../core/resources/userResource'
import accountResource from '../../../core/resources/accountResource'
import { addDays } from 'date-fns'
import config from '../../../core/config'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'
import { Op } from 'sequelize'
import NotFoundHttpError from '../../../core/utils/error/NotFoundHttpError'
import HttpError from '../../../core/utils/error/HttpError'
import { Container } from 'typedi'
import Logger, { Level } from '../../../core/services/logs/Logger'

const router = Router()

const logger = Container.get(Logger)

const levelsMap: {
  [K in msal.LogLevel]: Level
} = {
  [msal.LogLevel.Verbose]: 'debug',
  [msal.LogLevel.Trace]: 'debug',
  [msal.LogLevel.Info]: 'info',
  [msal.LogLevel.Warning]: 'warn',
  [msal.LogLevel.Error]: 'error',
}

const loggerCompat = (loglevel: msal.LogLevel, message: string, containsPii) => {
  logger.log(message, levelsMap[loglevel])
}

async function getCCA(accountAlias: string) {
  const account = await accountResource.findOne({
    attributes: ['settings'],
    where: { alias: { [Op.eq]: accountAlias } },
  })

  if (!account) throw new NotFoundHttpError('Account not found')

  if (!account?.settings?.sso?.azureAd) throw new HttpError(400, 'Azure AD settings not set')

  const azureADConfig = {
    azureTenantId: account.settings.sso.azureAd.tenantId,
    clientId: account.settings.sso.azureAd.clientId,
    clientSecret: account.settings.sso.azureAd.clientSecret,
  }

  const redirectUri = `${config('publicUrl')}/v1/sso/azure-ad/${accountAlias}/callback`

  // Configuration for MSAL
  const msalConfig = {
    auth: {
      clientId: azureADConfig.clientId, // Replace with your Azure AD App Client ID
      authority: `https://login.microsoftonline.com/${azureADConfig.azureTenantId}`, // Replace with your Tenant ID
      clientSecret: azureADConfig.clientSecret, // Replace with your Azure AD App Client Secret
    },
    system: {
      loggerOptions: {
        loggerCallback: loggerCompat,
        piiLoggingEnabled: false,
        logLevel: msal.LogLevel.Warning,
      },
    },
  }

  return {
    params: {
      scopes: ['openid', 'profile', 'User.Read'],
      redirectUri: redirectUri,
    },
    cca: new msal.ConfidentialClientApplication(msalConfig),
  }
}

const azureAdLogin = promiseHandler(async (req, res) => {
  const { accountAlias } = req.params
  const { params, cca } = await getCCA(accountAlias)

  // Get the URL to redirect the user to the Microsoft login page
  const response = await cca.getAuthCodeUrl(params)

  res.redirect(response)
})

const azureAdCallback = promiseHandler(async (req, res) => {
  const { accountAlias } = req.params

  const { params, cca } = await getCCA(accountAlias)

  const tokenRequest = {
    code: req.query.code as string, // Authorization code returned from Azure AD
    ...params,
  }

  // Exchange the authorization code for an access token
  const response = await cca.acquireTokenByCode(tokenRequest)

  const user = await userResource.findOne({
    attributes: ['id'],
    where: {
      email: response.account.username,
      active: true,
    },
  })

  if (!user) {
    res.status(401).send(`User not found: ${response.account.username}`)
    return
  }

  const client = await oAuthClientResource.findOne({
    where: { clientId: 'api' },
  })

  const now = new Date()

  const tokenData = {
    accessToken: await oAuthModel.generateAccessToken(),
    refreshToken: await oAuthModel.generateAccessToken(),
    accessTokenExpiresAt: addDays(now, 15),
    refreshTokenExpiresAt: addDays(now, 15),
    scope: ['*'],
  }

  const token = await oAuthModel.saveToken(tokenData, client, user)

  if (!token) {
    res.status(401).send('Failed to generate access token')
    return
  }

  const obfuscatedAuth = Buffer.from(
    JSON.stringify({
      access_token: token.accessToken,
    }),
  ).toString('base64')

  const redirectUrl = `${config('frontUrl')}/login?auth=${obfuscatedAuth}`

  res.redirect(redirectUrl)
})

const getOptionsForAccount = promiseHandler(async (req, res) => {
  const { accountAlias } = req.params

  const account = await accountResource.findOne({
    attributes: ['settings'],
    where: { alias: { $eq: accountAlias } },
    cache: true,
  })

  if (!account) return []

  const azureAd = account.settings?.sso?.azureAd?.enabled && {
    type: 'azure-ad',
    label: account.settings?.sso?.azureAd.label || 'Azure AD',
    url: `${config('publicUrl')}/v1/sso/azure-ad/${accountAlias}/login`,
  }

  return [azureAd].filter(Boolean)
})

router.get('/azure-ad/:accountAlias/login', azureAdLogin)
router.get('/azure-ad/:accountAlias/callback', azureAdCallback)
router.get('/:accountAlias', getOptionsForAccount)

export default router
