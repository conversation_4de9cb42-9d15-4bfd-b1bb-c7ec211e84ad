import { Router, Request, Response } from 'express'
import { differenceBy, groupBy, flatten, pick, omit, uniqBy } from 'lodash'
import Container from 'typedi'
import isAfter from 'date-fns/isAfter'
import { stringify } from 'csv-stringify'
import validator from 'validator'
import { Sequelize, QueryTypes } from 'sequelize'
import sequelize from '../../../core/services/db/sequelize'
import validate from '../../../core/middlewares/validate'
import blockMessageRuleResource from '../../../core/resources/blockMessageRuleResource'
import contactResource from '../../../core/resources/contactResource'
import customFieldValuesResource from '../../../core/resources/customFieldValuesResource'
import customFieldsResource from '../../../core/resources/customFieldsResource'
import {
  currentAccountOwnsPerson,
  currentAccountOwnsService,
  currentAccountOwnsTags,
} from '../../../core/utils/validator/accountOwns'
import { fromReqRes } from '../../../core/utils/resource/transformerHelpers'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'
import accountContext from '../../../core/middlewares/accountContext'
import resourceController, { filterQuery } from '../../../core/utils/routing/resourceController'
import ContactsController from '../controllers/ContactsController'
import { getHeaderCsvFile } from '../../../core/services/contacts/ContactService'
import transformer, { getContactNames as hideContactNumbers } from '../../../core/transformers/contactTransformer'
import { required, string, hasLengthLesserThanOrEqual, uuid4, arrayOf } from '../../../core/utils/validator/rules'
import {
  formatIdFromNumber,
  idToNumber,
  parseNumber,
  getPossibleNumbers,
} from '../../../core/utils/whatsapp/numberParser'
import { separateDDIFromTheNumber } from '../../../core/utils/validator/validateNumberFormat'
import arrayToObject from '../../../core/utils/array/arrayToObject'
import queuedAsyncMap from '../../../core/utils/array/queuedAsyncMap'
import driverService, { getServiceRpc } from '../../../core/services/driverService'
import { getCloseTicketQueue } from '../../../core/queues/tickets'
import serviceResource from '../../../core/resources/serviceResource'
import iteratePaginated from '../../../core/utils/iteratePaginated'
import { prepareInclude } from '../../../core/dbSequelize/repositories/BaseSequelizeRepository'
import { ContactInstance, ContactOrigin } from '../../../core/dbSequelize/models/Contact'
import { CustomFieldValueInstance } from '../../../core/dbSequelize/models/CustomFieldValue'
import ticketResource from '../../../core/resources/ticketResource'
import hasPermission from '../../../core/utils/hasPermission'
import { obfuscateFromNumber as hidePhoneNumber } from '../../../core/utils/phoneNumberHelper'
import HttpError from '../../../core/utils/error/HttpError'
import config from '../../../core/config'
import formatDate from '../../../core/utils/date/formatDate'
import BadRequestHttpError from '../../../core/utils/error/BadRequestHttpError'
import comparableIdFromService from '../../../core/utils/whatsapp/comparableIdFromService'
import { validateSchema } from '../../../core/middlewares/zodValidator'
import { customFieldsSchema } from '../../../core/zod-schemas'
import { checkAndOrganizeCustomFieldsPayload } from '../../../core/middlewares/validateCustomFields'
import { customFieldsMapError } from '../../../core/zod-schemas/mappedErrors'
import { AvailableFormatRangeTypesEnum, formatCustomField, formatRangeObject } from '../../../core/utils/customFields'
import tagResource from '../../../core/resources/tagResource'
import reportError from '../../../core/services/logs/reportError'
import configValues from '../configValues'
import HttpJobsDispatcher from '../../../core/services/jobs/http/HttpJobsDispatcher'

const controller = new ContactsController()

const getRowByService = (
  serviceType,
  contact,
  name,
  ddi = null,
  number = null,
  email = null,
  createdAt,
  customFields = [],
) => {
  const response = [
    name,
    ...(serviceType === 'email' ? [email] : serviceType === 'webchat' ? [email, number] : [ddi, number]),
    contact.defaultUser && contact.defaultUser.name,
    contact.defaultDepartment && contact.defaultDepartment.name,
    contact.tags.map((tag) => tag.label).join(' / '),
  ]

  response.push(formatDate(createdAt))

  customFields.forEach((field) => {
    const found = contact?.customFieldValues.find((customFieldValue) => customFieldValue.customFieldId === field.id)
    response.push(formatCustomField(found?.value, field?.type, field.settings))
  })

  return response
}

const getContactsByServiceIdAndIdsFromService = (serviceId, idsFromService) =>
  contactResource.findMany({
    where: {
      idFromService: idsFromService,
      serviceId,
      archivedAt: null,
    },
  })

// ============================================================================
// Validations
// ============================================================================
export const validateCreate = validate({
  body: {
    // TODO add other validations
    name: [string, hasLengthLesserThanOrEqual(255)],
    internalName: [string, hasLengthLesserThanOrEqual(255)],
    serviceId: [required, string, currentAccountOwnsService],
    personId: [string, currentAccountOwnsPerson],
    tagIds: [currentAccountOwnsTags],
  },
})

export const validateUpdate = validate({
  body: {
    name: [string, hasLengthLesserThanOrEqual(255)],
    internalName: [string, hasLengthLesserThanOrEqual(255)],
    serviceId: [string, currentAccountOwnsService],
    personId: [string, currentAccountOwnsPerson],
    tagIds: [currentAccountOwnsTags],
  },
})

export const validateImportGroup = validate({
  body: {
    id: [string, hasLengthLesserThanOrEqual(255)],
    serviceId: [string, currentAccountOwnsService],
    tagIds: [currentAccountOwnsTags],
  },
})

export const validateTicketOpening = validate({
  body: {
    departmentId: [required, uuid4],
    userId: [required, uuid4],
  },
})

// ============================================================================
// Handlers
// ============================================================================
const includeWhitelist = [
  'messages',
  'tags',
  'tagsFilter',
  'service',
  'person',
  'defaultDepartment',
  'defaultUser',
  'avatar',
  'thumbAvatar',
  'lastMessage',
  'lastMessage.ticket',
  'lastMessage.ticket.department',
  'groups',
  'currentTicket',
  'currentTicket.department',
  'currentTicket.user',
  'customFieldValues',
  'customFieldValues.customField',
  'cards',
]

const {
  destroy: baseDestroy,
  index: baseIndex,
  count,
  postIndex: sendPostIndex,
  countPost,
} = resourceController(contactResource, transformer, includeWhitelist)

// Evita subqueries e ordenações custosas, faz uso de index
const parseQuery = (query) => {
  query.order = query.order || []

  let newQuery = query

  if (query.includeTicketTransfer) {
    newQuery = filterQuery(newQuery, includeWhitelist)
  }

  const limit = Number(query.limit)
  const offset = Number(query.offset)

  const orderType = query?.activeOrder || ''

  switch (orderType) {
    case 'newestCreatedTicket':
      // Ordena pelo ticket mais recente (NULLs no final)
      newQuery.order = [[sequelize.col('currentTicket.createdAt'), 'DESC NULLS LAST']]
      break

    case 'oldestCreatedTicket':
      // Ordena pelo ticket mais antigo (NULLs no início)
      newQuery.order = [[sequelize.col('currentTicket.createdAt'), 'ASC NULLS FIRST']]
      break

    case 'longestWaitingTime':
      // Ordena pelo maior tempo de espera do contato
      // Primeiro por isFromMe (false = aguardando resposta), depois pela data mais antiga
      newQuery.order = [
        [sequelize.col('lastMessage.isFromMe'), 'ASC NULLS LAST'],
        [sequelize.col('lastMessage.createdAt'), 'ASC NULLS LAST'],
      ]
      break

    case 'shortestWaitingTime':
      // Ordena pelo menor tempo de espera do contato
      // Primeiro por isFromMe (false = aguardando resposta), depois pela data mais recente
      newQuery.order = [
        [sequelize.col('lastMessage.isFromMe'), 'ASC NULLS LAST'],
        [sequelize.col('lastMessage.createdAt'), 'DESC NULLS LAST'],
      ]
      break
    default:
      // Ordena pelos contatos que possuem ticket aberto e depois pelos contatos que possuem última mensagem
      if (query.orderByLastMessage) {
        newQuery.order = sequelize.literal(
          `${
            query.orderByCurrentTicketCreatedAt ? '"currentTicket.createdAt" ASC, ' : ''
          } "Contact"."lastMessageAt" desc nulls last`,
        )
      } else if (query.orderByCurrentTicketCreatedAt) {
        newQuery.order = sequelize.literal(
          `"currentTicket.createdAt" ASC ${limit >= 0 ? `limit ${limit}` : ''} ${
            offset >= 0 ? `offset ${query.offset}` : ''
          }`,
        )
        delete newQuery.limit
        delete newQuery.offset
      } else {
        newQuery.order = [
          [sequelize.literal('CASE WHEN "Contact"."lastMessageAt" IS NOT NULL THEN 1 ELSE 0 END'), 'DESC'],
          ['lastMessageAt', 'DESC'],
        ]
      }
  }

  return newQuery
}

const forward = promiseHandler(async (req: Request, res: Response) => {
  const query = parseQuery(req.query)
  const { ticketsEnabled, departmentsIds } = req.query

  const newQuery = {
    ...query,
    include: [
      'avatar',
      'thumbAvatar',
      {
        model: 'currentTicket',
        attributes: ['departmentId'],
        ...(ticketsEnabled && {
          where: {
            departmentId: {
              $in: departmentsIds,
            },
            isOpen: true,
          },
        }),
      },
      {
        model: 'service',
        required: true,
        where: Sequelize.literal(`case when "service"."type" in ('instagram', 'whatsapp-business', 'facebook-message')
                      then (now() - "Contact"."lastContactMessageAt") < interval '24 hour' and "Contact"."lastMessageAt" is not null
                      else true end`),
      },
    ],
  }

  return contactResource.findManyWithTotal(newQuery).then(async (result) => ({
    ...result,
    data: await fromReqRes(req, res).transformMany(transformer)(result.data),
  }))
})

const index = async (req, res, next) => {
  const query = parseQuery(req.query)

  if (!hasPermission(res.locals.user.permissions, 'groups.view')) {
    query.where = {
      ...query.where,
      isGroup: false,
    }
  }

  if (req.body.includeTicketTransfer) {
    const result = {
      data: [],
      total: 0,
    }

    let page = 1
    do {
      const res = await contactResource.findManyPaginated({
        ...query,
        page,
        perPage: 10,
      })
      result.data.push(res.data || [])
      result.total = res.total
      page += 1
    } while (res.data.length)

    const context = {
      account: res.locals.account,
      user: res.locals.user,
      impersonate: res.locals.impersonate,
    }
    return res.send({
      ...result,
      data: await contactResource.includeTicketTransfer(result.data, context),
    })
  }

  if (req.query.orderByGroupParticipantsIsAdmin) {
    req.query.order.unshift([sequelize.col('groups.group_participants.isAdmin'), 'desc'])
    req.query.order.unshift([sequelize.col('groups.group_participants.isSuperAdmin'), 'desc'])
    req.query.order.unshift([sequelize.col('isMe'), 'desc'])
  }

  return baseIndex(req, res, next)
}

const postIndex = async (req, res, next) => {
  const query = parseQuery(req.body)

  if (req.body.includeTicketTransfer) {
    if (!hasPermission(res.locals.user.permissions, 'groups.view')) {
      query.where = {
        ...query.where,
        isGroup: false,
      }
    }

    const result = await contactResource.findManyWithTotal(query)

    if (result.data?.length) {
      const ids = result.data.map((r) => r.id).join("','")

      const tags = await sequelize.query(
        `select * from tags t inner join contact_tags ct on ct."tagId" = t.id and ct."contactId" in ('${ids}') left join tag_departments td on td."tagId" = t.id`,
        {
          type: QueryTypes.SELECT,
        },
      )

      const { user } = res.locals

      const availableTags =
        user.isSuperAdmin || user.roles.some((role) => role.isAdmin)
          ? tags
          : tags.filter((t) => !t.departmentId || user.departments.some((d) => d.id === t.departmentId))

      const groupedTags = groupBy(availableTags, (t) => t.contactId)

      result.data = await Promise.all(
        result.data.map(async (r) => {
          let mentions = []

          if (r.dataValues.lastMessageId && r.dataValues.lastMessage?.data?.mentionedList?.length) {
            const mentionedList = r.dataValues?.lastMessage?.data?.mentionedList?.map((mention) => mention.id) || []
            if (mentionedList.length) {
              const mapContacts = await contactResource.findMany({
                attributes: ['id', 'name', 'data'],
                where: {
                  id: {
                    $in: mentionedList,
                  },
                },
              })

              mentions = mapContacts.map((contact) => ({
                id: contact.id,
                name: contact.name,
                numberToRender: `@${contact.data.number}`,
              }))
            }
          }

          return {
            ...r.dataValues,
            tags: uniqBy(groupedTags[r.id], 'id').map(({ departmentId, ...tagProps }) => tagProps) || [],
            ...(mentions.length && { mentionedList: mentions }),
          }
        }),
      )
    }
    const context = {
      account: res.locals.account,
      user: res.locals.user,
      impersonate: res.locals.impersonate,
    }
    return res.send({
      ...result,
      data: await contactResource.includeTicketTransfer(result.data, context),
    })
  }

  return sendPostIndex(req, res, next)
}

// eslint-disable-next-line complexity
const create = promiseHandler(async (req, res) => {
  const reqData: Partial<ContactInstance & { number?: string; email?: string }> = {
    ...pick(req.body, [
      'unsubscribed',
      'number',
      'email',
      'tagIds',
      'data',
      'defaultDepartmentId',
      'defaultUserId',
      'serviceId',
      'personId',
      'accountId',
      'mergeRelations',
      'note',
      'info',
      'customFields',
      'block',
      'dataBlock',
      'origin',
      'customFilter',
      'isGroup',
    ]),
    name: req.body.name || req.body.internalName,
    internalName: req.body.internalName || req.body.name,
    visible: true,
  }
  const { serviceId, accountId, customFields } = req.body
  const query = filterQuery(req.query, includeWhitelist)

  const service = await serviceResource.findById(reqData?.serviceId, {
    attributes: ['id', 'type', 'data'],
    cache: true,
  })

  if (!service) throw new BadRequestHttpError(`Invalid service #${serviceId}.`)

  const email = service.type === 'email' ? reqData.email || reqData.data?.email : ''
  const rawNumber = String(reqData.number || reqData.data?.number)

  let number = service.type.match(/^whatsapp.*$|^sms-wavy$/) ? parseNumber(rawNumber) : ''

  const whatsappService = await getServiceRpc('whatsapp-remote')

  if (!reqData.isGroup && !number && !email)
    throw new BadRequestHttpError('Required properties (number or email) were not provided.')

  if (reqData.isGroup && service.type === 'whatsapp') {
    if (!hasPermission(res.locals.user.permissions, 'groups.create')) {
      throw new HttpError(403, 'The creation cannot proceed because the user does not have permission to create group')
    }

    const group = configValues.workersGoServices.includes(service.type)
      ? await Container.get(HttpJobsDispatcher).dispatch(
          'create-group',
          {
            serviceId,
            name: reqData.name,
            contactIds: [service.data.myId],
          },
          {
            timeout: 2 * 60_000,
            useWorkersGo: true,
          },
        )
      : await whatsappService.wplvCreateGroup(serviceId, reqData.name, [service.data.myId])

    reqData.idFromService = group.id
    reqData.number = parseNumber(group.id)
    number = parseNumber(group.id)
  }

  let contact = await contactResource.findOne({
    where: {
      accountId,
      serviceId,
      idFromService: comparableIdFromService(service.type === 'email' ? email : rawNumber, service.type),
      archivedAt: null,
    },
  })

  const data = contact
    ? {
        ...omit(reqData, ['number', 'email', 'origin']),
        data: omit(reqData.data, ['number', 'email', 'validNumber']),
      }
    : {
        ...reqData,
        number,
        data: {
          ...reqData.data,
          number,
          email,
        },
        origin: ['web', 'app'].includes(reqData?.origin) ? reqData.origin : ContactOrigin.API,
      }

  contact = contact
    ? await contactResource.update(contact, data, { ...query, mergeJson: ['data'] })
    : await contactResource.create(data, {
        ...query,
        skipValidation: !!email,
      })

  if (contact.isGroup) {
    if (configValues.workersGoServices.includes(service.type)) {
      await Container.get(HttpJobsDispatcher).dispatch(
        'sync-group-participants',
        {
          serviceId,
          groupId: contact.id,
        },
        {
          timeout: 2 * 60_000,
          useWorkersGo: true,
        },
      )
      await Container.get(HttpJobsDispatcher).dispatch(
        'sync-group-by-id',
        {
          serviceId,
          groupId: contact.id,
        },
        {
          timeout: 2 * 60_000,
          useWorkersGo: true,
        },
      )
    } else {
      await whatsappService.syncGroupParticipants(contact.serviceId, contact.id)
      await whatsappService.syncGroupById(contact.serviceId, contact.id)
    }
  }

  if (customFields?.length) {
    await queuedAsyncMap<CustomFieldValueInstance, any>(customFields, async (field) => {
      const fieldId = field.fieldId ?? field.customFieldId ?? field.id
      if (!fieldId) {
        throw new HttpError(400, `Structure field incorrect for #${fieldId || `value:${field.value}`}`)
      }

      const fieldCustom = await customFieldsResource.existsById(fieldId)
      if (!fieldCustom) {
        throw new HttpError(400, `The custom field not exists in ${fieldId}]`)
      }

      return customFieldValuesResource.save({
        relatedId: contact.id,
        relatedType: 'contact',
        customFieldId: fieldId,
        value: typeof field.value === 'string' ? field.value : JSON.stringify(field.value),
      })
    })
  }

  return fromReqRes(req, res).transform(transformer)(contact)
})

const update = promiseHandler(async (req, res) => {
  const { id } = req.params

  const { permissions } = res.locals.user

  const userCantViewNumberAndCantEditContact =
    !hasPermission(permissions, 'contacts.view.number') && !hasPermission(permissions, 'contacts.update')

  if (userCantViewNumberAndCantEditContact) {
    throw new HttpError(
      403,
      'The update cannot proceed because the user does not have permission to view numbers and edit contacts',
    )
  }

  const shouldIgnoreChangingTheNumber =
    hasPermission(permissions, 'contacts.update') && !hasPermission(permissions, 'contacts.view.number')

  let reqData = {
    ...pick(req.body, [
      'unsubscribed',
      'name',
      'internalName',
      'number',
      'email',
      'tagIds',
      'tags',
      'data',
      'serviceId',
      'defaultDepartmentId',
      'defaultUserId',
      'personId',
      'accountId',
      'mergeRelations',
      'isSilenced',
      'note',
      'info',
      'service.type',
      'unread',
      'onlyAdminCanSendMessage',
      'onlyAdminCanEditProperties',
      'setAdminApproval',
      'setMemberAddMode',
      'groupDescription',
    ]),
  }

  if (!validator.isUUID(id)) {
    console.log(`UUID is invalid for: ${id}`)
    throw new HttpError(400, `UUID is invalid for: ${id}`)
  }

  const contact = await contactResource.findById(id)

  if (!contact) {
    throw new HttpError(400, `Contact not exist for: ${id}`)
  }

  if (shouldIgnoreChangingTheNumber && !contact.data.email && contact.data.number) {
    const number = contact?.data?.number
    reqData = {
      ...reqData,
      number,
      data: {
        ...contact?.data,
        ...(typeof reqData.data?.unread === 'boolean' && {
          unread: reqData.data.unread,
        }),
        number,
      },
    }
  }

  let configGroup = {}
  if (contact.isGroup) {
    if (!hasPermission(permissions, 'groups.update')) {
      throw new HttpError(403, 'The update cannot proceed because the user does not have permission to edit groups')
    }

    const whatsappService = await getServiceRpc('whatsapp-remote')
    if (reqData.onlyAdminCanSendMessage !== undefined) {
      const onlyAdminCanSendMessage = await whatsappService.wplvOnlyAdminCanSendMsgGroup(
        contact.serviceId,
        contact.idFromService,
        reqData.onlyAdminCanSendMessage,
      )
      configGroup.onlyAdminCanSend =
        onlyAdminCanSendMessage === 'OK' ? reqData.onlyAdminCanSendMessage : onlyAdminCanSendMessage
    }

    if (reqData.onlyAdminCanEditProperties !== undefined) {
      const onlyAdminCanEditProperties = await whatsappService.wplvOnlyAdminCanEditProperties(
        contact.serviceId,
        contact.idFromService,
        reqData.onlyAdminCanEditProperties,
      )
      configGroup.onlyAdminCanEditProperties =
        onlyAdminCanEditProperties === 'OK' ? reqData.onlyAdminCanEditProperties : onlyAdminCanEditProperties
    }

    if (reqData.setAdminApproval !== undefined) {
      const setAdminApproval = await whatsappService.wplvSetAdminApproval(
        contact.serviceId,
        contact.idFromService,
        reqData.setAdminApproval,
      )
      configGroup.membershipApprovalMode = setAdminApproval === 'OK' ? reqData.setAdminApproval : setAdminApproval
    }

    if (reqData.setMemberAddMode !== undefined) {
      const setMemberAddMode = await whatsappService.wplvSetMemberAddMode(
        contact.serviceId,
        contact.idFromService,
        reqData.setMemberAddMode,
      )
      configGroup.memberAddMode = setMemberAddMode === 'OK' ? reqData.setMemberAddMode : setMemberAddMode
    }

    if (reqData.groupDescription !== undefined) {
      reqData.groupDescription = reqData.groupDescription == '' ? ' ' : reqData.groupDescription
      const groupDescription = await whatsappService.wplvSetGroupDescription(
        contact.serviceId,
        contact.idFromService,
        reqData.groupDescription,
      )
      configGroup.description = groupDescription === 'OK' ? reqData.groupDescription : groupDescription
    }
  }

  const { customFields } = req.body
  if (customFields && customFields.length) {
    await queuedAsyncMap<CustomFieldValueInstance, any>(customFields, async (field) => {
      const customFieldId = field.fieldId ?? field.customFieldId ?? field.id
      if (!customFieldId) {
        throw new HttpError(400, `Structure field incorrect for # ${customFieldId || `value:${field.value}`}`)
      }

      const fieldCustom = await customFieldsResource.existsById(customFieldId)
      if (fieldCustom) {
        return customFieldValuesResource.save({
          relatedId: id,
          relatedType: 'contact',
          customFieldId,
          value: typeof field.value === 'string' ? field.value : JSON.stringify(field.value),
        })
      }
    })
  }
  const query = filterQuery(req.query, includeWhitelist)
  if (reqData.service && reqData.service.type === 'email' && reqData.email) {
    reqData.data = {
      ...reqData.data,
      email: reqData.email,
    }
  }

  if (reqData.tagIds) {
    const dataTagIds = reqData.tagIds
    const dataTags = await tagResource.findManyByIds(dataTagIds)

    const contactTags = await tagResource.findMany({
      include: [
        'departments',
        {
          model: 'contacts',
          where: {
            id: id,
          },
        },
      ],
    })

    const user = res.locals.user
    const isAdmin = user?.isSuperAdmin || user?.roles?.some((role) => role.isAdmin)

    const contactTagsNotSeenByTheUser = contactTags.filter(
      (tag) =>
        !isAdmin &&
        !tag.departments.some((department) => user.departments.some((d) => d.id === department.id)) &&
        tag.departments?.length,
    )

    reqData.tags = [...dataTags, ...contactTagsNotSeenByTheUser]
  }

  let contactReturn = await contactResource
    .update(contact, reqData, query)
    .then(fromReqRes(req, res).transform(transformer))

  if (Object.keys(configGroup)?.length === 0) {
    return contactReturn
  }

  return {
    ...contactReturn,
    configGroup,
  }
})

const updateContactList = promiseHandler((req, res) =>
  contactResource.updateContactList({
    ...req.body,
    accountId: res.locals.accountId,
  }),
)

type ContactCreateManyDTO = {
  internalName: string
  mergeRelations: boolean
  name: string
  number: string
  personId?: string
  defaultDepartmentId?: string
  defaultUserId?: string
  serviceId: string
  tagIds: string[]
  data?: {
    number: string
    email?: string
    unread?: boolean
  }
}

const getServices = async (ids: string[]): Promise<{}> => {
  const services = await serviceResource.findManyByIds(ids, {
    attributes: ['id', 'type'],
  })
  return arrayToObject(services, 'id', 'type')
}

const createOrUpdateMany = promiseHandler(async (req, res) => {
  const data: ContactCreateManyDTO[] = req.body
  const query = filterQuery(req.query, includeWhitelist)
  const servicesIds = data.map((dt) => dt.serviceId)
  const services = await getServices(servicesIds)

  const uniqContactsData: ContactInstance[] = Object.values(
    data.reduce((acc, cur) => {
      const number = cur.number || cur.data.number
      const isEmail = validator.isEmail(number)
      const fullNumber = (!isEmail && parseNumber(number)) || number
      const defaultDepartmentId = cur.defaultDepartmentId
      const defaultUserId = cur.defaultUserId
      const idFromService = services[cur.serviceId] === 'whatsapp' ? formatIdFromNumber(fullNumber) : fullNumber

      const numbers =
        services[cur.serviceId] !== 'email' && fullNumber.startsWith('55') && [12, 13].includes(fullNumber.length)
          ? getPossibleNumbers(number)
          : [number]

      if (numbers.some((n) => acc[n])) {
        return acc
      }

      return {
        ...acc,
        [fullNumber]: {
          ...omit(cur, ['data']),
          idFromService,
          number: !isEmail ? fullNumber : '',
          data: {
            number: !isEmail ? fullNumber : '',
            email: isEmail ? fullNumber : '',
          },
          defaultDepartmentId,
          defaultUserId,
          accountId: res.locals.accountId,
          visible: true,
        },
      }
    }, {}),
  )

  const contactsDataValidated = await queuedAsyncMap(uniqContactsData, async (contactData) => {
    const serviceType = services[contactData.serviceId]
    const { idFromService } = contactData || {}
    const { email } = contactData.data || {}

    const contact = await contactResource.findOne({
      where: {
        serviceId: contactData.serviceId,
        idFromService: comparableIdFromService(serviceType === 'email' ? email : idFromService, serviceType),
        archivedAt: null,
      },
    })

    return {
      ...contact,
      ...contactData,
      name: contactData.name || contact?.name,
      internalName: contact?.internalName || contactData.internalName,
      idFromService: contact?.idFromService || contactData?.idFromService,
      data: {
        ...{ number: contactData.data?.number },
        ...(contactData.data?.email && { email: contactData.data?.email }),
        ...contact?.data,
      },
    }
  })

  const contactsData = uniqBy(contactsDataValidated.filter(Boolean), 'idFromService')

  const contactsDataMap = groupBy(contactsData, (c) => c.idFromService)

  const groupedContacts = groupBy(contactsData, (c) => c.serviceId)
  const groupedContactsEntries = Object.entries(groupedContacts)

  const existingContacts = flatten(
    await Promise.all(
      groupedContactsEntries.map(([serviceId, contacts]) =>
        getContactsByServiceIdAndIdsFromService(
          serviceId,
          contacts.map((c) => c.idFromService),
        ),
      ),
    ),
  )

  const newContacts = differenceBy(contactsData, existingContacts, (c) => c.idFromService)

  const existingContactsDatas = existingContacts.map((c) => contactsDataMap[c.idFromService][0])

  const createdContacts = await contactResource
    .createMany(newContacts, {
      skipValidation: true,
    })
    .catch((e) => [])

  const updatedContacts = await contactResource
    .updateMany(existingContacts, existingContactsDatas, query)
    .catch((e) => [])

  const contacts = [...createdContacts, ...updatedContacts]

  return fromReqRes(req, res).transformMany(transformer)(contacts)
})

const markRead = promiseHandler((req, res) => {
  const { id } = req.params

  return contactResource.markReadById(id).then(Boolean)
})

const sync = promiseHandler(async (req, res) => {
  const { id } = req.params
  const { forceSync } = req.body

  const contact = await contactResource.markReadById(id, {
    include: ['service'],
  })

  const mustSync =
    contact.service.type === 'whatsapp' && config('workersGoServices').includes(contact.service.type)
      ? false
      : forceSync ||
        (['whatsapp', 'whatsapp-remote-pod'].includes(contact.service.type) &&
          (!('unread' in contact.data) ||
            !contact.data.lastSyncAt ||
            isAfter(new Date(contact.service.data.lastSyncAt), new Date(contact.data.lastSyncAt))))

  const unreadAlertToBlock = contact.data?.blockMessageRules?.unreadAlertToBlock

  const mustUpdate = mustSync || !('unread' in contact.data) || contact.data.unread || unreadAlertToBlock

  return Promise.all([
    ...(mustSync ? [contactResource.syncFromServiceById(contact)] : []),
    ...(mustUpdate
      ? [
          contactResource.update(
            contact,
            {
              data: {
                unread: false,
                ...(mustSync && { lastSyncAt: new Date() }),
                ...(unreadAlertToBlock && {
                  blockMessageRules: {
                    ...contact.data?.blockMessageRules,
                    unreadAlertToBlock: false,
                  },
                }),
              },
            },
            {
              mergeJson: ['data'],
            },
          ),
        ]
      : []),
  ]).then(() => true)
})

const destroyMany = promiseHandler(async (req, res) => {
  const { accountId, userId } = res.locals
  const { where, include, customFilter } = req.body
  const query = {
    include,
    customFilter,
    where: {
      ...where,
      accountId,
    },
  }

  await iteratePaginated(
    ({ page }) =>
      contactResource.findManyPaginated({
        ...query,
        page,
        perPage: 500,
      }),
    async (contact) => {
      await contactResource.destroy(contact, { dontEmit: true }, { byUserId: userId })
    },
  )

  return true
})

const exportCSV = async (req: Request, res: Response) => {
  const { accountId } = res.locals
  const { permissions } = res.locals.user
  const { serviceType } = req.body
  const language = res.locals.user?.language || 'pt-BR'

  const userCanViewNumber = res.locals.impersonate ? false : hasPermission(permissions, 'contacts.view.number')

  const query = {
    ...req.body,
    where: {
      ...req.body.where,
      accountId,
    },
    include: prepareInclude([
      ...((req.body && req.body.include) || []),
      { model: 'defaultDepartment', attributes: ['name'], required: false },
      { model: 'defaultUser', attributes: ['name'], required: false },
      { model: 'tags', attributes: ['label'], required: false },
      { model: 'customFieldValues' },
    ]),
    attributes: ['id', 'internalName', 'name', 'alternativeName', 'data', 'idFromService', 'createdAt'],
    order: req.body.order ?? [
      ['createdAt', 'DESC'],
      ['id', 'DESC'],
    ],
    distinct: true,
    subQuery: false,
  }

  const getContactName = (contact: ContactInstance) => contactResource.getContactName(contact)

  const { type } = req.body
  const delimiter = type === 'colon' ? ';' : ','

  const stringifier = stringify({ delimiter })

  stringifier.pipe(res)

  const header = getHeaderCsvFile(serviceType, type)[language]

  const customFields = await customFieldsResource.findMany({
    attributes: ['id', 'name', 'type', 'settings'],
    where: { allowed: 'contacts', accountId },
  })

  customFields.forEach((field) => {
    header.push(field.name)
  })

  stringifier.write(header)

  await iteratePaginated(
    ({ page }) =>
      contactResource.findManyPaginated({
        ...query,
        page,
        perPage: 500,
      }),
    async (contact) => {
      const name = userCanViewNumber
        ? getContactName(contact)
        : hideContactNumbers({ name: getContactName(contact) })?.name

      const separateNumber = separateDDIFromTheNumber(contact.data.number)
      const number = userCanViewNumber
        ? separateNumber?.numberWithoutDDI ?? contact.data.number
        : hidePhoneNumber(separateNumber?.numberWithoutDDI ?? contact.data.number)?.value

      const email = contact?.data?.email || contact?.idFromService

      const createdAt = contact.createdAt

      const row = getRowByService(
        serviceType,
        contact,
        name,
        separateNumber?.ddi,
        number,
        email,
        createdAt,
        customFields,
      )
      stringifier.write(row)
    },
    10,
  )
  stringifier.end()
}

const importFromGroups = promiseHandler(async (req, res) => {
  const { accountId } = res.locals
  const query = filterQuery(req.query, includeWhitelist)
  const { serviceId, groups, defaultUserId, defaultDepartmentId } = req.body
  const data = {
    accountId,
    serviceId,
    defaultDepartmentId,
    defaultUserId,
    groups,
  }
  return contactResource.importFromGroup(data, query)
})

const loadEarlierMessages = promiseHandler((req, res) => {
  const { id } = req.params
  const query = filterQuery(req.query, includeWhitelist)
  const { timestamp } = req.body

  return contactResource.loadEarlierMessagesById(id, timestamp, query).then(() => true)
})

const closeTicket = promiseHandler(async (req, res) => {
  const { id } = req.params
  const byUserId = req.user?.id
  const { ticketTopicIds, comments, aiSummaryRating } = req.body

  const data = {
    byUserId,
    ticketTopicIds,
    comments,
    aiSummaryRating,
  }

  return getCloseTicketQueue(id).run(() =>
    contactResource
      .closeTicketById(id, data)
      .then(() => true)
      .catch(() => false),
  )
})

const transferTicket = promiseHandler((req, res) => {
  const { id } = req.params
  const byUserId = req.user?.id
  const data = {
    ...pick(req.body, ['departmentId', 'userId', 'comments']),
    byUserId,
    protocolsEnabled: res.locals.account.settings && res.locals.account.settings.protocolsEnabled,
  }

  return contactResource.transferTicketById(id, data).then(() => true)
})

const updateTicket = promiseHandler((req, res) => {
  const { id } = req.params
  const data = {
    ...pick(req.body, ['departmentId', 'userId', 'ticketTopicId', 'comments']),
  }

  return contactResource.updateTicketById(id, data).then(() => true)
})

const exists = promiseHandler(async (req, res) => {
  const { ids: rawIds, numbers = [], serviceId } = req.query

  const ids: string[] = (rawIds || numbers.filter(Boolean).map(formatIdFromNumber)).filter(Boolean)

  const items = await queuedAsyncMap(ids, async (id) => ({
    id,
    exists: await driverService.contactExists(serviceId, id),
  }))

  return items.reduce((obj, item) => {
    obj[idToNumber(item.id)] = item.exists
    return obj
  }, {})
})

const bulkTransfer = promiseHandler(async (req, res) =>
  ticketResource.bulkTransfer(req.body, req.user, res.locals.account.settings?.protocolsEnabled || false),
)

const destroy = promiseHandler(async (req, res) => {
  const { id } = req.params
  const byUserId = req.user?.id

  return contactResource.destroyById(id, { dontEmit: false }, { byUserId })
})

const cachedTicketsCount = promiseHandler(async (req, res, next) => {
  const { accountId } = res.locals
  const { id: userId, permissions } = res.locals.user
  const { isFiltered = false, departmentIds = [] } = req.body.query
  const canUseCaching = () => !isFiltered

  if (config('useCachedTicketsCount') && canUseCaching()) {
    const result = await contactResource.getTicketsCount(accountId, userId, departmentIds, permissions)
    if (!result) next()
    return res.send(result)
  }
  next()
})

const block = promiseHandler(async (req, res) => {
  const { block, byUserId, description } = req.body
  const { id } = req.params

  const { permissions } = res.locals.user

  const userCanBlockContact = hasPermission(permissions, 'contacts.block')

  if (!userCanBlockContact || !res.locals.account.settings.isBlockListActive) {
    throw new HttpError(400, 'Update cannot proceed because the user does not have a block list permission`s')
  }

  const contact = await contactResource.findById(id)

  if (!contact) {
    throw new HttpError(400, `Contact not exist for: ${id}`)
  }

  if (block && !!contact?.currentTicketId) {
    const data = {
      byUserId,
      ticketTopicIds: [],
      comments: '',
    }

    await getCloseTicketQueue(id).run(() => contactResource.closeTicketById(id, data))
  }

  return transformer(
    await contactResource.updateById(id, {
      block,
      dataBlock: {
        level: block ? 3 : 2,
        date: new Date(),
        description,
        byUserId,
      },
    }),
    {
      account: res.locals.account,
      user: res.locals.user,
      impersonate: res.locals.impersonate,
    },
  )
})

const contactBlockedByMessageRule = promiseHandler(async (req) => {
  const { id } = req.params

  return blockMessageRuleResource.contactBlockedByMessageRule(id)
})

const show = promiseHandler(async (req, res) => {
  const { id } = req.params
  const query = filterQuery(req.query, includeWhitelist)

  let contact = await contactResource.findById(id, query).then(fromReqRes(req, res).transform(transformer))

  if (!contact?.isGroup) {
    return contact
  }

  if (!hasPermission(res.locals.user.permissions, 'groups.view')) {
    throw new HttpError(403, 'Show cannot proceed because the user does not have a groups show permission')
  }

  const whatsappService = await getServiceRpc('whatsapp-remote')
  const groupConfig = await whatsappService
    .wplvGetGroupConfig(contact.serviceId, contact.idFromService)
    .catch((e) => null)

  if (contact.isGroup) {
    await driverService.syncGroupParticipants(contact.serviceId, contact.id).catch(reportError)
  }

  return {
    ...contact,
    groupConfig,
  }
})

// ============================================================================
// Routes
// ============================================================================
const router = Router()

router.use(accountContext())

router.post('/export-template', controller.exportTemplate)
router.get('/', index)
router.get('/forward', forward)
router.post('/list', postIndex)
router.get('/count', count)
router.post('/count', cachedTicketsCount, countPost)
router.get(
  '/exists',
  validate({
    query: {
      serviceId: [required, string, uuid4, currentAccountOwnsService],
      numbers: [required, arrayOf(string)],
    },
  }),
  exists,
)
router.get('/:contactId/media/count', controller.countMediaByContact)
router.post('/send-email-follow-up-webchat', controller.sendEmailFollowUpWebchat)
router.get('/:id', show)
router.post(
  '/',
  validateCreate,
  // checkAndOrganizeCustomFieldsPayload, // TODO Retornar depois de concluída as validações dos novos campos e alinhamento com Produtos
  // validateSchema(customFieldsSchema, customFieldsMapError), // TODO Retornar depois de concluída as validações dos novos campos e alinhamento com Produtos
  create,
)
router.post('/export/csv', exportCSV)
// TODO validate
router.post('/many', createOrUpdateMany)
router.post('/groups', validateImportGroup, importFromGroups)
router.put(
  '/:id',
  validateUpdate,
  // checkAndOrganizeCustomFieldsPayload, // TODO Retornar depois de concluída as validações dos novos campos e alinhamento com Produtos
  // validateSchema(customFieldsSchema, customFieldsMapError), // TODO Retornar depois de concluída as validações dos novos campos e alinhamento com Produtos
  update,
)
router.post('/:id/mark-read', markRead)
router.post('/:id/sync', sync)
router.post('/:id/load-earlier-messages', loadEarlierMessages)
router.delete('/many', destroyMany)
router.delete('/:id', destroy)
router.post('/:id/ticket/close', closeTicket)
router.post('/:id/ticket/summary', controller.summaryTicket)
router.post('/:id/ticket/transfer', transferTicket)
router.post('/ticket/bulk-transfer', bulkTransfer)
router.put('/:id/ticket', updateTicket)
router.post('/update-contact-list', updateContactList)
router.post('/import-contacts', controller.verifyHeader, controller.importContacts)
router.post('/:id/block', block)
router.get('/:id/block-message-rule', contactBlockedByMessageRule)
router.post('/:contactId/add-members', controller.addMembers)
router.post('/:contactId/remove-members', controller.removeMembers)
router.post('/:contactId/leave-group', controller.leaveGroup)
router.post('/:contactId/promote-members', controller.promoteMembers)
router.post('/:contactId/demote-members', controller.demoteMembers)
router.get('/:contactId/media', controller.findMediasByContact)
export default router
