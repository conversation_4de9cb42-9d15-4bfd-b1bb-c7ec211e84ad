import { Request } from 'express'
import { Container, Inject } from 'typedi'
import QueueJobsDispatcher from '../../../core/services/jobs/queue/QueueJobsDispatcher'
import messageResource from '../../../core/resources/messageResource'
import QueuedAudioTranscribeJob from '../../workers/jobs/transcribe/QueuedAudioTranscribeJob'

export default class TranscriptsController {
  @Inject()
  queueJobsDispatcher: QueueJobsDispatcher

  protected resource = messageResource

  async transcribe(req: Request, res: any): Promise<void> {
    const queueJobsDispatcher = Container.get(QueueJobsDispatcher)
    const idMessage = req.params.id

    await this.resource?.updateById(idMessage, { isTranscribing: true, transcribeError: false })

    await queueJobsDispatcher.dispatch<QueuedAudioTranscribeJob>(
      'queued-audio-transcribe',
      { idMessage, userId: res.locals?.user?.id },
      {
        hashKey: idMessage,
      },
    )
  }
}
