import { Container } from 'typedi'
import { stringify } from 'csv-stringify'
import BaseResourceController from './BaseResourceController'
import { ContactInstance } from '../../../core/dbSequelize/models/Contact'
import contactResource, { IFindMediaByContactInput } from '../../../core/resources/contactResource'
import contactTransformer from '../../../core/transformers/contactTransformer'
import ContactService from '../../../core/services/contacts/ContactService'
import { promiseHandler } from '../../../core/utils/routing/resourceRouter'
import HttpError from '../../../core/utils/error/HttpError'
import { getHeaderCsvFile } from '../../../core/services/contacts/ContactService'
import { setWebchatIdleStage } from '../../workers/jobs/contact/WebchatContactIdleStagesJob'
import HttpJobsDispatcher from '../../../core/services/jobs/http/HttpJobsDispatcher'
import SendMailFollowUpWebchatJob from '../../workers/jobs/contact/SendMailFollowUpWebchat'
import queuedAsyncMap from '../../../core/utils/array/queuedAsyncMap'
import driverService, { getServiceRpc } from '../../../core/services/driverService'
import errorTransformer from './whatsapp/errorTransformer'
import { numberToId, parseNumber } from '../../../core/utils/whatsapp/numberParser'
import comparableIdFromService from '../../../core/utils/whatsapp/comparableIdFromService'
import reportError from '../../../core/services/logs/reportError'
import hasPermission from '../../../core/utils/hasPermission'
import configValues from '../../../core/configValues'

const httpJobsDispatcher = Container.get(HttpJobsDispatcher)

export default class ContactsController extends BaseResourceController<ContactInstance> {
  protected contactService: ContactService

  protected resource = contactResource

  protected transformer = contactTransformer

  protected includeWhitelist = ['organizations', 'contacts', 'contacts.avatar', 'contacts.thumbAvatar']

  verifyHeader = promiseHandler(async (req, res, next) => {
    const { service, csvHeaderLine, type } = req.body
    const language = res.locals.user?.language || 'pt-BR'
    const validateHeaderCsv = getHeaderCsvFile(service.type, type)[language].every((field) => {
      return csvHeaderLine.includes(field)
    })

    if (!validateHeaderCsv) {
      next(new HttpError(403, 'Csv File - Invalid Header'))
      return
    }
    next()
  })

  exportTemplate = async (req, res) => {
    const { serviceType, type } = req.body
    const delimiter = ';'
    const language = res.locals.user?.language || 'pt-BR'
    const stringifier = stringify({ delimiter })
    stringifier.pipe(res)
    stringifier.write(getHeaderCsvFile(serviceType, type)[language])
    stringifier.end()
  }

  importContacts = promiseHandler(async (req, res) => {
    const { tagsIds, defaultDepartmentId, defaultUserId, file, service, csvHeaderLine, type } = req.body
    const accountId = res.locals.accountId
    const language = res.locals.user?.language || 'pt-BR'
    const contactService = Container.get(ContactService)

    return contactService.importCSV({
      serviceId: service.id,
      serviceType: service.type,
      fileId: file.id,
      csvHeaderLine,
      type,
      defaultDepartmentId,
      defaultUserId,
      accountId,
      tagsIds,
      language,
    })
  })

  sendEmailFollowUpWebchat = async (req, res) => {
    const { id: contactId } = req.body

    const contact = await contactResource.findById(contactId)
    await setWebchatIdleStage(contact, { 2: new Date() })
    await httpJobsDispatcher.dispatch<SendMailFollowUpWebchatJob>('send-mail-follow-up-webchat', {
      contact,
      origin: 'manual',
    })

    return res.json(true)
  }

  addMembers = promiseHandler(async (req, res) => {
    if (!hasPermission(res.locals.user.permissions, 'groups.add.members')) {
      throw new HttpError(
        403,
        'The add members cannot continue because the user does not have permission to add members',
      )
    }
    let payload = req.body
    if (!hasPermission(res.locals.user.permissions, 'contacts.view.number')) {
      const contactId = payload.map((item) => item.contactId)
      const contacts = await contactResource.findMany({
        where: { id: { $in: contactId } },
        attributes: ['id', 'idFromService'],
      })

      payload = contacts.map((c) => {
        return {
          contactId: c.id,
          number: c.idFromService.replace('@c.us', ''),
        }
      })
      if (payload.length != req.body.length) {
        let numbers = req.body.filter((item) => item.contactId == null)
        payload.push(...numbers)
      }
    }

    const whatsappService = await getServiceRpc('whatsapp-remote')
    const httpJobsDispatcher = Container.get(HttpJobsDispatcher)

    const group: ContactInstance = await contactResource.findById(req.params.contactId)

    const response = await queuedAsyncMap(payload, async (contact: { contactId?: string; number?: string }) => {
      const number = numberToId(contact.number)

      return (
        configValues.workersGoServices.includes('whatsapp')
          ? httpJobsDispatcher.dispatch(
              'add-group-participants',
              {
                serviceId: group.serviceId,
                groupId: group.idFromService,
                participantIds: [number],
              },
              {
                timeout: 2 * 60_000,
                useWorkersGo: true,
              },
            )
          : whatsappService.wplvAddGroupParticipants(group.serviceId, group.idFromService, [number], 'v2')
      )
        .then(
          async (response: {
            success: boolean
            participants: {
              contactId: string
              success: boolean
              error?: string
              inviteCode?: string
              inviteCodeExpiration: string
            }[]
          }) => {
            let sentInviteMessage = false

            if (!response.success && response.participants?.length) {
              const participantResp = response.participants[0]

              if (participantResp?.inviteCode) {
                sentInviteMessage = await whatsappService
                  .sendGroupInviteMessage(group.serviceId, group.idFromService, {
                    inviteCode: participantResp.inviteCode,
                    inviteCodeExpiration: participantResp.inviteCodeExpiration,
                    contactId: participantResp.contactId || number,
                  })
                  .then(Boolean)
              } else if (!participantResp.success) {
                throw new Error(participantResp.error)
              }
            }

            const contactExists =
              (await (contact.contactId && (await contactResource.findById(contact.contactId)))) ||
              (await contactResource.findOne({
                where: {
                  accountId: group.accountId,
                  serviceId: group.serviceId,
                  idFromService: comparableIdFromService(number + '@c.us', 'whatsapp'),
                  archivedAt: null,
                },
              }))

            if (!contactExists) {
              await contactResource.create({
                accountId: group.accountId,
                name: number,
                number,
                serviceId: group.serviceId,
                visible: false,
              })
            }

            return {
              sentInviteMessage,
              number: parseNumber(number),
              success: response.success || sentInviteMessage,
            }
          },
        )
        .catch(errorTransformer)
        .catch((e) => ({
          number: parseNumber(number),
          success: false,
          failReason: e.message,
        }))
    })

    if (configValues.workersGoServices.includes('whatsapp')) {
      await httpJobsDispatcher
        .dispatch(
          'sync-group-participants',
          {
            serviceId: group.serviceId,
            groupId: group.id,
          },
          {
            timeout: 2 * 60_000,
            useWorkersGo: true,
          },
        )
        .catch(reportError)
    } else {
      await driverService.syncGroupParticipants(group.serviceId, group.id).catch(reportError)
    }

    return response
  })

  removeMembers = promiseHandler(async (req, res) => {
    if (!hasPermission(res.locals.user.permissions, 'groups.remove.members')) {
      throw new HttpError(
        403,
        'The remove members cannot continue because the user does not have permission to remove members',
      )
    }
    const whatsappService = await getServiceRpc('whatsapp-remote')

    const group: ContactInstance = await contactResource.findById(req.params.contactId)

    const response = await queuedAsyncMap(req.body, async (contactId: ContactInstance['id']) => {
      const contact = await contactResource.findById(contactId, {
        attributes: ['idFromService'],
      })

      return (
        configValues.workersGoServices.includes('whatsapp')
          ? httpJobsDispatcher.dispatch(
              'remove-group-participants',
              {
                serviceId: group.serviceId,
                groupId: group.idFromService,
                participantIds: [contact.idFromService],
              },
              {
                timeout: 2 * 60_000,
                useWorkersGo: true,
              },
            )
          : whatsappService.wplvRemoveGroupParticipants(group.serviceId, group.idFromService, [contact.idFromService])
      )
        .then((r) => ({
          number: parseNumber(contact.idFromService),
          success: true,
        }))
        .catch(errorTransformer)
        .catch((e) => ({
          number: parseNumber(contact.idFromService),
          success: false,
          failReason: e.message,
        }))
    })

    if (configValues.workersGoServices.includes('whatsapp')) {
      await httpJobsDispatcher
        .dispatch(
          'sync-group-participants',
          {
            serviceId: group.serviceId,
            groupId: group.id,
          },
          {
            timeout: 2 * 60_000,
            useWorkersGo: true,
          },
        )
        .catch(reportError)
    } else {
      await driverService.syncGroupParticipants(group.serviceId, group.id).catch(reportError)
    }

    return response
  })

  leaveGroup = promiseHandler(async (req, res) => {
    if (!hasPermission(res.locals.user.permissions, 'groups.leave')) {
      throw new HttpError(
        403,
        'Leaving the group cannot continue because the user does not have permission to leave the group',
      )
    }
    const whatsappService = await getServiceRpc('whatsapp-remote')

    const group: ContactInstance = await contactResource.findById(req.params.contactId)

    return whatsappService.wplvLeaveGroup(group.serviceId, group.idFromService).then(async (r) => {
      await driverService.syncGroupParticipants(group.serviceId, group.id).catch(reportError)
      await driverService.syncGroupById(group.serviceId, group.id).catch(reportError)
      return r
    })
  })

  promoteMembers = promiseHandler(async (req, res) => {
    if (!hasPermission(res.locals.user.permissions, 'groups.promote.admin')) {
      throw new HttpError(
        403,
        'The promote member to admin cannot continue because the user does not have permission to promote member to admin',
      )
    }
    const whatsappService = await getServiceRpc('whatsapp-remote')

    const group: ContactInstance = await contactResource.findById(req.params.contactId)

    const response = await queuedAsyncMap(req.body, async (contactId: ContactInstance['id']) => {
      const contact = await contactResource.findById(contactId, {
        attributes: ['idFromService'],
      })

      return whatsappService
        .wplvPromoteGroupParticipants(group.serviceId, group.idFromService, [contact.idFromService])
        .then((r) => ({
          number: parseNumber(contact.idFromService),
          success: true,
        }))
        .catch(errorTransformer)
        .catch((e) => ({
          number: parseNumber(contact.idFromService),
          success: false,
          failReason: e.message,
        }))
    })

    await driverService.syncGroupParticipants(group.serviceId, group.id).catch(reportError)

    return response
  })

  demoteMembers = promiseHandler(async (req, res) => {
    if (!hasPermission(res.locals.user.permissions, 'groups.demote.admin')) {
      throw new HttpError(
        403,
        'The remove admin permission cannot continue because the user does not have permission to remove admin permission',
      )
    }
    const whatsappService = await getServiceRpc('whatsapp-remote')

    const group: ContactInstance = await contactResource.findById(req.params.contactId)

    const response = await queuedAsyncMap(req.body, async (contactId: ContactInstance['id']) => {
      const contact = await contactResource.findById(contactId, {
        attributes: ['idFromService'],
      })

      return whatsappService
        .wplvDemoteGroupParticipants(group.serviceId, group.idFromService, [contact.idFromService])
        .then((r) => ({
          number: parseNumber(contact.idFromService),
          success: true,
        }))
        .catch(errorTransformer)
        .catch((e) => ({
          number: parseNumber(contact.idFromService),
          success: false,
          failReason: e.message,
        }))
    })

    await driverService.syncGroupParticipants(group.serviceId, group.id).catch(reportError)

    return response
  })

  findMediasByContact = promiseHandler(async (req, res) => {
    const { contactId } = req.params
    const query = this.filterQuery(req.query, this.includeWhitelist)
    const formatQueryParams: IFindMediaByContactInput = {
      type: query?.where?.type,
      name: query?.where?.name,
      cursor: query?.where?.cursor,
      perPage: query?.perPage,
      order: query?.order,
    }

    const context = {
      account: res.locals.account,
      user: res.locals.user,
      impersonate: res.locals.impersonate,
    }
    return contactResource.findMediasByContact(contactId, context, formatQueryParams)
  })

  countMediaByContact = promiseHandler(async (req, res) => {
    const { contactId } = req.params
    return contactResource.countContactMedias(contactId)
  })

  summaryTicket = promiseHandler((req, res) => {
    const { id } = req.params
    const isAdmin = res.locals?.user?.roles.find((role) => role.isAdmin)
    const userId = res.locals?.user?.id || null

    return contactResource
      .summaryTicketById(id, isAdmin, userId)
      .then((response) => {
        res.json(response)
      })
      .catch((error) => {
        res.status(error.status || 500).json({
          error: error.message || 'An error occurred while summarizing the ticket',
        })
      })
  })
}
