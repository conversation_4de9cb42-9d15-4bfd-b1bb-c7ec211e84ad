import { Container, Inject, Service } from 'typedi'
import express, { Request, Response } from 'express'
import bodyParser from 'body-parser'
import http from 'http'
import { differenceBy } from 'lodash'
import HttpJobsRunner from '../../core/services/jobs/http/HttpJobsRunner'
import BootstrapApp from '../../core/services/app/BootstrapApp'
import ServerPod from './services/ServerPod'
import Config from '../../core/services/config/Config'
import httpErrorHandler from '../../core/middlewares/httpErrorHandler'
import { promiseHandler } from '../../core/utils/routing/resourceRouter'
import Logger from '../../core/services/logs/Logger'
import NotFoundHttpError from '../../core/utils/error/NotFoundHttpError'
import ServerPodRegister from './services/ServerPodRegister'
import BadRequestHttpError from '../../core/utils/error/BadRequestHttpError'
import queuedAsyncMap from '../../core/utils/array/queuedAsyncMap'
import reportError from '../../core/services/logs/reportError'
import taskQueue, { TaskQueue } from '../../core/services/queue/taskQueue'
import ConsulRepository from '../workers/jobs/serverPodManager/ConsulRepository'
import BrowserDataBackuper from './services/BrowserDataBackuper'
import configValues from './configValues'

type StopReasons = 'RESTART_AFTER_LOGOUT' | 'RESTART' | 'STOPPED_ON_SERVER' | 'PAGE_CLOSED'

@Service()
export default class App extends BootstrapApp {
  @Inject()
  protected httpJobsRunner: HttpJobsRunner

  @Inject()
  protected config: Config<typeof configValues>

  @Inject()
  protected logger: Logger

  @Inject()
  protected register: ServerPodRegister

  @Inject()
  protected browserDataBackuper: BrowserDataBackuper

  @Inject()
  protected consulRepository: ConsulRepository

  protected updateLocalStateQueue: TaskQueue = taskQueue('updateLocalStateFromApi', {
    concurrency: 1,
    timeout: 5 * 60 * 1000,
  })

  protected serverPodMap: Map<
    string,
    {
      securityToken: string
      serverPod: ServerPod
      lastConnectedAt?: Date
      status: 'starting' | 'running' | 'stopping'
      pageId?: string
    }
  > = new Map()

  async onStart() {
    const port = this.config.get('serverPodPort')

    const app = express()
    app.use(bodyParser.json({ limit: '100mb' }))
    app.get('/healthz', this.healthz)
    app.get('/pod/stats', this.statsHandler)
    app.post('/pod/backup', this.backupHandler)
    app.post('/pod/signal', this.signalHandler)
    app.post('/pod/:serviceId/rpc', this.rpcHandler)
    app.post('/pod/:serviceId/restart', this.restartHandler)
    app.use(httpErrorHandler)

    const server = http.createServer(app)

    // @ts-ignore
    await new Promise((resolve) => server.listen(port, resolve))

    this.logger.log(`Server running at port ${port}.`)

    // update local state from server
    await this.updateLocalStateFromApi(true).catch(reportError)

    // self update on consul every minute
    setInterval(async () => {
      await this.checkDisconnectedServices().catch(reportError)
      await this.updateLocalStateFromApi(false).catch(reportError)
    }, this.config.get('updateInterval'))
  }

  healthz = promiseHandler(async (req: Request, res: Response) => {
    return true
  })

  backupHandler = promiseHandler(async (req: Request, res: Response) => this.backupAllBrowserData())

  signalHandler = promiseHandler(async (req: Request, res: Response) => {
    this.updateLocalStateFromApi().catch(reportError)
    return true
  })

  statsHandler = promiseHandler(async (req: Request, res: Response) => {
    return this.getRegisterMeta()
  })

  rpcHandler = promiseHandler(async (req: Request, res: Response) => {
    req.setTimeout(10 * 60 * 1000)
    res.setTimeout(10 * 60 * 1000)

    const { serviceId } = req.params
    const { method, params, meta } = req.body

    const serverPodTuple = this.serverPodMap.get(serviceId)

    if (!serverPodTuple) throw new NotFoundHttpError(`ServiceId ${serviceId} not running here.`)

    const { serverPod } = serverPodTuple

    return serverPod.call(method, params, meta)
  })

  restartHandler = promiseHandler(async (req: Request, res: Response) => {
    const { serviceId } = req.params
    const { clean } = req.body

    this.logger.log(`[ServiceId: ${serviceId}] Restarting...`)

    const serverPodTuple = this.serverPodMap.get(serviceId)

    const reason = clean ? 'RESTART_AFTER_LOGOUT' : 'RESTART'

    if (serverPodTuple) {
      await this.stopServerPod(serviceId, reason)
    }

    await this.startServerPod(serviceId, serverPodTuple.securityToken, true)
  })

  protected async backupAllBrowserData() {
    this.logger.log('Browser data backup started...')

    const res = await Promise.all(
      [...this.serverPodMap.entries()].map(async ([serviceId]) => {
        this.logger.log(`Backuping "${serviceId}"...`)
        return {
          id: serviceId,
          backuped: await this.browserDataBackuper.backup(serviceId, false),
        }
      }),
    )

    this.logger.log('Browser data backup finished.')

    return res
  }

  protected async startServerPod(serviceId: string, securityToken: string, skipBackupRestore = false) {
    this.logger.log(`[ServiceId: ${serviceId}] Starting service.`)

    if (this.serverPodMap.get(serviceId)) {
      // await this.stopServerPod(serviceId)
      throw new BadRequestHttpError(`Service with id "${serviceId}" already started.`)
    }

    const serverPod = Container.get(ServerPod)

    this.serverPodMap.set(serviceId, {
      securityToken,
      serverPod,
      status: 'starting',
    })

    const apiUrl = `${this.config.get('internalApiUrl')}/whatsapp-server-pod-webhook`

    const serverPodUrl = this.config.get('serverPodUrl')
    const defaultUA = this.config.get('defaultUA')

    serverPod.setup({
      apiUrl,
      serviceId,
      securityToken,
      serverPodUrl,
      defaultUA,
    })

    try {
      if (!skipBackupRestore) {
        this.logger.log(`[ServiceId: ${serviceId}] Restoring browser data from s3...`)

        const hasRestored = await this.browserDataBackuper.restore(serviceId, true).catch(async (e) => {
          this.logger.log(e, 'error')
          this.logger.log(`[ServiceId: ${serviceId}] Browser data restore failed with error: ${e}`, 'error')
          await serverPod.logout('BACKUP_RESTORE_FAILED')
          return false
        })

        this.logger.log(`[ServiceId: ${serviceId}] Browser data has been restored from s3?: ${hasRestored}`)
      } else {
        this.logger.log(`[ServiceId: ${serviceId}] Skipping browser data restore (skipBackupRestore=true).`)
      }

      await serverPod.connect()
      this.serverPodMap.get(serviceId).status = 'running'
      this.serverPodMap.get(serviceId).pageId = serverPod.getPageId()
    } catch (e) {
      this.logger.log(`[ServiceId: ${serviceId}] Start service failed. %o`, 'error', [e])
      this.serverPodMap.delete(serviceId)
      return
    }

    this.logger.log(`[ServiceId: ${serviceId}] Started service.`)
  }

  protected async stopServerPod(serviceId: string, reason: StopReasons) {
    this.logger.log(`[ServiceId: ${serviceId}] Stopping service. Reason: ${reason}`)

    const serverPodTuple = this.serverPodMap.get(serviceId)

    if (!serverPodTuple) throw new NotFoundHttpError(`ServiceId ${serviceId} not running here.`)

    const { serverPod } = serverPodTuple

    this.serverPodMap.get(serviceId).status = 'stopping'

    await serverPod.disconnect()

    if (reason === 'RESTART_AFTER_LOGOUT') {
      this.logger.log(`[ServiceId: ${serviceId}] Deleting browser data because of logout...`)
      await this.browserDataBackuper.deleteLocal(serviceId)
    }

    if (reason === 'STOPPED_ON_SERVER') {
      this.logger.log(`[ServiceId: ${serviceId}] Backuping browser data to s3...`)
      const hasBackuped = await this.browserDataBackuper.backup(serviceId, true)
      this.logger.log(`[ServiceId: ${serviceId}] Browser data has been backuper to s3?: ${hasBackuped}`)
    }

    this.serverPodMap.delete(serviceId)

    this.logger.log(`[ServiceId: ${serviceId}] Stopped service.`)
  }

  protected async checkDisconnectedServices() {
    // stop closed services
    await queuedAsyncMap(
      [...this.serverPodMap.entries()],
      async ([serviceId, { serverPod, status, securityToken }]) => {
        // Page closed
        if (status === 'running' && !serverPod.pageIsOpen()) {
          this.logger.log(`[ServiceId: ${serviceId}] Service page is closed, stopping its pod.`)
          await this.stopServerPod(serviceId, 'PAGE_CLOSED')
          await this.startServerPod(serviceId, securityToken, true)
        }
      },
      1,
    )
  }

  protected async updateLocalStateFromApi(isFirstStart = false) {
    await this.updateLocalStateQueue.run(async () => {
      const state = await this.register.update(this.getRegisterMeta())

      if (Boolean(this.config.get('browserLogsEnabled')) !== Boolean(state.settings?.browserLogsEnabled)) {
        this.logger.log(`Setting changed: browserLogsEnabled=${state.settings?.browserLogsEnabled}`)
        this.config.set('browserLogsEnabled', state.settings?.browserLogsEnabled)
      }

      const localServiceIdTuples = [...(this.serverPodMap?.entries() || [])].map(([serviceId, { securityToken }]) => ({
        serviceId,
        securityToken,
      }))
      const serverServiceIdTuples = (state.serviceIds || []).map((s) => {
        const [serviceId, securityToken] = s.split(':')
        return {
          serviceId,
          securityToken,
        }
      })

      const toStart = differenceBy(serverServiceIdTuples, localServiceIdTuples, 'serviceId')
      const toStop = differenceBy(localServiceIdTuples, serverServiceIdTuples, 'serviceId')

      if (toStart.length) {
        this.logger.log(`Starting: ${toStart.map((s) => s.serviceId)}.`)
      }

      if (toStop.length) {
        this.logger.log(`Stopping: ${toStop.map((s) => s.serviceId)}.`)
      }

      await queuedAsyncMap(toStop, (s) => this.stopServerPod(s.serviceId, 'STOPPED_ON_SERVER'))
      await queuedAsyncMap(toStart, (s) => this.startServerPod(s.serviceId, s.securityToken, isFirstStart))
    })
  }

  protected getRegisterMeta() {
    return {
      id: this.config.get('instanceId'),
      url: this.config.get('serverPodUrl'),
      usedPods: this.serverPodMap.size,
      pageIds: [...this.serverPodMap.entries()].map(([serviceId, { pageId }]) => [serviceId, pageId]),
      serviceIds: [...this.serverPodMap.entries()].map(
        ([serviceId, { securityToken }]) => `${serviceId}:${securityToken}`,
      ),
      version: this.config.get('version'),
      cloud: this.config.get('cloud'),
      type: this.config.get('type'),
    }
  }

  onDeath(): Promise<void> {
    return Promise.resolve()
  }
}
