# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [3.60.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.60.0-mr-3630.2...v3.60.1) (2025-09-16)

**Note:** Version bump only for package @digisac/back





# [3.60.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.60.0-rc.10...v3.60.0) (2025-09-15)



# [3.59.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.60.0-mr-3453.15...v3.59.0) (2025-09-09)

**Note:** Version bump only for package @digisac/back





# [3.59.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.59.0-rc.29...v3.59.0) (2025-09-09)

**Note:** Version bump only for package @digisac/back





## [3.58.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.58.1-mr-3562.5...v3.58.2) (2025-09-08)

**Note:** Version bump only for package @digisac/back





## [3.58.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.58.1-mr-3594.1...v3.58.1) (2025-09-04)

**Note:** Version bump only for package @digisac/back





# [3.58.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.58.0-rc.21...v3.58.0) (2025-09-02)

**Note:** Version bump only for package @digisac/back





## [3.57.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.57.2-mr-3500.5...v3.57.3) (2025-09-02)

**Note:** Version bump only for package @digisac/back





## [3.57.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.57.1-mr-3541.2...v3.57.2) (2025-09-01)

**Note:** Version bump only for package @digisac/back





## [3.57.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.57.0-mr-3560.1...v3.57.1) (2025-08-27)

**Note:** Version bump only for package @digisac/back





# [3.57.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.57.0-rc.15...v3.57.0) (2025-08-26)

**Note:** Version bump only for package @digisac/back





## [3.56.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.56.1-mr-3539.0...v3.56.1) (2025-08-21)

**Note:** Version bump only for package @digisac/back





# [3.56.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.56.0-rc.20...v3.56.0) (2025-08-18)

**Note:** Version bump only for package @digisac/back





# [3.55.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.3...v3.55.0) (2025-08-11)



# [3.55.0-rc.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3438.4...v3.55.0-rc.15) (2025-08-08)



# [3.55.0-mr-3438.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3438.3...v3.55.0-mr-3438.4) (2025-08-08)



# [3.55.0-mr-3438.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3438.2...v3.55.0-mr-3438.3) (2025-08-08)


### Bug Fixes

* update notification message for insufficient credits ([f9a1382](https://gitlab.ikatec.cloud/digisac/digisac/commit/f9a13822144a20fc2f9ad5cc93176b1ff33c5511))



# [3.55.0-mr-3438.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.14...v3.55.0-mr-3438.2) (2025-08-08)


### Bug Fixes

* missing translation token ([492e73d](https://gitlab.ikatec.cloud/digisac/digisac/commit/492e73d5a70667f394527c907f6b027153ecf7f3))



# [3.55.0-rc.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3438.1...v3.55.0-rc.14) (2025-08-07)



# [3.55.0-mr-3438.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.2...v3.55.0-mr-3438.1) (2025-08-07)



# [3.55.0-mr-3483.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1-mr-3466.3...v3.55.0-mr-3483.1) (2025-08-07)



# [3.55.0-mr-3438.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.14...v3.55.0-mr-3438.0) (2025-08-07)



# [3.55.0-mr-3427.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3483.0...v3.55.0-mr-3427.14) (2025-08-07)



# [3.55.0-mr-3483.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.13...v3.55.0-mr-3483.0) (2025-08-06)


### Bug Fixes

* **tests:** update mocks for agent credits services ([b25ef06](https://gitlab.ikatec.cloud/digisac/digisac/commit/b25ef068155fe94033f83a7239b16f8d58febcfd))



# [3.55.0-mr-3427.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.13...v3.55.0-mr-3427.13) (2025-08-06)



# [3.55.0-rc.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.12...v3.55.0-rc.13) (2025-08-06)


### Features

* block agent credit limits ([200ac5b](https://gitlab.ikatec.cloud/digisac/digisac/commit/200ac5bd057026889f2528c07334fe8405028971))



# [3.55.0-rc.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.11...v3.55.0-rc.12) (2025-08-06)



# [3.55.0-mr-3427.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.11...v3.55.0-mr-3427.11) (2025-08-06)



# [3.55.0-rc.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.10...v3.55.0-rc.11) (2025-08-06)


### Bug Fixes

* lint at pipelineResource.test ([b5cdacb](https://gitlab.ikatec.cloud/digisac/digisac/commit/b5cdacb63b1cd3d64ce1412c9d8a55d13247ba87))



# [3.55.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3431.3...v3.55.0-rc.10) (2025-08-05)



# [3.55.0-mr-3431.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1...v3.55.0-mr-3431.3) (2025-08-05)



# [3.55.0-rc.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3468.2...v3.55.0-rc.9) (2025-08-05)



# [3.55.0-mr-3468.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.8...v3.55.0-mr-3468.2) (2025-08-05)



# [3.55.0-rc.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3459.3...v3.55.0-rc.8) (2025-08-05)



# [3.55.0-mr-3459.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3431.2...v3.55.0-mr-3459.3) (2025-08-05)



# [3.55.0-mr-3431.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3459.2...v3.55.0-mr-3431.1) (2025-08-05)



# [3.55.0-mr-3459.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3427.10...v3.55.0-mr-3459.2) (2025-08-05)



# [3.55.0-mr-3427.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1-mr-3475.0...v3.55.0-mr-3427.10) (2025-08-05)



# [3.55.0-mr-3427.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3427.8...v3.55.0-mr-3427.9) (2025-08-05)



# [3.55.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0...v3.55.0-rc.7) (2025-08-05)


### Bug Fixes

* **version:** versao alterada para 3.55.0-rc.6 ([8866639](https://gitlab.ikatec.cloud/digisac/digisac/commit/88666390da0983d6ae39bf74e7e94ff7c8614f7f))



## [3.55.5-mr-3439.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.5-mr-3439.1...v3.55.5-mr-3439.2) (2025-08-04)



## [3.55.5-mr-3439.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.6...v3.55.5-mr-3439.1) (2025-08-04)



# [3.55.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.1-mr-3443.3...v3.55.0-rc.6) (2025-08-04)


### Bug Fixes

* **version:** versao alterada para 3.55.0-rc.5 ([26051bd](https://gitlab.ikatec.cloud/digisac/digisac/commit/26051bd370f523841211935c713cb01fe48f6ae1))



## [3.55.1-mr-3443.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.4-mr-3466.1...v3.55.1-mr-3443.3) (2025-08-04)



# [3.54.0-mr-3427.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.5...v3.54.0-mr-3427.7) (2025-07-31)


### Bug Fixes

* **ai-agent-dashboard:** corrige apontamentos no front ([ee889cd](https://gitlab.ikatec.cloud/digisac/digisac/commit/ee889cd05a825ec495100e580f389cb516402136))



# [3.55.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-rc.16...v3.55.0-rc.5) (2025-07-31)



# [3.55.0-mr-3465.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.4...v3.55.0-mr-3465.0) (2025-07-31)



# [3.55.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3431.0...v3.55.0-rc.4) (2025-07-31)



# [3.55.0-mr-3431.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3468.1...v3.55.0-mr-3431.0) (2025-07-31)



# [3.55.0-mr-3468.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3467.0...v3.55.0-mr-3468.0) (2025-07-31)



# [3.55.0-mr-3458.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3458.1...v3.55.0-mr-3458.0) (2025-07-31)



# [3.54.0-mr-3458.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.4-mr-3466.0...v3.54.0-mr-3458.1) (2025-07-31)


### Features

* increasing code coverage from 60% to 87% ([ed234b8](https://gitlab.ikatec.cloud/digisac/digisac/commit/ed234b802a1d739232df48d88f98f1e73a2a8e92))
* novos testes unitarios para a classe cardResource ([35f87ab](https://gitlab.ikatec.cloud/digisac/digisac/commit/35f87ab821f1aaac0ee33a5a184f9f162c095ccc))



## [3.53.1-mr-3443.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3427.6...v3.53.1-mr-3443.1) (2025-07-31)



# [3.54.0-mr-3427.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3459.1...v3.54.0-mr-3427.5) (2025-07-30)


### Features

* **ai-agent-dashboard:** corrige testes unitários da sugestão de prompt ([aa3ce37](https://gitlab.ikatec.cloud/digisac/digisac/commit/aa3ce37f168813451cba1db5fdb68170f91a971d))



# [3.55.0-mr-3459.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3459.0...v3.55.0-mr-3459.1) (2025-07-30)



# [3.55.0-mr-3459.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.3...v3.55.0-mr-3459.0) (2025-07-30)



# [3.55.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3358.5...v3.55.0-rc.3) (2025-07-30)



# [3.55.0-mr-3358.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.2...v3.55.0-mr-3358.5) (2025-07-30)



# [3.55.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3458.0...v3.55.0-rc.2) (2025-07-30)


### Features

* improve code coverage from 40% to 63% ([6a2b789](https://gitlab.ikatec.cloud/digisac/digisac/commit/6a2b789304e6f348f50259b170dd5b221b1788e2))



# [3.54.0-mr-3458.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-mr-3331.10...v3.54.0-mr-3458.0) (2025-07-30)



# [3.55.0-mr-3331.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3431.9...v3.55.0-mr-3331.10) (2025-07-30)


### Features

* adiciona testes unitarios a classe CardResource ([4b2b7dc](https://gitlab.ikatec.cloud/digisac/digisac/commit/4b2b7dcf834eef6dd844891096207755cf906f81))



# [3.54.0-mr-3431.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.55.0-rc.1...v3.54.0-mr-3431.9) (2025-07-30)



# [3.55.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-rc.13...v3.55.0-rc.1) (2025-07-30)


### Bug Fixes

* **version:** versao alterada para 3.55.0-rc.0 ([f8f7a21](https://gitlab.ikatec.cloud/digisac/digisac/commit/f8f7a21798b44e4d89acea190c5930de6892f3b2))


### Features

* **ai-agent-dashboard:** implementa consumo de ia para o agente inteligente no backend ([70065eb](https://gitlab.ikatec.cloud/digisac/digisac/commit/70065ebc83ee5723dc4d77bf2d0f74bce5c1a8c3))



## [3.53.2-mr-3442.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3431.8...v3.53.2-mr-3442.0) (2025-07-29)



# [3.54.0-mr-3431.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.1...v3.54.0-mr-3431.8) (2025-07-29)


### Features

* testes unitarios cardResource ([767b158](https://gitlab.ikatec.cloud/digisac/digisac/commit/767b158d9751f50d0d7b3705d1ce157adca557ab))



# [3.54.0-mr-3427.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.1-mr-3452.0...v3.54.0-mr-3427.3) (2025-07-28)



# [3.54.0-mr-3427.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0...v3.54.0-mr-3427.2) (2025-07-28)


### Features

* remoção do console.log() ([881ec6e](https://gitlab.ikatec.cloud/digisac/digisac/commit/881ec6e7297e30e6108ce19892836682e52ccb10))



# [3.54.0-mr-3431.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.14...v3.54.0-mr-3431.6) (2025-07-28)


### Features

* corige retornos do back para o front ([d1e3eeb](https://gitlab.ikatec.cloud/digisac/digisac/commit/d1e3eeb598f432b535769d3adc4c3c3892a97420))



# [3.54.0-mr-3431.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.13...v3.54.0-mr-3431.5) (2025-07-28)


### Features

* validação copiloto ([014dc72](https://gitlab.ikatec.cloud/digisac/digisac/commit/014dc72b7423c523a1d501b3cccc151e56ec0715))



# [3.54.0-mr-3431.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-rc.11...v3.54.0-mr-3431.4) (2025-07-28)



# [3.54.0-mr-3431.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3444.2...v3.54.0-mr-3431.3) (2025-07-26)



# [3.54.0-mr-3444.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3431.2...v3.54.0-mr-3444.2) (2025-07-26)



# [3.54.0-mr-3431.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.12...v3.54.0-mr-3431.2) (2025-07-25)



# [3.54.0-mr-3444.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.5-mr-3443.0...v3.54.0-mr-3444.0) (2025-07-25)



## [3.51.5-mr-3443.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-mr-3424.1...v3.51.5-mr-3443.0) (2025-07-25)


### Bug Fixes

* SD-1491 fixing contacts query ([d453083](https://gitlab.ikatec.cloud/digisac/digisac/commit/d4530830e86ba2c7381f1618c97d62061f2d1f31))


### Features

* atualização do axios ([304c077](https://gitlab.ikatec.cloud/digisac/digisac/commit/304c077c0a8b2a77693bc589d92b4eb5e98591f2))



# [3.53.0-mr-3427.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.5-mr-3439.0...v3.53.0-mr-3427.1) (2025-07-24)



## [3.51.5-mr-3439.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.11...v3.51.5-mr-3439.0) (2025-07-24)


### Bug Fixes

* hsm countdown reinicia com mensagens recebidas tunel ([60efa15](https://gitlab.ikatec.cloud/digisac/digisac/commit/60efa15ab12ab7600fc30d48e9f18469ce54b139))


### Features

* adiciona bloqueio no copilot para msg unica de audio caso não tenha transcrição de audio habilitada ([e13135a](https://gitlab.ikatec.cloud/digisac/digisac/commit/e13135aaa616f7209faa92a8d0d7f54c831405a8))



# [3.53.0-mr-3427.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.5-mr-3432.0...v3.53.0-mr-3427.0) (2025-07-23)



# [3.52.0-mr-3358.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3401.3...v3.52.0-mr-3358.4) (2025-07-16)



# [3.52.0-mr-3358.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3379.3...v3.52.0-mr-3358.3) (2025-07-16)


### Bug Fixes

* correct translation for 'ACTIVE_TICKET_TOPICS' in Portuguese locale ([0ac42c7](https://gitlab.ikatec.cloud/digisac/digisac/commit/0ac42c7d741b9021a853d50302a2ea5b36b565bf))



# [3.52.0-mr-3331.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3284.2...v3.52.0-mr-3331.9) (2025-07-16)



# [3.48.0-mr-3358.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3385.2...v3.48.0-mr-3358.2) (2025-07-16)



# [3.48.0-mr-3358.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3369.0...v3.48.0-mr-3358.1) (2025-07-14)



# [3.48.0-mr-3331.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-mr-3228.8...v3.48.0-mr-3331.7) (2025-07-14)



# [3.48.0-mr-3331.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3288.13...v3.48.0-mr-3331.6) (2025-07-02)



# [3.48.0-mr-3358.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3284.17...v3.48.0-mr-3358.0) (2025-07-02)



# [3.48.0-mr-3331.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.15...v3.48.0-mr-3331.5) (2025-07-02)


### Features

* **ticket-topics:** implement CRUD functionality for ticket topics ([afc51bf](https://gitlab.ikatec.cloud/digisac/digisac/commit/afc51bff21041dca975ef09894db086c68bae7d1))



# [3.48.0-mr-3331.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.19...v3.48.0-mr-3331.4) (2025-06-29)





## [3.54.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.3-mr-3496.2...v3.54.3) (2025-08-11)

**Note:** Version bump only for package @digisac/back





## [3.54.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1-mr-3466.3...v3.54.2) (2025-08-07)

**Note:** Version bump only for package @digisac/back





## [3.54.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.1-mr-3475.0...v3.54.1) (2025-08-05)

**Note:** Version bump only for package @digisac/back





# [3.54.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.54.0-rc.16...v3.54.0) (2025-08-04)

**Note:** Version bump only for package @digisac/back





## [3.53.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.1-mr-3452.0...v3.53.1) (2025-07-29)

**Note:** Version bump only for package @digisac/back





# [3.53.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.53.0-rc.14...v3.53.0) (2025-07-28)


### Bug Fixes

* importação global da pasta core ([ef65f5a](https://gitlab.ikatec.cloud/digisac/digisac/commit/ef65f5a1b3a97ba3a2cd57433a86a9efe1e51062))





# [3.52.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.52.0-rc.20...v3.52.0) (2025-07-28)

**Note:** Version bump only for package @digisac/back





## [3.51.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.4-mr-3105.30...v3.51.5) (2025-07-28)

**Note:** Version bump only for package @digisac/back





## [3.51.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.4-mr-3417.0...v3.51.4) (2025-07-17)

**Note:** Version bump only for package @digisac/back





## [3.51.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.2...v3.51.3) (2025-07-16)



## [3.51.1-mr-3405.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.0...v3.51.1-mr-3405.0) (2025-07-15)


### Bug Fixes

* **whatsapp:** atualização waVersion para 2.3000.1024760756 ([fb866e0](https://gitlab.ikatec.cloud/digisac/digisac/commit/fb866e0204cf69d6c595d1ad771c39bfd0565ce7))





## [3.51.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.2-mr-3408.0...v3.51.2) (2025-07-16)

**Note:** Version bump only for package @digisac/back





# [3.51.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.51.0-rc.10...v3.51.0) (2025-07-14)

**Note:** Version bump only for package @digisac/back





## [3.50.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.2-mr-3360.3...v3.50.3) (2025-07-14)

**Note:** Version bump only for package @digisac/back





## [3.50.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.2-mr-3381.1...v3.50.2) (2025-07-08)

**Note:** Version bump only for package @digisac/back





## [3.50.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.20...v3.50.1) (2025-07-07)

**Note:** Version bump only for package @digisac/back





# [3.50.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.2...v3.50.0) (2025-07-07)



# [3.50.0-rc.18](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1...v3.50.0-rc.18) (2025-07-07)



# [3.50.0-mr-3376.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1-mr-3368.0...v3.50.0-mr-3376.0) (2025-07-07)



# [3.50.0-rc.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3364.1...v3.50.0-rc.17) (2025-07-03)



# [3.50.0-mr-3364.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.16...v3.50.0-mr-3364.1) (2025-07-03)



# [3.50.0-rc.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3365.0...v3.50.0-rc.16) (2025-07-03)



# [3.50.0-mr-3365.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3364.0...v3.50.0-mr-3365.0) (2025-07-03)


### Bug Fixes

* **bot-interpolation-undefined:** corrige interpolação para funcionar corretamente com variaveis novas e antigas ([d0ea226](https://gitlab.ikatec.cloud/digisac/digisac/commit/d0ea226454832c9168f67dc93f200cfe204df27c))



# [3.50.0-rc.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3356.2...v3.50.0-rc.15) (2025-07-02)



# [3.50.0-mr-3356.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.14...v3.50.0-mr-3356.1) (2025-07-01)



# [3.50.0-rc.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.20...v3.50.0-rc.14) (2025-06-30)



# [3.49.0-rc.20](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0...v3.49.0-rc.20) (2025-06-30)



# [3.50.0-rc.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3311.1...v3.50.0-rc.13) (2025-06-30)



# [3.50.0-mr-3311.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.12...v3.50.0-mr-3311.1) (2025-06-30)



# [3.50.0-mr-3356.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3355.1...v3.50.0-mr-3356.0) (2025-06-30)



# [3.50.0-mr-3355.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3356.0...v3.50.0-mr-3355.1) (2025-06-30)



# [3.50.0-mr-3355.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.11...v3.50.0-mr-3355.0) (2025-06-30)


### Bug Fixes

* **bot-simulator-interpolation-error:** corrige função de interpolação de variaveis no simulador do bot ([8bb6898](https://gitlab.ikatec.cloud/digisac/digisac/commit/8bb6898d84594f6e1c75da1b260bf7feeb62fa50))



# [3.50.0-rc.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3311.0...v3.50.0-rc.11) (2025-06-30)



# [3.50.0-mr-3311.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3311.7...v3.50.0-mr-3311.0) (2025-06-30)



# [3.49.0-mr-3311.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.4...v3.49.0-mr-3311.7) (2025-06-30)



# [3.50.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3343.8...v3.50.0-rc.10) (2025-06-30)



# [3.49.0-mr-3311.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.9...v3.49.0-mr-3311.6) (2025-06-27)


### Bug Fixes

* corrigindo exportacao de relatorio do funil ([5a243d3](https://gitlab.ikatec.cloud/digisac/digisac/commit/5a243d3c08b96766200cd7dd528fedf3e4d290d4))



# [3.50.0-mr-3343.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3334.1...v3.50.0-mr-3343.7) (2025-06-27)



# [3.50.0-mr-3334.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.7...v3.50.0-mr-3334.1) (2025-06-27)



# [3.50.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3318.3...v3.50.0-rc.7) (2025-06-27)



# [3.50.0-mr-3318.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.3-mr-3349.0...v3.50.0-mr-3318.3) (2025-06-27)



# [3.50.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.17...v3.50.0-rc.6) (2025-06-26)



# [3.50.0-mr-3343.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.2...v3.50.0-mr-3343.5) (2025-06-26)



# [3.50.0-mr-3343.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3343.2...v3.50.0-mr-3343.3) (2025-06-26)



# [3.50.0-mr-3343.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3343.1...v3.50.0-mr-3343.2) (2025-06-26)


### Bug Fixes

* alter fix startPeriod hour to 00:00:00 on page filter ([ea6b5b6](https://gitlab.ikatec.cloud/digisac/digisac/commit/ea6b5b6f715a7fe8f8711bb694676ed75a52f87c))



# [3.50.0-mr-3343.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1-mr-3305.3...v3.50.0-mr-3343.1) (2025-06-26)


### Bug Fixes

* fix evaluation metrics filter by day period ([466afe6](https://gitlab.ikatec.cloud/digisac/digisac/commit/466afe67e35fe813c4147d4f7b5babb4c9315659))



# [3.50.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.15...v3.50.0-rc.4) (2025-06-26)



# [3.50.0-mr-3318.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3318.1...v3.50.0-mr-3318.2) (2025-06-25)



# [3.49.0-mr-3318.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3334.0...v3.49.0-mr-3318.1) (2025-06-25)



# [3.50.0-mr-3334.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.3...v3.50.0-mr-3334.0) (2025-06-25)



# [3.50.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-rc.2...v3.50.0-rc.3) (2025-06-25)



# [3.50.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.50.0-mr-3336.0...v3.50.0-rc.2) (2025-06-25)



# [3.50.0-mr-3336.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.14...v3.50.0-mr-3336.0) (2025-06-25)


### Bug Fixes

* ensure comment messages with files emit proper update events ([3fa8cdb](https://gitlab.ikatec.cloud/digisac/digisac/commit/3fa8cdbaaa3f2da64c45f68a8071c92adb438570))



# [3.50.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.12...v3.50.0-rc.1) (2025-06-25)


### Bug Fixes

* **version:** versao alterada para 3.50.0-rc.0 ([66b9617](https://gitlab.ikatec.cloud/digisac/digisac/commit/66b9617e44e3b2c4a46451fc738eddf149be03b4))



# [3.49.0-mr-3311.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.8...v3.49.0-mr-3311.2) (2025-06-24)



# [3.49.0-mr-3311.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3327.1...v3.49.0-mr-3311.1) (2025-06-24)



# [3.49.0-mr-3327.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3311.0...v3.49.0-mr-3327.1) (2025-06-24)



# [3.49.0-mr-3311.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.7...v3.49.0-mr-3311.0) (2025-06-24)



# [3.49.0-mr-3318.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.6...v3.49.0-mr-3318.0) (2025-06-23)



# [3.49.0-mr-3327.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3327.0...v3.49.0-mr-3327.0) (2025-06-23)


### Features

* implementando filtro por data de ganho ([bd39415](https://gitlab.ikatec.cloud/digisac/digisac/commit/bd39415cfe91a1c093c1b7abbb0abfc0f8ea8bf6))



# [3.48.0-mr-3327.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.17...v3.48.0-mr-3327.0) (2025-06-23)



# [3.49.0-mr-3300.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.3...v3.49.0-mr-3300.8) (2025-06-21)


### Features

* **BaseBotService:** implementa uso de alias para variáveis de interpolação ([8866f40](https://gitlab.ikatec.cloud/digisac/digisac/commit/8866f40203d80e53922ba5a0726d83aa0d989929))



# [3.49.0-mr-3300.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3300.6...v3.49.0-mr-3300.7) (2025-06-20)


### Features

* **SDIG-657-improved-interpolation-of-variables:** corrige label com  informações textuais e ajusta função que retorna o nome do contato ([2cef054](https://gitlab.ikatec.cloud/digisac/digisac/commit/2cef0549631e3d49589a215665e3be59bfc3a978))



# [3.49.0-mr-3300.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3326.0...v3.49.0-mr-3300.6) (2025-06-20)



# [3.49.0-mr-3326.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3325.0...v3.49.0-mr-3326.0) (2025-06-20)



# [3.48.0-mr-3311.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3311.0...v3.48.0-mr-3311.1) (2025-06-19)



# [3.48.0-mr-3311.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3317.0...v3.48.0-mr-3311.0) (2025-06-19)



# [3.47.0-mr-3300.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.12...v3.47.0-mr-3300.5) (2025-06-17)


### Features

* **SDIG-657-improved-interpolation-of-variables:** adiciona novas variáveis de interpolação no bot e ajusta limite de itens no autocomplete ([b7a2b0a](https://gitlab.ikatec.cloud/digisac/digisac/commit/b7a2b0a06b2283a7bc8db393289e3e90d907ef40))





## [3.49.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1-mr-3383.0...v3.49.2) (2025-07-07)



## [3.49.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1-mr-3368.0...v3.49.1) (2025-07-07)

**Note:** Version bump only for package @digisac/back





## [3.49.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.1-mr-3368.0...v3.49.1) (2025-07-07)

**Note:** Version bump only for package @digisac/back





# [3.49.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.3...v3.49.0) (2025-06-30)



# [3.49.0-mr-3337.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.19...v3.49.0-mr-3337.4) (2025-06-30)



# [3.49.0-rc.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.2...v3.49.0-rc.17) (2025-06-26)



# [3.49.0-rc.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.3...v3.49.0-rc.16) (2025-06-26)



# [3.49.0-mr-3337.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.2...v3.49.0-mr-3337.3) (2025-06-26)



# [3.49.0-mr-3337.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3344.0...v3.49.0-mr-3337.2) (2025-06-26)



# [3.49.0-mr-3337.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.15...v3.49.0-mr-3337.1) (2025-06-26)



# [3.49.0-rc.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.6...v3.49.0-rc.15) (2025-06-26)



# [3.49.0-mr-3317.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.14...v3.49.0-mr-3317.6) (2025-06-26)



# [3.49.0-rc.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.13...v3.49.0-rc.14) (2025-06-25)



# [3.49.0-rc.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1...v3.49.0-rc.13) (2025-06-25)



# [3.49.0-rc.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3304.0...v3.49.0-rc.12) (2025-06-25)



# [3.49.0-mr-3304.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1-mr-3338.0...v3.49.0-mr-3304.0) (2025-06-25)



# [3.49.0-rc.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0...v3.49.0-rc.11) (2025-06-24)



# [3.49.0-mr-3134.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3134.2...v3.49.0-mr-3134.3) (2025-06-24)



# [3.49.0-mr-3134.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3337.0...v3.49.0-mr-3134.2) (2025-06-24)



# [3.49.0-mr-3337.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.10...v3.49.0-mr-3337.0) (2025-06-24)



# [3.49.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.9...v3.49.0-rc.10) (2025-06-24)



# [3.49.0-rc.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3330.1...v3.49.0-rc.9) (2025-06-24)



# [3.49.0-mr-3330.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.8...v3.49.0-mr-3330.1) (2025-06-24)


### Bug Fixes

* adjust job import in workers ([766b47b](https://gitlab.ikatec.cloud/digisac/digisac/commit/766b47b67daea86aca8b6417cbb03dbe582488a4))



# [3.49.0-mr-3330.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.5...v3.49.0-mr-3330.0) (2025-06-23)



# [3.49.0-mr-3317.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3329.0...v3.49.0-mr-3317.5) (2025-06-23)


### Bug Fixes

* **TicketInactiveJob:** corrige casos em que a última mensagem do chamado é um comentário ([58afd70](https://gitlab.ikatec.cloud/digisac/digisac/commit/58afd70fb935caf6e79be35de5bb02f5914ca6ca))



# [3.49.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3325.1...v3.49.0-rc.6) (2025-06-23)



# [3.49.0-mr-3325.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.5...v3.49.0-mr-3325.1) (2025-06-23)



# [3.49.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3310.1...v3.49.0-rc.5) (2025-06-23)



# [3.49.0-mr-3310.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.4...v3.49.0-mr-3310.1) (2025-06-23)



# [3.49.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.17...v3.49.0-rc.4) (2025-06-23)



# [3.49.0-mr-3317.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.3...v3.49.0-mr-3317.4) (2025-06-23)


### Features

* **TicketInactiveJob:** implementa mais melhorias sugeridas em CR ([839f448](https://gitlab.ikatec.cloud/digisac/digisac/commit/839f44880c50bc1bc24073077c1c1b39b37a62aa))
* **TicketInactiveJob:** implementa melhorias sugeridas em CR ([a5216d6](https://gitlab.ikatec.cloud/digisac/digisac/commit/a5216d6fcda313a801d2012fbe7152d9ea2c12ea))



# [3.49.0-mr-3317.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.2...v3.49.0-mr-3317.3) (2025-06-21)


### Bug Fixes

* **TicketInactiveJob:** correção na indentação ([4697e74](https://gitlab.ikatec.cloud/digisac/digisac/commit/4697e74932e8219001176d89f199c5753368fb31))
* **TicketInactiveJob:** utilizar contactResource da classe BaseBotService ([17e1842](https://gitlab.ikatec.cloud/digisac/digisac/commit/17e184298c3ec4567dd91df42f52f8e045af31b6))



# [3.49.0-mr-3317.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3317.1...v3.49.0-mr-3317.2) (2025-06-20)



# [3.49.0-mr-3317.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3325.0...v3.49.0-mr-3317.1) (2025-06-20)



# [3.49.0-mr-3325.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.3...v3.49.0-mr-3325.0) (2025-06-20)


### Features

* adiciona endpoint no super admin para que o agnus possa habilitar o funil de vendas ([bfd29c2](https://gitlab.ikatec.cloud/digisac/digisac/commit/bfd29c2bb7a42980d4541c8c907af36b78824f73))



# [3.49.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3297.0...v3.49.0-rc.3) (2025-06-20)



# [3.49.0-mr-3297.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3310.0...v3.49.0-mr-3297.0) (2025-06-20)



# [3.49.0-mr-3310.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3297.3...v3.49.0-mr-3310.0) (2025-06-20)



# [3.48.0-mr-3297.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3310.4...v3.48.0-mr-3297.3) (2025-06-20)



# [3.48.0-mr-3317.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-rc.2...v3.48.0-mr-3317.0) (2025-06-19)


### Features

* **TicketInactiveJob:** implementa melhorias relacionadas ao lock ([a6a8110](https://gitlab.ikatec.cloud/digisac/digisac/commit/a6a81101f419308232b2528f537b3e3d4c6566e7))



# [3.49.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.49.0-mr-3229.7...v3.49.0-rc.2) (2025-06-18)



# [3.49.0-mr-3229.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3229.7...v3.49.0-mr-3229.7) (2025-06-18)


### Bug Fixes

* adding CSAT to plan history. ([b0a9505](https://gitlab.ikatec.cloud/digisac/digisac/commit/b0a9505728b77f21b64321180edd3af2d2886690))
* removing negative number. ([3a37b04](https://gitlab.ikatec.cloud/digisac/digisac/commit/3a37b0489d8344b4f673efc95ace77c43fdfe44e))



# [3.49.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.16...v3.49.0-rc.1) (2025-06-18)


### Bug Fixes

* **version:** versao alterada para 3.49.0-rc.0 ([20f9df2](https://gitlab.ikatec.cloud/digisac/digisac/commit/20f9df2692dfd8575b1d686b22b074e43282ab18))



# [3.48.0-mr-3297.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3297.1...v3.48.0-mr-3297.2) (2025-06-18)



# [3.48.0-mr-3297.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.1-mr-3319.0...v3.48.0-mr-3297.1) (2025-06-18)


### Bug Fixes

* Fixing to fetch the account id provided by the url. ([a3247e0](https://gitlab.ikatec.cloud/digisac/digisac/commit/a3247e00a5d09b84c4813f0f4a4ffd78be5badc8))


### Features

* **TicketInactiveJob:** melhora vazão no processamento de tickets inativos ([6554acc](https://gitlab.ikatec.cloud/digisac/digisac/commit/6554acc8e424964a3738e276e2c9e096e5bc8399))



## [3.46.1-mr-3304.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.10...v3.46.1-mr-3304.0) (2025-06-16)


### Bug Fixes

* reordering imports ([3551bc6](https://gitlab.ikatec.cloud/digisac/digisac/commit/3551bc658f59ffc7c7bc8a8283a47abd02836817))



# [3.48.0-mr-3134.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.9...v3.48.0-mr-3134.1) (2025-06-16)



# [3.48.0-mr-3229.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.8...v3.48.0-mr-3229.6) (2025-06-16)



# [3.48.0-mr-3310.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3024.11...v3.48.0-mr-3310.3) (2025-06-16)



# [3.48.0-mr-3310.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0-rc.13...v3.48.0-mr-3310.2) (2025-06-13)



# [3.48.0-mr-3310.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3236.1...v3.48.0-mr-3310.0) (2025-06-12)


### Features

* redesign de mensagens de audio no copilot ([c40f7b6](https://gitlab.ikatec.cloud/digisac/digisac/commit/c40f7b6132525a819c41b6c9163ddb542ccc104d))



# [3.48.0-mr-3297.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-mr-3248.2...v3.48.0-mr-3297.0) (2025-06-12)


### Bug Fixes

* Correcting comment in MR. ([71ad875](https://gitlab.ikatec.cloud/digisac/digisac/commit/71ad87582d45bf68092bf5f5ced8b525203cba55))



# [3.48.0-mr-3229.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0...v3.48.0-mr-3229.5) (2025-06-11)


### Bug Fixes

* SD-1528 - Buffer audios before converting ([6519a6b](https://gitlab.ikatec.cloud/digisac/digisac/commit/6519a6b8ddad736917154ac8533555628770eb8d))



# [3.47.0-mr-3297.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0-mr-3297.3...v3.47.0-mr-3297.4) (2025-06-10)



# [3.47.0-mr-3297.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0-rc.11...v3.47.0-mr-3297.3) (2025-06-10)



# [3.43.0-mr-3134.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-mr-3024.4...v3.43.0-mr-3134.8) (2025-05-28)



# [3.44.0-mr-3024.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-mr-3024.2...v3.44.0-mr-3024.3) (2025-05-28)



# [3.44.0-mr-3024.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-mr-3024.0...v3.44.0-mr-3024.1) (2025-05-27)



# [3.44.0-mr-3024.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-mr-3234.0...v3.44.0-mr-3024.0) (2025-05-27)


### Bug Fixes

* update tag creation messages and clean up dialog components ([be4d293](https://gitlab.ikatec.cloud/digisac/digisac/commit/be4d2939ab750d31870c5f6839a1d10772ef2c26))



# [3.43.0-mr-3227.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-rc.5...v3.43.0-mr-3227.0) (2025-05-26)


### Features

* create a cronjob to detect how many tokens needs a expiration notification ([c2988ba](https://gitlab.ikatec.cloud/digisac/digisac/commit/c2988ba6170f7694541650f4d4429b4900f3845a))



# [3.43.0-mr-3201.26](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.2-mr-3222.0...v3.43.0-mr-3201.26) (2025-05-22)



# [3.43.0-mr-3201.24](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3201.23...v3.43.0-mr-3201.24) (2025-05-22)



# [3.43.0-mr-3201.23](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3201.22...v3.43.0-mr-3201.23) (2025-05-22)


### Bug Fixes

* fixed paginate parameters ([b51f71b](https://gitlab.ikatec.cloud/digisac/digisac/commit/b51f71b1492f571fe1e45342ff1118ecbb5d6483))



# [3.43.0-mr-3213.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0...v3.43.0-mr-3213.6) (2025-05-21)



# [3.43.0-mr-3213.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-rc.1...v3.43.0-mr-3213.5) (2025-05-21)



# [3.43.0-mr-3213.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.2-mr-3196.2...v3.43.0-mr-3213.4) (2025-05-21)


### Features

* obfuscate access token on show route ([3a8e8f6](https://gitlab.ikatec.cloud/digisac/digisac/commit/3a8e8f6590d413478eeabbaaf19a59e66763258e))



# [3.43.0-mr-3213.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.1-mr-3158.7...v3.43.0-mr-3213.3) (2025-05-20)



# [3.43.0-mr-3213.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-mr-3212.5...v3.43.0-mr-3213.2) (2025-05-20)



# [3.43.0-mr-3134.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3134.5...v3.43.0-mr-3134.6) (2025-05-14)



# [3.43.0-mr-3134.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-rc.12...v3.43.0-mr-3134.5) (2025-05-14)



# [3.43.0-mr-3192.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-mr-3188.1...v3.43.0-mr-3192.0) (2025-05-13)


### Features

* create migration and update model OAuthAccessToken ([46377ad](https://gitlab.ikatec.cloud/digisac/digisac/commit/46377ad5229c84d11dd11d4486fddd1ef8def47e))
* **departments:** remove old files ([a1ebbf1](https://gitlab.ikatec.cloud/digisac/digisac/commit/a1ebbf15422e02019e076a3c2f308d7113bdad49))
* validate no named token is valid or not and return status from access token ([9d46540](https://gitlab.ikatec.cloud/digisac/digisac/commit/9d4654027d73ccd0568576e4769fb768f339cb24))



# [3.43.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.2...v3.43.0-rc.3) (2025-05-09)


### Bug Fixes

* **ci:** corrige bump de versão ([2baa4b3](https://gitlab.ikatec.cloud/digisac/digisac/commit/2baa4b344883323e50fb25625109b34da143bb6e))
* **ci:** corrige gitlab-ci.yml ([213f8d2](https://gitlab.ikatec.cloud/digisac/digisac/commit/213f8d2efd28d569558e8b8cfcf549d6da056285))





## [3.48.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.3-mr-3349.0...v3.48.3) (2025-06-30)

**Note:** Version bump only for package @digisac/back





## [3.48.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1-mr-3305.3...v3.48.2) (2025-06-26)

**Note:** Version bump only for package @digisac/back





## [3.48.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.1-mr-3338.0...v3.48.1) (2025-06-25)

**Note:** Version bump only for package @digisac/back





# [3.48.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.48.0-rc.17...v3.48.0) (2025-06-24)

**Note:** Version bump only for package @digisac/back





## [3.47.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.0-rc.14...v3.47.3) (2025-06-18)

**Note:** Version bump only for package @digisac/back





## [3.47.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.2-mr-3313.0...v3.47.2) (2025-06-18)

**Note:** Version bump only for package @digisac/back





## [3.47.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.47.1-mr-3319.0...v3.47.1) (2025-06-18)

**Note:** Version bump only for package @digisac/back





# [3.47.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.46.0...v3.47.0) (2025-06-11)



## [3.45.1-mr-3254.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.46.0-rc.12...v3.45.1-mr-3254.0) (2025-06-03)


### Features

* **adapter:** ajustes ([6e67857](https://gitlab.ikatec.cloud/digisac/digisac/commit/6e678576699c8539cdc80ce81596200dae34425e))
* **sendMessage:** mock para envio de mensagem waba provedor meta ([9d99d6f](https://gitlab.ikatec.cloud/digisac/digisac/commit/9d99d6f887ca519a1431d995c3ab48d24aea2a35))





# [3.46.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.46.0-rc.19...v3.46.0) (2025-06-10)

**Note:** Version bump only for package @digisac/back





## [3.45.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.1-mr-3298.2...v3.45.2) (2025-06-09)

**Note:** Version bump only for package @digisac/back





## [3.45.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-mr-3281.4...v3.45.1) (2025-06-09)

**Note:** Version bump only for package @digisac/back





# [3.45.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.45.0-rc.12...v3.45.0) (2025-06-02)

**Note:** Version bump only for package @digisac/back





# [3.44.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.44.0-rc.12...v3.44.0) (2025-06-02)

**Note:** Version bump only for package @digisac/back





## [3.43.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.3-mr-3251.1...v3.43.4) (2025-06-02)

**Note:** Version bump only for package @digisac/back





## [3.43.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.2...v3.43.3) (2025-05-30)

**Note:** Version bump only for package @digisac/back





## [3.43.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.1-mr-3166.5...v3.43.2) (2025-05-27)

**Note:** Version bump only for package @digisac/back





## [3.43.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.1-mr-3226.1...v3.43.1) (2025-05-26)

**Note:** Version bump only for package @digisac/back





# [3.43.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.43.0-rc.14...v3.43.0) (2025-05-21)

**Note:** Version bump only for package @digisac/back





## [3.42.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.1-mr-3158.7...v3.42.2) (2025-05-21)

**Note:** Version bump only for package @digisac/back





## [3.42.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.1-mr-3186.0...v3.42.1) (2025-05-13)

**Note:** Version bump only for package @digisac/back





# [3.42.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.2...v3.42.0) (2025-05-12)



# [3.42.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3163.2...v3.42.0-rc.10) (2025-05-06)



# [3.42.0-mr-3163.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.9...v3.42.0-mr-3163.2) (2025-05-06)



# [3.42.0-rc.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3124.0...v3.42.0-rc.9) (2025-05-06)



# [3.42.0-mr-3124.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.8...v3.42.0-mr-3124.0) (2025-05-06)



# [3.42.0-rc.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.7...v3.42.0-rc.8) (2025-05-06)



# [3.42.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3164.5...v3.42.0-rc.7) (2025-05-06)



# [3.42.0-mr-3164.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3164.4...v3.42.0-mr-3164.5) (2025-05-06)



# [3.42.0-mr-3164.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.6...v3.42.0-mr-3164.4) (2025-05-06)



# [3.42.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.1...v3.42.0-rc.6) (2025-05-06)



# [3.42.0-mr-3164.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3164.2...v3.42.0-mr-3164.3) (2025-05-06)



# [3.42.0-mr-3164.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3164.1...v3.42.0-mr-3164.2) (2025-05-06)



# [3.42.0-mr-3164.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3124.4...v3.42.0-mr-3164.0) (2025-05-06)



# [3.41.0-mr-3124.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-mr-3163.1...v3.41.0-mr-3124.4) (2025-05-06)



# [3.42.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3160.1...v3.42.0-rc.5) (2025-05-06)



# [3.41.0-mr-3124.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3153.0...v3.41.0-mr-3124.3) (2025-05-06)



# [3.41.0-mr-3153.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.23...v3.41.0-mr-3153.0) (2025-05-06)



# [3.41.0-rc.23](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3122.1...v3.41.0-rc.23) (2025-05-06)



# [3.41.0-mr-3122.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3160.0...v3.41.0-mr-3122.1) (2025-05-06)



# [3.42.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0...v3.42.0-rc.4) (2025-05-05)



# [3.42.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.22...v3.42.0-rc.3) (2025-05-05)



# [3.41.0-rc.22](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3127.1...v3.41.0-rc.22) (2025-05-05)



# [3.41.0-mr-3127.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3127.0...v3.41.0-mr-3127.1) (2025-05-05)



# [3.41.0-mr-3127.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3085.0...v3.41.0-mr-3127.0) (2025-05-05)



# [3.41.0-mr-3085.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.21...v3.41.0-mr-3085.0) (2025-05-05)



# [3.41.0-rc.21](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3128.3...v3.41.0-rc.21) (2025-05-05)



# [3.41.0-mr-3128.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.6...v3.41.0-mr-3128.3) (2025-05-05)



# [3.41.0-mr-2919.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.5...v3.41.0-mr-2919.6) (2025-05-05)



# [3.41.0-mr-2919.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.4...v3.41.0-mr-2919.5) (2025-05-05)



# [3.41.0-mr-2919.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.2...v3.41.0-mr-2919.4) (2025-05-05)



# [3.42.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3124.2...v3.42.0-rc.2) (2025-05-05)



# [3.41.0-mr-3124.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.20...v3.41.0-mr-3124.2) (2025-05-05)



# [3.41.0-rc.20](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.3...v3.41.0-rc.20) (2025-05-05)



# [3.41.0-mr-2919.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3137.1...v3.41.0-mr-2919.3) (2025-05-05)



# [3.41.0-mr-3137.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.2...v3.41.0-mr-3137.1) (2025-05-02)



# [3.41.0-mr-2919.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-2919.1...v3.41.0-mr-2919.2) (2025-05-02)



# [3.41.0-mr-2919.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.5-mr-3085.2...v3.41.0-mr-2919.1) (2025-05-02)



## [3.42.5-mr-3085.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3128.2...v3.42.5-mr-3085.2) (2025-05-02)



# [3.41.0-mr-3128.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.2-mr-3153.0...v3.41.0-mr-3128.2) (2025-05-02)



## [3.42.1-rc.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.1-mr-3125.1...v3.42.1-rc.0) (2025-04-30)



## [3.42.1-mr-3125.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.42.0-rc.1...v3.42.1-mr-3125.1) (2025-04-30)



# [3.42.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.19...v3.42.0-rc.1) (2025-04-30)



## [3.37.5-mr-3085.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3090.3...v3.37.5-mr-3085.1) (2025-04-29)


### Bug Fixes

* pontos mr ([eac9702](https://gitlab.ikatec.cloud/digisac/digisac/commit/eac9702b163a3fd1ad6284fcf7afdbb9f60d8812))
* SD-1415 - filter out archived departments ([b3eaf13](https://gitlab.ikatec.cloud/digisac/digisac/commit/b3eaf13a7716221cb50a1fac5773231be2bb83ae))



# [3.41.0-mr-3090.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.39.1-mr-3104.2...v3.41.0-mr-3090.2) (2025-04-29)



## [3.40.1-mr-3125.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3128.1...v3.40.1-mr-3125.0) (2025-04-28)



# [3.41.0-mr-3128.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.1-mr-3137.0...v3.41.0-mr-3128.1) (2025-04-28)



# [3.41.0-mr-3122.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.9...v3.41.0-mr-3122.0) (2025-04-25)



# [3.41.0-mr-3090.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.0-mr-3131.1...v3.41.0-mr-3090.1) (2025-04-25)



# [3.41.0-mr-3090.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3130.0...v3.41.0-mr-3090.0) (2025-04-25)



# [3.41.0-mr-2919.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3051.0...v3.41.0-mr-2919.0) (2025-04-25)



# [3.41.0-mr-3128.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.39.2-mr-3121.3...v3.41.0-mr-3128.0) (2025-04-25)



# [3.41.0-mr-3124.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.1-mr-3109.2...v3.41.0-mr-3124.0) (2025-04-25)



## [3.36.1-mr-3109.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.0-mr-3124.1...v3.36.1-mr-3109.2) (2025-04-25)



## [3.39.2-mr-3127.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.7...v3.39.2-mr-3127.0) (2025-04-25)



# [3.40.0-mr-3124.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3123.0...v3.40.0-mr-3124.0) (2025-04-24)


### Features

* criando branch ([25bff23](https://gitlab.ikatec.cloud/digisac/digisac/commit/25bff2376496be767efacae4e8f992a5a7788d7d))
* finalizando notification ([be15c18](https://gitlab.ikatec.cloud/digisac/digisac/commit/be15c180615d0cd47447ef07d559b5d976ab8626))



## [3.37.5-mr-3085.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.5-mr-3083.0...v3.37.5-mr-3085.0) (2025-04-14)





## [3.41.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.1-mr-2602.11...v3.41.2) (2025-05-08)

**Note:** Version bump only for package @digisac/back





## [3.41.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-mr-3160.2...v3.41.1) (2025-05-06)

**Note:** Version bump only for package @digisac/back





# [3.41.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.41.0-rc.19...v3.41.0) (2025-05-05)

**Note:** Version bump only for package @digisac/back





## [3.40.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.1-mr-3147.1...v3.40.1) (2025-04-30)

**Note:** Version bump only for package @digisac/back





# [3.40.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.40.0-rc.9...v3.40.0) (2025-04-28)

**Note:** Version bump only for package @digisac/back





## [3.39.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.39.1-mr-3093.1...v3.39.2) (2025-04-28)

**Note:** Version bump only for package @digisac/back





## [3.39.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.39.0-rc.19...v3.39.1) (2025-04-22)

**Note:** Version bump only for package @digisac/back





## [3.38.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.38.1-mr-3113.0...v3.38.1) (2025-04-22)

**Note:** Version bump only for package @digisac/back





# [3.38.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.38.0-rc.5...v3.38.0) (2025-04-14)

**Note:** Version bump only for package @digisac/back





## [3.37.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.4-mr-3080.0...v3.37.4) (2025-04-11)

**Note:** Version bump only for package @digisac/back





## [3.37.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.2-mr-3066.1...v3.37.3) (2025-04-10)

**Note:** Version bump only for package @digisac/back





## [3.37.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.6...v3.37.2) (2025-04-09)

**Note:** Version bump only for package @digisac/back





## [3.36.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.3-mr-3048.6...v3.36.6) (2025-04-08)

**Note:** Version bump only for package @digisac/back





## [3.37.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.37.0-rc.14...v3.37.1) (2025-04-07)

**Note:** Version bump only for package @digisac/back





## [3.36.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.5-mr-3059.0...v3.36.5) (2025-04-07)

**Note:** Version bump only for package @digisac/back





## [3.36.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.3-mr-3041.2...v3.36.4) (2025-04-03)

**Note:** Version bump only for package @digisac/back





## [3.36.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.1-mr-3016.3...v3.36.2) (2025-03-27)

**Note:** Version bump only for package @digisac/back





## [3.36.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.0-mr-3038.1...v3.36.1) (2025-03-26)

**Note:** Version bump only for package @digisac/back





# [3.36.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.36.0-rc.8...v3.36.0) (2025-03-25)

**Note:** Version bump only for package @digisac/back





## [3.35.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.35.1-mr-3010.1...v3.35.1) (2025-03-21)

**Note:** Version bump only for package @digisac/back





# [3.35.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.35.0-rc.12...v3.35.0) (2025-03-17)

**Note:** Version bump only for package @digisac/back





## [3.34.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.34.3-mr-2986.1...v3.34.3) (2025-03-12)

**Note:** Version bump only for package @digisac/back





## [3.34.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.34.2-mr-2969.1...v3.34.2) (2025-03-07)

**Note:** Version bump only for package @digisac/back





## [3.34.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.34.1-mr-2968.0...v3.34.1) (2025-03-06)

**Note:** Version bump only for package @digisac/back





# [3.34.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.34.0-rc.9...v3.34.0) (2025-03-05)

**Note:** Version bump only for package @digisac/back





# [3.33.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.3...v3.33.0) (2025-02-24)



# [3.33.0-rc.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.2...v3.33.0-rc.10) (2025-02-21)



# [3.33.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2920.0...v3.33.0-rc.7) (2025-02-20)



# [3.33.0-mr-2920.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-rc.6...v3.33.0-mr-2920.0) (2025-02-20)



# [3.33.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.3...v3.33.0-rc.6) (2025-02-20)



# [3.33.0-mr-2719.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.2...v3.33.0-mr-2719.3) (2025-02-20)


### Bug Fixes

* remove character limitation from general validation of short text custom fields ([0d0480b](https://gitlab.ikatec.cloud/digisac/digisac/commit/0d0480be9e0dc0c6b4ae059ff5f849f76995b3cc))



# [3.33.0-mr-2719.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.1...v3.33.0-mr-2719.2) (2025-02-19)



# [3.33.0-mr-2719.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-rc.5...v3.33.0-mr-2719.1) (2025-02-19)



# [3.33.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.0...v3.33.0-rc.5) (2025-02-19)



# [3.33.0-mr-2719.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2746.13...v3.33.0-mr-2719.0) (2025-02-19)



# [3.33.0-mr-2746.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2841.2...v3.33.0-mr-2746.13) (2025-02-19)



# [3.33.0-mr-2841.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-rc.4...v3.33.0-mr-2841.2) (2025-02-19)



# [3.33.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.1-mr-2927.0...v3.33.0-rc.4) (2025-02-19)



# [3.33.0-mr-2746.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2746.11...v3.33.0-mr-2746.12) (2025-02-18)



# [3.33.0-mr-2746.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2719.4...v3.33.0-mr-2746.11) (2025-02-18)



# [3.33.0-mr-2719.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-mr-2920.1...v3.33.0-mr-2719.4) (2025-02-18)



# [3.33.0-mr-2841.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-mr-2923.2...v3.33.0-mr-2841.1) (2025-02-18)



# [3.33.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2912.0...v3.33.0-rc.3) (2025-02-17)



# [3.33.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.33.0-mr-2900.0...v3.33.0-rc.2) (2025-02-17)



# [3.33.0-mr-2900.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-mr-2915.1...v3.33.0-mr-2900.0) (2025-02-17)



# [3.33.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-mr-2915.0...v3.33.0-rc.1) (2025-02-14)



## [3.31.1-dev.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2878.3...v3.31.1-dev.1) (2025-02-14)



## [3.31.1-mr-2878.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.16...v3.31.1-mr-2878.3) (2025-02-14)



## [3.31.1-mr-2878.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.15...v3.31.1-mr-2878.2) (2025-02-13)



# [3.31.0-mr-2892.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.9...v3.31.0-mr-2892.2) (2025-02-10)



# [3.31.0-mr-2892.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2892.0...v3.31.0-mr-2892.1) (2025-02-10)



# [3.31.0-mr-2892.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2900.0...v3.31.0-mr-2892.0) (2025-02-10)



## [3.31.1-mr-2900.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2874.9...v3.31.1-mr-2900.0) (2025-02-10)



# [3.26.0-mr-2746.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2877.5...v3.26.0-mr-2746.10) (2025-02-05)



## [3.31.1-mr-2878.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1-mr-2874.5...v3.31.1-mr-2878.1) (2025-02-05)


### Features

* altearando o openai como IA default do digisac ([45b4f8b](https://gitlab.ikatec.cloud/digisac/digisac/commit/45b4f8bdd15151e5521f592005e4e38e90e54296))



## [3.31.1-mr-2878.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2877.0...v3.31.1-mr-2878.0) (2025-02-04)


### Features

* altearando o openai como IA default do digisac ([18fb7a3](https://gitlab.ikatec.cloud/digisac/digisac/commit/18fb7a357579d3ff09cae27590baff0d98fafe73))



## [3.30.1-mr-2841.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-mr-2858.0...v3.30.1-mr-2841.0) (2025-01-29)



## [3.30.1-mr-2840.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.1-mr-2854.3...v3.30.1-mr-2840.3) (2025-01-28)



## [3.30.1-mr-2840.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2855.0...v3.30.1-mr-2840.2) (2025-01-28)


### Bug Fixes

* altera migration para executar novas permissoes em cargos no-admin ([df9a5cf](https://gitlab.ikatec.cloud/digisac/digisac/commit/df9a5cf638f91b74a514d8567d7912e76b6b7856))



## [3.30.1-mr-2840.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1...v3.30.1-mr-2840.1) (2025-01-27)


### Features

* criacao da permissao de download no front ([dbce815](https://gitlab.ikatec.cloud/digisac/digisac/commit/dbce8157a42273e75ad1328ed10baf9942bd588f))



## [3.30.1-mr-2840.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-mr-2814.0...v3.30.1-mr-2840.0) (2025-01-23)


### Features

* create migration run-seed-permissions ([fc043a7](https://gitlab.ikatec.cloud/digisac/digisac/commit/fc043a7e8c33e6293290dc160fb361cb22ad5fd2))



# [3.29.0-mr-2719.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.28.1-dev.0...v3.29.0-mr-2719.3) (2025-01-08)



# [3.29.0-mr-2719.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.1-dev.2...v3.29.0-mr-2719.2) (2025-01-07)



# [3.27.0-mr-2719.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.1-mr-2732.0...v3.27.0-mr-2719.1) (2025-01-06)



# [3.26.0-mr-2759.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-mr-2696.12...v3.26.0-mr-2759.4) (2024-12-31)



# [3.26.0-mr-2696.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-mr-2719.0...v3.26.0-mr-2696.12) (2024-12-31)


### Features

* temporarily removes custom field validations ([96ddcbf](https://gitlab.ikatec.cloud/digisac/digisac/commit/96ddcbf4db62b083fd0fa609693a7ac32cd379d3))



# [3.26.0-mr-2719.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-rc.11...v3.26.0-mr-2719.0) (2024-12-31)



# [3.26.0-mr-2755.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2761.2...v3.26.0-mr-2755.3) (2024-12-30)



# [3.25.0-mr-2759.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2755.3...v3.25.0-mr-2759.1) (2024-12-30)


### Features

* add identifier code change behavior for new custom fields ([2fe03a2](https://gitlab.ikatec.cloud/digisac/digisac/commit/2fe03a293fdc1c9a4a4cb4699ea00541dc1adbea))



# [3.25.0-mr-2759.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2757.0...v3.25.0-mr-2759.0) (2024-12-27)



# [3.25.0-mr-2755.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.1-mr-2756.0...v3.25.0-mr-2755.1) (2024-12-27)



# [3.26.0-mr-2696.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-rc.9...v3.26.0-mr-2696.11) (2024-12-24)



# [3.26.0-mr-2696.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-mr-2696.9...v3.26.0-mr-2696.10) (2024-12-23)


### Bug Fixes

* Correção de importações indevidas ([b3ff569](https://gitlab.ikatec.cloud/digisac/digisac/commit/b3ff5692d3ea8a94f2c6a18312b235e6faba43f2))



# [3.26.0-mr-2696.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-mr-2696.8...v3.26.0-mr-2696.9) (2024-12-23)



# [3.26.0-mr-2696.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0...v3.26.0-mr-2696.8) (2024-12-23)



# [3.26.0-mr-2719.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.2-dev.1...v3.26.0-mr-2719.2) (2024-12-20)



# [3.25.0-mr-2719.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.24.1-dev.2...v3.25.0-mr-2719.1) (2024-12-13)



## [3.24.1-dev.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-rc.9...v3.24.1-dev.2) (2024-12-13)





## [3.32.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.3-mr-2939.0...v3.32.3) (2025-02-24)

**Note:** Version bump only for package @digisac/back





## [3.32.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.2-mr-2934.0...v3.32.2) (2025-02-21)

**Note:** Version bump only for package @digisac/back





# [3.32.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.32.0-rc.22...v3.32.0) (2025-02-18)

**Note:** Version bump only for package @digisac/back





## [3.31.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2908.2...v3.31.2) (2025-02-18)

**Note:** Version bump only for package @digisac/back





## [3.31.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.1-mr-2885.4...v3.31.1) (2025-02-13)

**Note:** Version bump only for package @digisac/back





# [3.31.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.31.0-rc.18...v3.31.0) (2025-01-29)

**Note:** Version bump only for package @digisac/back





## [3.30.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1-mr-2854.6...v3.30.2) (2025-01-29)

**Note:** Version bump only for package @digisac/back





## [3.30.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.1-mr-2844.3...v3.30.1) (2025-01-27)

**Note:** Version bump only for package @digisac/back





# [3.30.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.30.0-rc.11...v3.30.0) (2025-01-21)

**Note:** Version bump only for package @digisac/back





## [3.29.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.2-mr-2811.2...v3.29.3) (2025-01-20)

**Note:** Version bump only for package @digisac/back





## [3.29.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.1-mr-2817.1...v3.29.2) (2025-01-17)

**Note:** Version bump only for package @digisac/back





## [3.29.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.1-mr-2816.1...v3.29.1) (2025-01-17)

**Note:** Version bump only for package @digisac/back





# [3.29.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.29.0-rc.16...v3.29.0) (2025-01-14)

**Note:** Version bump only for package @digisac/back





## [3.28.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.28.1-mr-2806.2...v3.28.2) (2025-01-13)

**Note:** Version bump only for package @digisac/back





## [3.28.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.28.0...v3.28.1) (2025-01-10)


### Bug Fixes

* **whatsappBusiness:** add nullable check ([0919907](https://gitlab.ikatec.cloud/digisac/digisac/commit/09199077e9948e0aa15ec46d7c196c3330fa428a))





# [3.28.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.28.0-rc.4...v3.28.0) (2025-01-07)


### Features

* force build version 3.28.0 ([f4ec577](https://gitlab.ikatec.cloud/digisac/digisac/commit/f4ec5777c7c13e664ae70357fedca3ad0f1da426))





# [3.27.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-rc.10...v3.27.0) (2025-01-02)



# [3.26.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.27.0-mr-2731.3...v3.26.0) (2024-12-23)

**Note:** Version bump only for package @digisac/back





# [3.26.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.26.0-rc.7...v3.26.0) (2024-12-23)

**Note:** Version bump only for package @digisac/back





## [3.25.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-mr-2642.4...v3.25.1) (2024-12-16)

**Note:** Version bump only for package @digisac/back





# [3.25.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.25.0-rc.9...v3.25.0) (2024-12-16)

**Note:** Version bump only for package @digisac/back





# [3.24.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.24.0-rc.11...v3.24.0) (2024-12-09)

**Note:** Version bump only for package @digisac/back





# [3.23.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.23.0-rc.13...v3.23.0) (2024-12-02)

**Note:** Version bump only for package @digisac/back





## [3.22.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.22.1-mr-2668.0...v3.22.1) (2024-12-02)

**Note:** Version bump only for package @digisac/back





# [3.22.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.22.0-rc.2...v3.22.0) (2024-11-25)

**Note:** Version bump only for package @digisac/back





## [3.20.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.20.1-mr-2592.4...v3.20.1) (2024-11-22)

**Note:** Version bump only for package @digisac/back





## [3.21.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.2-mr-2593.2...v3.21.3) (2024-11-18)

**Note:** Version bump only for package @digisac/back





## [3.21.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.1...v3.21.2) (2024-11-13)



## [3.21.1-mr-2609.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.1-mr-2609.0...v3.21.1-mr-2609.1) (2024-11-13)

**Note:** Version bump only for package @digisac/back





## [3.21.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.1-mr-2610.0...v3.21.1) (2024-11-13)

**Note:** Version bump only for package @digisac/back





# [3.21.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.21.0-rc.3...v3.21.0) (2024-11-12)

**Note:** Version bump only for package @digisac/back





# [3.20.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.20.0-rc.9...v3.20.0) (2024-11-05)

**Note:** Version bump only for package @digisac/back





## [3.19.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.18.3...v3.19.3) (2024-10-31)

**Note:** Version bump only for package @digisac/back





## [3.18.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.18.3-mr-2577.2...v3.18.3) (2024-10-31)

**Note:** Version bump only for package @digisac/back





## [3.18.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.19.0...v3.18.2) (2024-10-31)

**Note:** Version bump only for package @digisac/back





# [3.19.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.19.0-rc.8...v3.19.0) (2024-10-23)

**Note:** Version bump only for package @digisac/back





## [3.18.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.18.1-mr-2515.0...v3.18.1) (2024-10-17)

**Note:** Version bump only for package @digisac/back





# [3.18.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.18.0-rc.19...v3.18.0) (2024-10-14)

**Note:** Version bump only for package @digisac/back





## [3.17.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.5-mr-2505.0...v3.17.5) (2024-10-10)

**Note:** Version bump only for package @digisac/back





## [3.17.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.4-mr-2474.0...v3.17.4) (2024-10-02)

**Note:** Version bump only for package @digisac/back





## [3.17.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.3-mr-2464.1...v3.17.3) (2024-09-26)

**Note:** Version bump only for package @digisac/back





## [3.17.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.1...v3.17.2) (2024-09-25)


### Bug Fixes

* **createAccount:** corrige utilização da feature-flag enableSearchMessages ([8423b42](https://gitlab.ikatec.cloud/digisac/digisac/commit/8423b4204d0387600393449227145f8232ef242f))





## [3.17.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.1-mr-2463.0...v3.17.1) (2024-09-25)

**Note:** Version bump only for package @digisac/back





# [3.17.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.17.0-rc.8...v3.17.0) (2024-09-24)

**Note:** Version bump only for package @digisac/back





## [3.16.12](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.11-mr-2449.1...v3.16.12) (2024-09-24)

**Note:** Version bump only for package @digisac/back





## [3.16.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.7-mr-2442.2...v3.16.11) (2024-09-24)

**Note:** Version bump only for package @digisac/back





## [3.16.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.10-mr-2435.1...v3.16.10) (2024-09-20)

**Note:** Version bump only for package @digisac/back





## [3.16.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.9-mr-2441.7...v3.16.9) (2024-09-18)

**Note:** Version bump only for package @digisac/back





## [3.16.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.7...v3.16.8) (2024-09-18)

**Note:** Version bump only for package @digisac/back





## [3.16.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.7-mr-2443.0...v3.16.7) (2024-09-18)

**Note:** Version bump only for package @digisac/back





## [3.16.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.6-mr-2425.2...v3.16.6) (2024-09-13)

**Note:** Version bump only for package @digisac/back





## [3.16.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.4-mr-2424.1...v3.16.5) (2024-09-13)

**Note:** Version bump only for package @digisac/back





## [3.16.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.4-mr-2423.0...v3.16.4) (2024-09-13)

**Note:** Version bump only for package @digisac/back





## [3.16.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.2...v3.16.3) (2024-09-11)


### Bug Fixes

* **account:** corrige bug em accountTransformer ([ce2311b](https://gitlab.ikatec.cloud/digisac/digisac/commit/ce2311bc4d241999c9ea938c7b5ed93ca1c26951))





## [3.16.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.2-mr-2407.1...v3.16.2) (2024-09-10)

**Note:** Version bump only for package @digisac/back





## [3.16.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.1-mr-2370.0...v3.16.1) (2024-09-09)

**Note:** Version bump only for package @digisac/back





# [3.16.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.16.0-rc.7...v3.16.0) (2024-09-05)

**Note:** Version bump only for package @digisac/back





## [3.15.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.3-mr-2400.1...v3.15.3) (2024-09-05)

**Note:** Version bump only for package @digisac/back





## [3.15.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.2-mr-2372.1...v3.15.2) (2024-08-29)

**Note:** Version bump only for package @digisac/back





## [3.15.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0...v3.15.1) (2024-08-21)

**Note:** Version bump only for package @digisac/back





# [3.15.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.1...v3.15.0) (2024-08-21)



# [3.15.0-rc.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.7...v3.15.0-rc.8) (2024-08-21)


### Bug Fixes

* **contacts:** corrige ortografico no nome da coluna archivedAt ([8a2dc9b](https://gitlab.ikatec.cloud/digisac/digisac/commit/8a2dc9b247fb4a168ee2b1bcdc5e497a57b8634b))



# [3.15.0-rc.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.6...v3.15.0-rc.7) (2024-08-20)


### Bug Fixes

* corrige formatação da migration 20240726140451-update-duplicate-contacts ([d94d4a6](https://gitlab.ikatec.cloud/digisac/digisac/commit/d94d4a6db3ef3f95f8984727b53e3958df72fc2f))



# [3.15.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.5...v3.15.0-rc.6) (2024-08-20)


### Bug Fixes

* corrige problema na atualização do status de mensagens gupshup ([0fc204f](https://gitlab.ikatec.cloud/digisac/digisac/commit/0fc204fbab67a86f20d539ed0e22a33555b15116))



# [3.15.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.4...v3.15.0-rc.5) (2024-08-20)



# [3.15.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.7...v3.15.0-rc.4) (2024-08-20)



# [3.15.0-rc.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-mr-2340.0...v3.15.0-rc.3) (2024-08-16)



# [3.15.0-mr-2340.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.15.0-rc.2...v3.15.0-mr-2340.0) (2024-08-16)


### Bug Fixes

* alteração na execução do cron ([7e29349](https://gitlab.ikatec.cloud/digisac/digisac/commit/7e2934907277f2f17f219e25f5f88b4fe01179cb))



# [3.15.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-rc.0...v3.15.0-rc.2) (2024-08-15)



## [3.13.4-rc.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2331.2...v3.13.4-rc.0) (2024-08-15)



## [3.13.4-mr-2331.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.7-mr-2338.0...v3.13.4-mr-2331.2) (2024-08-15)



# [3.15.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.6...v3.15.0-rc.1) (2024-08-15)



# [3.14.0-rc.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.5...v3.14.0-rc.6) (2024-08-15)



# [3.14.0-rc.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.4...v3.14.0-rc.5) (2024-08-15)



# [3.14.0-rc.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-mr-2327.1...v3.14.0-rc.4) (2024-08-14)



# [3.14.0-mr-2327.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.6...v3.14.0-mr-2327.1) (2024-08-14)



# [3.13.0-rc.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-mr-2306.6...v3.13.0-rc.13) (2024-08-14)



# [3.13.0-mr-2306.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.5-mr-2332.1...v3.13.0-mr-2306.6) (2024-08-14)



## [3.13.4-mr-2331.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2331.0...v3.13.4-mr-2331.1) (2024-08-13)



## [3.13.4-mr-2331.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2330.0...v3.13.4-mr-2331.0) (2024-08-13)


### Bug Fixes

* **profile:** melhora a checagem do atributo otpAuthActive na api ([1441d0c](https://gitlab.ikatec.cloud/digisac/digisac/commit/1441d0c639231d2d72e25d47498fe2960fbb634c))



# [3.14.0-mr-2327.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2325.0...v3.14.0-mr-2327.0) (2024-08-13)


### Features

* atualiza versão do whatsapp para 2.3000.1015602493 ([cc750c1](https://gitlab.ikatec.cloud/digisac/digisac/commit/cc750c1fb5831aacc05961a6d2274303d79d1b31))



# [3.14.0-dev.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-mr-2306.5...v3.14.0-dev.10) (2024-08-12)



# [3.13.0-mr-2306.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-mr-2306.4...v3.13.0-mr-2306.5) (2024-08-12)



# [3.13.0-mr-2306.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.3-mr-2323.3...v3.13.0-mr-2306.4) (2024-08-12)



# [3.13.0-mr-2306.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.2...v3.13.0-mr-2306.3) (2024-08-09)


### Bug Fixes

* adiciona required true na querie ([5d01350](https://gitlab.ikatec.cloud/digisac/digisac/commit/5d0135062c67bf7f677e45b456c36a23dc555eef))



# [3.13.0-mr-2306.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.2-mr-2317.3...v3.13.0-mr-2306.2) (2024-08-09)


### Bug Fixes

* remove importacao desnecessaria ([647d32f](https://gitlab.ikatec.cloud/digisac/digisac/commit/647d32fed089535a4cad06e7c105bd389fe8a14a))
* remove importacao desnecessaria ([8558c95](https://gitlab.ikatec.cloud/digisac/digisac/commit/8558c95090fe786cd8a4605feed9f98437554cc4))



# [3.13.0-mr-2306.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-rc.12...v3.13.0-mr-2306.1) (2024-08-07)


### Bug Fixes

* sobe inclusao de usuarios que tenham o chat interno habilitado ([9c36cd3](https://gitlab.ikatec.cloud/digisac/digisac/commit/9c36cd3d33c855da630777f3f3f59d50e556bd43))



# [3.14.0-mr-2312.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-dev.9...v3.14.0-mr-2312.2) (2024-08-06)



# [3.14.0-dev.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.3-mr-2304.2...v3.14.0-dev.9) (2024-08-06)



# [3.14.0-mr-2307.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-mr-2312.1...v3.14.0-mr-2307.0) (2024-08-06)



# [3.14.0-mr-2312.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-mr-2312.0...v3.14.0-mr-2312.1) (2024-08-05)



# [3.14.0-mr-2312.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-mr-2307.5...v3.14.0-mr-2312.0) (2024-08-05)


### Bug Fixes

* validação de erro ([6666883](https://gitlab.ikatec.cloud/digisac/digisac/commit/6666883bd009327b2439716d7a822bf2c15deaf6))



# [3.14.0-mr-2307.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-dev.8...v3.14.0-mr-2307.5) (2024-08-03)



# [3.14.0-dev.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.3...v3.14.0-dev.8) (2024-08-03)



# [3.13.0-dev.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-dev.10...v3.13.0-dev.11) (2024-08-02)


### Reverts

* Revert "chore: atualiza yarn.lock para front e back" ([41ce8d2](https://gitlab.ikatec.cloud/digisac/digisac/commit/41ce8d2e1c6abacc5650279bc7613d77cf614bdd))



# [3.13.0-dev.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.3-mr-2307.4...v3.13.0-dev.10) (2024-08-02)



## [3.13.3-mr-2307.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-dev.9...v3.13.3-mr-2307.4) (2024-08-02)



# [3.13.0-dev.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-dev.8...v3.13.0-dev.9) (2024-08-02)



# [3.13.0-dev.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-dev.0...v3.13.0-dev.8) (2024-08-02)



# [3.14.0-dev.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.2...v3.14.0-dev.0) (2024-08-02)



# [3.13.0-dev.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.3-mr-2307.3...v3.13.0-dev.7) (2024-08-02)



## [3.13.3-mr-2307.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.3-mr-2304.1...v3.13.3-mr-2307.3) (2024-08-02)



## [3.12.3-mr-2307.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-mr-2306.0...v3.12.3-mr-2307.2) (2024-08-02)


### Bug Fixes

* corrige a lógica na redução de retentativas, quando está no stage de otp ([d2224e5](https://gitlab.ikatec.cloud/digisac/digisac/commit/d2224e58dde13a9ad7b04a318b9b32171f45909b))



# [3.13.0-mr-2306.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-dev.5...v3.13.0-mr-2306.0) (2024-08-02)


### Features

* cria job para alterar o token do chat interno e atualizar a url ([899f3eb](https://gitlab.ikatec.cloud/digisac/digisac/commit/899f3eb29c122b9eadf8aafc006730046831a507))



# [3.13.0-dev.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.3-mr-2304.0...v3.13.0-dev.5) (2024-07-31)





## [3.14.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0...v3.14.1) (2024-08-21)

**Note:** Version bump only for package @digisac/back





# [3.14.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.14.0-rc.7...v3.14.0) (2024-08-21)

**Note:** Version bump only for package @digisac/back





## [3.13.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.7-mr-2338.0...v3.13.7) (2024-08-16)

**Note:** Version bump only for package @digisac/back





## [3.13.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.5-mr-2332.2...v3.13.6) (2024-08-14)

**Note:** Version bump only for package @digisac/back





## [3.13.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2325.1...v3.13.5) (2024-08-14)

**Note:** Version bump only for package @digisac/back





## [3.13.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.4-mr-2330.0...v3.13.4) (2024-08-13)

**Note:** Version bump only for package @digisac/back





## [3.13.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.3-mr-2323.3...v3.13.3) (2024-08-12)

**Note:** Version bump only for package @digisac/back





## [3.13.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.2-mr-2317.3...v3.13.2) (2024-08-09)

**Note:** Version bump only for package @digisac/back





## [3.13.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0...v3.13.1) (2024-08-07)

**Note:** Version bump only for package @digisac/back





# [3.13.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.13.0-rc.12...v3.13.0) (2024-08-07)

**Note:** Version bump only for package @digisac/back





## [3.12.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.3-mr-2304.4...v3.12.3) (2024-08-07)

**Note:** Version bump only for package @digisac/back





## [3.12.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.2-mr-2291.0...v3.12.2) (2024-07-26)

**Note:** Version bump only for package @digisac/back





## [3.12.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.1-mr-2290.1...v3.12.1) (2024-07-24)

**Note:** Version bump only for package @digisac/back





# [3.12.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.12.0-rc.10...v3.12.0) (2024-07-23)

**Note:** Version bump only for package @digisac/back





## [3.11.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.8-mr-2281.5...v3.11.9) (2024-07-18)

**Note:** Version bump only for package @digisac/back





## [3.11.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.7-mr-2272.3...v3.11.8) (2024-07-18)

**Note:** Version bump only for package @digisac/back





## [3.11.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.6-mr-2273.6...v3.11.7) (2024-07-18)

**Note:** Version bump only for package @digisac/back





## [3.11.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.5-mr-2278.1...v3.11.6) (2024-07-18)

**Note:** Version bump only for package @digisac/back





## [3.11.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.5-mr-2274.0...v3.11.5) (2024-07-17)

**Note:** Version bump only for package @digisac/back





## [3.11.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.3...v3.11.4) (2024-07-11)

**Note:** Version bump only for package @digisac/back





## [3.11.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.3-mr-2254.0...v3.11.3) (2024-07-10)

**Note:** Version bump only for package @digisac/back





## [3.11.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.2-mr-2245.0...v3.11.2) (2024-07-04)

**Note:** Version bump only for package @digisac/back





# [3.11.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.0-rc.16...v3.11.0) (2024-07-03)



## [3.10.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.11.0-mr-2237.0...v3.10.3) (2024-07-02)

**Note:** Version bump only for package @digisac/back





## [3.10.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.10.2-mr-2228.2...v3.10.3) (2024-07-02)

**Note:** Version bump only for package @digisac/back





# [3.10.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.10.0-rc.6...v3.10.0) (2024-06-17)

**Note:** Version bump only for package @digisac/back





## [3.9.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.4-mr-2178.5...v3.9.5) (2024-06-05)

**Note:** Version bump only for package @digisac/back





## [3.9.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.3-mr-2159.1...v3.9.3) (2024-05-28)

**Note:** Version bump only for package @digisac/back





## [3.9.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.1-mr-2152.1...v3.9.1) (2024-05-22)

**Note:** Version bump only for package @digisac/back





# [3.9.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.9.0-rc.5...v3.9.0) (2024-05-20)

**Note:** Version bump only for package @digisac/back





## [3.8.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.4...v3.8.5) (2024-05-16)

**Note:** Version bump only for package @digisac/back





## [3.8.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.3-mr-2115.4...v3.8.4) (2024-05-09)

**Note:** Version bump only for package @digisac/back





## [3.8.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.2-mr-2124.2...v3.8.3) (2024-05-09)

**Note:** Version bump only for package @digisac/back





## [3.8.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.2-mr-2123.3...v3.8.2) (2024-05-09)

**Note:** Version bump only for package @digisac/back





## [3.8.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.1-mr-2121.1...v3.8.1) (2024-05-07)

**Note:** Version bump only for package @digisac/back





# [3.8.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1...v3.8.0) (2024-05-06)



# [3.8.0-rc.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.8.0-rc.1...v3.8.0-rc.2) (2024-04-29)



# [3.8.0-rc.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-dev.3...v3.8.0-rc.1) (2024-04-29)



## [3.7.1-dev.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2106.1...v3.7.1-dev.3) (2024-04-29)



## [3.7.1-mr-2106.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-dev.2...v3.7.1-mr-2106.1) (2024-04-29)



## [3.7.1-dev.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2106.0...v3.7.1-dev.2) (2024-04-29)



## [3.7.1-mr-2106.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.8...v3.7.1-mr-2106.0) (2024-04-29)


### Features

* **whatsapp:** adiciona as alterações relacionadas a versão 2.3000.x do whatsapp ([ba48393](https://gitlab.ikatec.cloud/digisac/digisac/commit/ba483933ed51f4b08426d1e95a6a4c5c7bb3619e))



## [3.7.1-mr-2094.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.7...v3.7.1-mr-2094.8) (2024-04-29)


### Bug Fixes

* **whatsapp:** corrige envio citação de mensagens. ([31a1671](https://gitlab.ikatec.cloud/digisac/digisac/commit/31a16713d71007d428d9a2ffb8dc5579229df027))



## [3.7.1-mr-2094.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.5...v3.7.1-mr-2094.6) (2024-04-28)


### Bug Fixes

* **distribution:** corrige erro apontado em trace. ([879dd7d](https://gitlab.ikatec.cloud/digisac/digisac/commit/879dd7d81d01a83c76382f7b656aec63a4aff35f))
* **whatsapp:** corrige problema de concorrência no envio de mensagem e no retorno. ([e612aff](https://gitlab.ikatec.cloud/digisac/digisac/commit/e612aff7a3959f17b6ea1330111c0d3c02c3af4c))



## [3.7.1-mr-2094.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-dev.1...v3.7.1-mr-2094.4) (2024-04-26)


### Bug Fixes

* **whatsapp:** incremento no timeout do evento de recebimento de mensagem, para evitar concorrência em sendAndSave. ([d45101f](https://gitlab.ikatec.cloud/digisac/digisac/commit/d45101fb33aed3b5d84e5b9ff6109bb6f62d35bc))



## [3.7.1-mr-2094.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2094.2...v3.7.1-mr-2094.3) (2024-04-25)



## [3.7.1-mr-2094.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-dev.0...v3.7.1-mr-2094.2) (2024-04-23)



## [3.7.1-dev.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.0...v3.7.1-dev.0) (2024-04-23)



## [3.2.1-mr-2094.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.0-mr-2098.0...v3.2.1-mr-2094.1) (2024-04-22)


### Bug Fixes

* **serverPod:** corrige inicialização da sessão no browserless ([a22d287](https://gitlab.ikatec.cloud/digisac/digisac/commit/a22d287605ee22da9c524ef02c2c1d5723b0eebc))
* **serverPod:** corrige reinicialização e backup das conexões. ([019d04e](https://gitlab.ikatec.cloud/digisac/digisac/commit/019d04e84fe2066218c9ede50dd7cb0a0c7917eb))
* **services:** corrige log para serverpod indisponível para a conexão. ([a21dde1](https://gitlab.ikatec.cloud/digisac/digisac/commit/a21dde1d37b613e0e93cf621fb5c75ab69f55bb9))


### Features

* adiciona os testes e2e utilizados na LowLevel ([591cbbc](https://gitlab.ikatec.cloud/digisac/digisac/commit/591cbbc6968150f1008c520105e4da65730d84f2))
* **browser:** recupera instância da conexão com o serverpod relacionado. ([5e4b76e](https://gitlab.ikatec.cloud/digisac/digisac/commit/5e4b76ed767a90e9f8bbf844bb847e498b46df9d))
* **core:** adiciona tratativa ao nível de log warning ([7a0e70d](https://gitlab.ikatec.cloud/digisac/digisac/commit/7a0e70de18a359fc563ca35f390a72c8e6baf203))
* **core:** implementa timeout error na execução do pooling ([c89f887](https://gitlab.ikatec.cloud/digisac/digisac/commit/c89f887049929edeb3e5ca55ce365e517e5a7616))
* **tests:** atualiza test e2e do BrowserDataBackuper ([9486659](https://gitlab.ikatec.cloud/digisac/digisac/commit/94866591b23546b7f64c00d0cb60b1a773989afb))



## [3.2.1-mr-2094.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.1-mr-2087.4...v3.2.1-mr-2094.0) (2024-04-19)


### Bug Fixes

* ajustes para subir o serverPod e sincronizar conexões. ([44750bf](https://gitlab.ikatec.cloud/digisac/digisac/commit/44750bf0f2b147a42a6bb73b77422e7d2632d7c8))



## [3.2.1-mr-2087.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.6.4-mr-2092.0...v3.2.1-mr-2087.4) (2024-04-19)


### Features

* refatorações trazidas da MR-2076 ([9f8d790](https://gitlab.ikatec.cloud/digisac/digisac/commit/9f8d790c1e3a77084a4c6a6316c5a0780ec9871a))
* **whatsapp:** adiciona refatorações implementadas na MR-2076, como a remoção do PodGateway ([8241087](https://gitlab.ikatec.cloud/digisac/digisac/commit/8241087c721468fea2649ab63dd9369989c70e49))
* **whatsapp:** altera a chamada do job WhatsappRemoteRpcJob ([c5ad002](https://gitlab.ikatec.cloud/digisac/digisac/commit/c5ad002523f9b03536737dbe3cdbe2b5ba16f32b))



## [3.2.1-mr-2087.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.0-rc.1...v3.2.1-mr-2087.2) (2024-04-17)



## [3.2.1-mr-2087.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.6.3...v3.2.1-mr-2087.1) (2024-04-16)



## [3.2.1-mr-2087.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.1-mr-2084.3...v3.2.1-mr-2087.0) (2024-04-16)





## [3.7.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.1-mr-2092.0...v3.7.1) (2024-04-30)

**Note:** Version bump only for package @digisac/back





# [3.7.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.7.0-mr-2097.1...v3.7.0) (2024-04-22)

**Note:** Version bump only for package @digisac/back





## [3.6.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.6.1...v3.6.3) (2024-04-16)

**Note:** Version bump only for package @digisac/back





## [3.6.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.6.0...v3.6.1) (2024-04-12)

**Note:** Version bump only for package @digisac/back





# [3.6.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.5.0...v3.6.0) (2024-04-08)

**Note:** Version bump only for package @digisac/back





# [3.5.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.5.0-rc.3...v3.5.0) (2024-03-25)



## [3.4.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.1-mr-2052.0...v3.4.5) (2024-03-13)

**Note:** Version bump only for package @digisac/back





## [3.4.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.4...v3.4.5) (2024-03-13)

**Note:** Version bump only for package @digisac/back





## [3.4.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.3...v3.4.4) (2024-03-13)


### Bug Fixes

* **campaign:** corrige import de model ([538ce7e](https://gitlab.ikatec.cloud/digisac/digisac/commit/538ce7ee38ce63e71e07871a22dbfd80ea8b2f3d))





## [3.4.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.2-mr-1981.11...v3.4.3) (2024-03-13)

**Note:** Version bump only for package @digisac/back





## [3.4.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.1...v3.4.2) (2024-03-11)

**Note:** Version bump only for package @digisac/back





## [3.4.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.4.0-rc.6...v3.4.1) (2024-03-11)


### Bug Fixes

* ajuste em CSS no alinhamento do contador de chamados. ([8462e34](https://gitlab.ikatec.cloud/digisac/digisac/commit/8462e34346aad3afc910e957824e7e64c5958bb8))





## [3.3.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.3.2-mr-2040.6...v3.3.2) (2024-03-01)

**Note:** Version bump only for package @digisac/back





## [3.3.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.3.1-mr-2034.0...v3.3.1) (2024-02-28)

**Note:** Version bump only for package @digisac/back





# [3.3.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.3.0-rc.11...v3.3.0) (2024-02-26)

**Note:** Version bump only for package @digisac/back





## [3.2.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.3-mr-2016.0...v3.2.3) (2024-02-14)

**Note:** Version bump only for package @digisac/back





## [3.2.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.2-mr-2015.2...v3.2.2) (2024-02-09)

**Note:** Version bump only for package @digisac/back





## [3.2.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-mr-2004.4...v3.2.1) (2024-02-07)

**Note:** Version bump only for package @digisac/back





# [3.2.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-rc.37...v3.2.0) (2024-02-06)



# [3.2.0-rc.23](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.8...v3.2.0-rc.23) (2024-02-05)



# [3.2.0-rc.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-rc.16...v3.2.0-rc.17) (2024-02-02)



# [3.2.0-rc.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.2.0-mr-1995.0...v3.2.0-rc.16) (2024-02-02)



# [3.2.0-mr-1995.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.8-mr-1989.0...v3.2.0-mr-1995.0) (2024-02-02)

**Note:** Version bump only for package @digisac/back





## [3.1.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.9...v3.1.10) (2024-02-06)

**Note:** Version bump only for package @digisac/back





## [3.1.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.8-mr-1998.1...v3.1.9) (2024-02-05)

**Note:** Version bump only for package @digisac/back





## [3.1.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.8-mr-1989.0...v3.1.8) (2024-02-02)

**Note:** Version bump only for package @digisac/back





## [3.1.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.6...v3.1.7) (2024-01-30)

**Note:** Version bump only for package @digisac/back





## [3.1.6](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.5...v3.1.6) (2024-01-30)

**Note:** Version bump only for package @digisac/back





## [3.1.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.4...v3.1.5) (2024-01-22)

**Note:** Version bump only for package @digisac/back





## [3.1.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.3...v3.1.4) (2024-01-22)

**Note:** Version bump only for package @digisac/back





## [3.1.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.2...v3.1.3) (2024-01-19)

**Note:** Version bump only for package @digisac/back





## [3.1.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.1-mr-1934.1...v3.1.1) (2024-01-11)

**Note:** Version bump only for package @digisac/back





# [3.1.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.1.0-rc.33...v3.1.0) (2024-01-10)

**Note:** Version bump only for package @digisac/back





## [3.0.19](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.18-mr-1907.3...v3.0.19) (2024-01-02)

**Note:** Version bump only for package @digisac/back





## [3.0.18](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.17-mr-1913.1...v3.0.18) (2024-01-02)

**Note:** Version bump only for package @digisac/back





## [3.0.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.16...v3.0.17) (2024-01-02)

**Note:** Version bump only for package @digisac/back





## [3.0.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.16-mr-1882.1...v3.0.16) (2023-12-11)

**Note:** Version bump only for package @digisac/back





## [3.0.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.15-mr-1849.0...v3.0.15) (2023-11-17)

**Note:** Version bump only for package @digisac/back





## [3.0.14](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.13...v3.0.14) (2023-11-08)


### Reverts

* Revert "fix: validação para migrar estrutura do  serverpod da low level para digisac" ([f029477](https://gitlab.ikatec.cloud/digisac/digisac/commit/f029477b6646177c75eaa81425b017b350178578))





## [3.0.13](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.12...v3.0.13) (2023-11-08)


### Bug Fixes

* validação para migrar estrutura do  serverpod da low level para digisac ([b087442](https://gitlab.ikatec.cloud/digisac/digisac/commit/b087442ddcc8f0bf228633deee569ba5117f4fab))





## [3.0.11](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.11-mr-1827.0...v3.0.11) (2023-10-30)

**Note:** Version bump only for package @digisac/back





## [3.0.10](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.9...v3.0.10) (2023-09-20)


### Bug Fixes

* **contacts:** MN-5216 - correção na exportação de contatos ([fa36704](https://gitlab.ikatec.cloud/digisac/digisac/commit/fa367048037582006703294203260ee60178057e))





## [3.0.9](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.8...v3.0.9) (2023-09-20)


### Bug Fixes

* **services:** MN-5201 - Correção na sincronização de templates da Gupshup ([53e72ab](https://gitlab.ikatec.cloud/digisac/digisac/commit/53e72abe6c0e9cd3a8c6b3c519d0e5e618f6062a))





## [3.0.8](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.7...v3.0.8) (2023-09-20)

**Note:** Version bump only for package @digisac/back





## [3.0.7](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.5...v3.0.7) (2023-08-17)

**Note:** Version bump only for package @digisac/back





## [3.0.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v3.0.0-mr-1632.14...v3.0.5) (2023-08-11)

**Note:** Version bump only for package @digisac/back





# [3.0.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.0.1-mr-1358.37...v3.0.0) (2023-01-31)

## [2.10.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.10.4-mr-1435.0...v2.10.5) (2023-01-16)

**Note:** Version bump only for package @digisac/back

## New Infra

change just for release

## [2.10.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.10.2...v2.10.3) (2022-12-21)

### Bug Fixes

- Release - 2.10.3 ([9f051bc](https://gitlab.ikatec.cloud/digisac/digisac/commit/9f051bc3a13ed322530aa7434017c8634a7bcdb5))

## [2.10.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.10.1...v2.10.2) (2022-12-21)

### Bug Fixes

- Release - 2.10.2 ([4bc3c5c](https://gitlab.ikatec.cloud/digisac/digisac/commit/4bc3c5ce6202142fa064dc88b2f91833d9d1edad))

## [2.10.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.10.0...v2.10.1) (2022-12-15)

### Bug Fixes

- MN-4317- remove linhas reintroduzidas por resolução de conflito no controller de contacts ([c44785e](https://gitlab.ikatec.cloud/digisac/digisac/commit/c44785e9fa6ad242c25a460760c02e9a045ab64c))

# [2.10.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.9.2...v2.10.0) (2022-12-15)

### Features

- release 2.9.5 ([9bb1f89](https://gitlab.ikatec.cloud/digisac/digisac/commit/9bb1f8925dbc3ea28f41ae54d6b77c3a71d17fc4))

## [2.9.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.9.1...v2.9.2) (2022-10-14)

**Note:** Version bump only for package @digisac/back

## [2.9.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.9.0...v2.9.1) (2022-10-10)

### Bug Fixes

- MN-4223 - Corrige migração de hsm para templates e interpolação de parâmetros ([739045f](https://gitlab.ikatec.cloud/digisac/digisac/commit/739045f752d7a5d783c5ab97e315ff8f27c9807e))

# [2.9.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.8.1...v2.9.0) (2022-10-03)

### Features

- Atualização da master 2.8.1 ([b984950](https://gitlab.ikatec.cloud/digisac/digisac/commit/b984950c3ae837c4bb20a1c7982e689cecece353))

## [2.8.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.8.0...v2.8.1) (2022-09-05)

**Note:** Version bump only for package @digisac/back

## Merge Develop

# [2.8.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.2...v2.8.0) (2022-09-05)

## [2.7.1-rc.34](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.33...v2.7.1-rc.34) (2022-09-05)

### Bug Fixes

- **holidayTimeCopy:** MN-3916 - bloqueio para feriados em mesmo periodo ou mesmo nome ([d113f52](https://gitlab.ikatec.cloud/digisac/digisac/commit/d113f52661ff67b39f8e176d0f0a1a7784d41297))

## [2.7.1-rc.33](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.32...v2.7.1-rc.33) (2022-09-01)

## [2.7.1-rc.32](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.31...v2.7.1-rc.32) (2022-08-31)

### Bug Fixes

- MN-4053 - ajuste no botV2 ([9e598f5](https://gitlab.ikatec.cloud/digisac/digisac/commit/9e598f588469fa63ba430e4ffc45933a428ebda4))

## [2.7.1-rc.31](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.30...v2.7.1-rc.31) (2022-08-31)

### Bug Fixes

- **contact:** SI-45 - Envio de mensagem via API, criando contatos sem visibilidade ([ee05756](https://gitlab.ikatec.cloud/digisac/digisac/commit/ee0575618aee783ebc611034e9db7cf07e9de58c))

## [2.7.1-rc.30](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.29...v2.7.1-rc.30) (2022-08-31)

### Features

- MN-3918 - corrige message not found no update de mensagens ([33e002b](https://gitlab.ikatec.cloud/digisac/digisac/commit/33e002b07908aeca90fb5fb7e524abfb679c2449))

## [2.7.1-rc.29](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.28...v2.7.1-rc.29) (2022-08-30)

### Features

- **icons:** MN-3696 - ajustes de ícones no Menu ([e6613b4](https://gitlab.ikatec.cloud/digisac/digisac/commit/e6613b41067ac732e9a2cf180500de90959b1327))

## [2.7.1-rc.28](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.27...v2.7.1-rc.28) (2022-08-29)

## [2.7.1-rc.27](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.26...v2.7.1-rc.27) (2022-08-25)

### Bug Fixes

- SI-80 - Tratando os erros de conexão do telegram ([ffe6fae](https://gitlab.ikatec.cloud/digisac/digisac/commit/ffe6fae9063af9fa1466ddeb214dd04857b5f33f))

## [2.7.1-rc.26](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.25...v2.7.1-rc.26) (2022-08-25)

## [2.7.1-rc.25](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.24...v2.7.1-rc.25) (2022-08-25)

### Bug Fixes

- **campaign:** MN-3909 -edição de campanhas SMS e WhatsApp não abre ([5058c52](https://gitlab.ikatec.cloud/digisac/digisac/commit/5058c523c1186cd0062592e461182ef6321616b4))

## [2.7.1-rc.24](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.23...v2.7.1-rc.24) (2022-08-25)

### Bug Fixes

- **wizard:** MN-4085 - Assistente de nova conta, adicionado condicional ([074778e](https://gitlab.ikatec.cloud/digisac/digisac/commit/074778e24db7a172e6e7cb05ad0d20a692baf577))

## [2.7.1-rc.23](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.22...v2.7.1-rc.23) (2022-08-24)

### Bug Fixes

- SI-90 - ajust import number when not international ([c40d591](https://gitlab.ikatec.cloud/digisac/digisac/commit/c40d5915397ecd511baf9a30cd6014e0dc99c9a2))

## [2.7.1-rc.22](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.21...v2.7.1-rc.22) (2022-08-24)

### Features

- QA-225 - Mapear elementos da tela de chat ([b9e4c29](https://gitlab.ikatec.cloud/digisac/digisac/commit/b9e4c29c1e9f58935bac781501ae76de46e255c3))

## [2.7.1-rc.21](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-rc.20...v2.7.1-rc.21) (2022-08-23)

## [2.7.1-rc.20](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1-mr-1274.0...v2.7.1-rc.20) (2022-08-23)

### Features

- MN-4039 - correção na query de permissões ([a897f3e](https://gitlab.ikatec.cloud/digisac/digisac/commit/a897f3e2fa5498d677f799f12970736d379d4cdd))

## [2.7.1-rc.19](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.1...v2.7.1-rc.19) (2022-08-23)

### Bug Fixes

- SI-46 - trata endereço de e-mail, quando em branco. ([d0190c2](https://gitlab.ikatec.cloud/digisac/digisac/commit/d0190c256e95d50fc6d0f1f638c5188151dc9145))

## [2.7.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.7.0...v2.7.1) (2022-08-16)

### Bug Fixes

- **account:** SI-76 - exclui a permissão tickets.view.all.departments na criação do cargo Administrador. ([d2a9730](https://gitlab.ikatec.cloud/digisac/digisac/commit/d2a973041f03792c6fb35b378dd9abd0a2553430))

# [2.7.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.6.2...v2.7.0) (2022-08-08)

## [2.4.1-rc.31](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.30...v2.4.1-rc.31) (2022-08-02)

### Bug Fixes

- SI-13 - Retirar opção de legenda dos anexos nas conexões Instagram e Facebook ([dd143fa](https://gitlab.ikatec.cloud/digisac/digisac/commit/dd143fa85fe31f5e190895728e9714b9f39447f9))

## [2.4.1-rc.30](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.29...v2.4.1-rc.30) (2022-08-02)

### Bug Fixes

- SI-39 - altera a propriedade resize para default em campos do tipo textarea ([cc1902f](https://gitlab.ikatec.cloud/digisac/digisac/commit/cc1902fe89a26ea853dbbd09e793fcc20985b246))

## [2.4.1-rc.29](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.28...v2.4.1-rc.29) (2022-08-02)

## [2.4.1-rc.28](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.27...v2.4.1-rc.28) (2022-07-29)

### Features

- **tutorials:** MN-3698 - nova ordem dos videos tutoriais ([a277283](https://gitlab.ikatec.cloud/digisac/digisac/commit/a277283fa9bba2b18775332ea90b2fd34727f618))

## [2.4.1-rc.27](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1...v2.4.1-rc.27) (2022-07-29)

### Features

- QA-229 - automação nova tela de campanha ([063865b](https://gitlab.ikatec.cloud/digisac/digisac/commit/063865b66d5d7e8f7ec5cd59ebbb445fc5b5f40f))

## [2.4.1-rc.26](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.25...v2.4.1-rc.26) (2022-07-22)

## [2.4.1-rc.25](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.24...v2.4.1-rc.25) (2022-07-22)

## [2.4.1-rc.24](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.23...v2.4.1-rc.24) (2022-07-22)

### Features

- **filter:** MN-3495 - Possibilidade de filtrar contatos sem tags ([dbebdc9](https://gitlab.ikatec.cloud/digisac/digisac/commit/dbebdc93220e5be9363bb72660f88efd3eb42aa1))

## [2.4.1-rc.23](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.22...v2.4.1-rc.23) (2022-07-20)

### Bug Fixes

- **colors:** MN-3635 - alteração na cores do Digisac ([02bf76b](https://gitlab.ikatec.cloud/digisac/digisac/commit/02bf76b54aae8971d6f7d2a2166ae677b70876fb))

## [2.4.1-rc.22](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.21...v2.4.1-rc.22) (2022-07-20)

### Bug Fixes

- **language:** MN-3674 - modal de cadastro de cargo sem internacionalização ([ccd248f](https://gitlab.ikatec.cloud/digisac/digisac/commit/ccd248ff0f7d7f962c4a1be9b95656d27665903c))

## [2.4.1-rc.21](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.20...v2.4.1-rc.21) (2022-07-20)

### Bug Fixes

- **campaign:** MN-3424 - Restrições na campanha ([7823c37](https://gitlab.ikatec.cloud/digisac/digisac/commit/7823c372163357e98e688db58abb1ea2c21823e3))

## [2.4.1-rc.20](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.19...v2.4.1-rc.20) (2022-07-18)

### Bug Fixes

- **internalChat:** correção na validação para geração de token ([50dce64](https://gitlab.ikatec.cloud/digisac/digisac/commit/50dce644617b8380f8c918dd9684c57993e3f300))

## [2.4.1-rc.18](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.17...v2.4.1-rc.18) (2022-07-14)

### Bug Fixes

- **internalChat:** MN-3980 - correção na geração de token ([42360a6](https://gitlab.ikatec.cloud/digisac/digisac/commit/42360a6792196c3718abe782afaf4d6e1e1ed9ae))

## [2.4.1-rc.17](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.16...v2.4.1-rc.17) (2022-07-13)

## [2.4.1-rc.16](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1-rc.15...v2.4.1-rc.16) (2022-07-12)

### Bug Fixes

- **thumbnails:** MN-2847 -Duplicação de imagens thumbnails ([08d501b](https://gitlab.ikatec.cloud/digisac/digisac/commit/08d501b900b567a2ecb66365673e800308271d28))

## [2.4.1-rc.15](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.0...v2.4.1-rc.15) (2022-07-12)

### Bug Fixes

- **profile:** SI-50 - corrige quando tenta salvar senha em branco na tela de perfil ([a3ab26c](https://gitlab.ikatec.cloud/digisac/digisac/commit/a3ab26c6ea1f5fc2a6d560412080638afb9611bf))

## [2.6.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.6.1...v2.6.2) (2022-08-05)

### Bug Fixes

- SI-138 - correção de parâmetros de HSM e flags do chat interno ([c6d8d6d](https://gitlab.ikatec.cloud/digisac/digisac/commit/c6d8d6dd9c9a0a653373dbff720974c1039e6aa5))

## [2.6.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.6.0...v2.6.1) (2022-08-05)

### Bug Fixes

- versão 2.5.1 ([c5076fe](https://gitlab.ikatec.cloud/digisac/digisac/commit/c5076fe349ac9169a7afa5b56c5260d80bff57f3))

# [2.6.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.5...v2.6.0) (2022-08-05)

### Bug Fixes

- update keys for digisac.biz ([bd63901](https://gitlab.ikatec.cloud/digisac/digisac/commit/bd63901b243d888b6c732354c9b92fc45e7b712d))

### Features

- adiciona digisac.biz dentro do projeto ([c7cdcd4](https://gitlab.ikatec.cloud/digisac/digisac/commit/c7cdcd43f66abae24bee6bdddb92011fcaa0c47f))

## [2.5.5](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.4...v2.5.5) (2022-08-05)

**Note:** Version bump only for package @digisac/back

## [2.5.4](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.3...v2.5.4) (2022-08-05)

### Bug Fixes

- version 2.5.0 ([100e5c6](https://gitlab.ikatec.cloud/digisac/digisac/commit/100e5c63fac5563e1b1da540f5a97c0657f9eaf6))

## [2.5.3](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.2...v2.5.3) (2022-08-05)

### Bug Fixes

- version 2.4.3 ([f417337](https://gitlab.ikatec.cloud/digisac/digisac/commit/f417337366499b1181c1c35ba0eba109faec2be1))

## [2.5.2](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.5.1...v2.5.2) (2022-08-04)

**Note:** Version bump only for package @digisac/back

## [2.5.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.1...v2.5.1) (2022-08-04)

**Note:** Version bump only for package @digisac/back

## [2.4.1](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.4.0...v2.4.1) (2022-07-29)

### Bug Fixes

- **whatsApp:** MN-4026 - Atualiza versão do WhatsApp web para 2.2226.5 ([fb5349c](https://gitlab.ikatec.cloud/digisac/digisac/commit/fb5349cc2918fea70fe34943d18c819f8838906d))

# [2.4.0](https://gitlab.ikatec.cloud/digisac/digisac/compare/v2.3.2...v2.4.0) (2022-07-04)

### Features

- **version:** versão 2.4.3 ([948d839](https://gitlab.ikatec.cloud/digisac/digisac/commit/948d83905515c4787772b2ec521bd10f77039f0f))

## [2.3.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.3.1...v2.3.2) (2022-06-10)

### Bug Fixes

- corrige o tratamento de usuário já autenticado em outra sessão ([91f5a9b](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/91f5a9bbbcbf97c05d60e9b06c85ba373fd58aaa))

## [2.3.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.3.0...v2.3.1) (2022-06-06)

### Bug Fixes

- **bot:** correções e melhorias no socket ([2b87642](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/2b8764230e78d132401866b7a88c6cd301088e5b))

# [2.3.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.6...v2.3.0) (2022-05-30)

### Features

- nova conexão com Google Businesss ([3f5e6dd](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/3f5e6dd4761c99bf6175a2825e49ad237bb9177e))

## [2.2.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.6-mr-1153.3...v2.2.6) (2022-05-26)

**Note:** Version bump only for package @digisac/back

## [2.2.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.4-rc.12...v2.2.5) (2022-05-23)

## [2.2.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.4-rc.1...v2.2.4) (2022-05-09)

**Note:** Version bump only for package @digisac/back

## [2.2.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.4-rc.1...v2.2.4) (2022-05-09)

**Note:** Version bump only for package @digisac/back

## [2.2.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.2...v2.2.3) (2022-04-27)

### Bug Fixes

- **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([bf0359c](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/bf0359c2521ec73756f105230d8db70f37c1b960))

## [2.2.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.1...v2.2.2) (2022-04-20)

### Bug Fixes

- **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([aaa401e](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/aaa401eace6a85ff60291b310a0337e803dc45a9))

## [2.2.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.2.0...v2.2.1) (2022-04-11)

### Bug Fixes

- **internalChat:** corrige conflito entre migrations que geram o token usando a função userResource.generateTokenFromInternalChat ([38880cd](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/38880cdf931a33fc4c60692e28be54a66671fbb8))

# [2.2.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.245...v2.2.0) (2022-04-07)

# [2.1.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.242...v2.1.0) (2022-04-06)

### Features

- ajusta versao de master ([c3fba04](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/c3fba048b00e951e84f80a68aee93e4cf0a3c65a))
- ajusta versao de master ([add0443](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/add04433bf8cf5260b4d567059cc0c172cf889fd))
- ajusta versao de master ([d6d90e9](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/d6d90e9a9f12be645424f0d52310e8cd2cfd75f5))

## [2.0.1-rc.241](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.240...v2.0.1-rc.241) (2022-04-06)

# [2.1.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v2.0.1-rc.241...v2.1.0) (2022-04-06)

### Features

- ajusta versao de master ([c3fba04](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/c3fba048b00e951e84f80a68aee93e4cf0a3c65a))
- ajusta versao de master ([add0443](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/add04433bf8cf5260b4d567059cc0c172cf889fd))
- ajusta versao de master ([d6d90e9](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/d6d90e9a9f12be645424f0d52310e8cd2cfd75f5))

# [1.179.3-mr-389.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-mr-389.0...v1.179.3-mr-389.1) (2021-09-06)

## [1.179.3-mr-368.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.7...v1.179.3-mr-368.0) (2021-08-28)

## [1.179.74](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.73...v1.179.74) (2022-01-04)

### Bug Fixes

- **whatsapp:** altera consulta que força sincronização de mensagens para recuperar os idFromServices distintamente. ([04f7445](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/04f74457684bc477c45ea24f0202625ba1ef093c))

## [1.179.73](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.72...v1.179.73) (2022-01-03)

**Note:** Version bump only for package @digisac/back

## [1.179.72](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.71...v1.179.72) (2022-01-03)

**Note:** Version bump only for package @digisac/back

## [1.179.71](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.70...v1.179.71) (2022-01-03)

**Note:** Version bump only for package @digisac/back

## [1.179.70](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.69...v1.179.70) (2022-01-03)

### Bug Fixes

- **statistics:** ajusta a comparação de datas pois devido ao timezone alguns tickets estavam sendo contados no dia seguinte. ([d688bca](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/d688bca64674ae11c7981e3bca0367d0cbd8a9a0))

## [1.179.69](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.68...v1.179.69) (2022-01-03)

### Bug Fixes

- **integrations:** ajusta permissão de visualização para os pontos onde se tem acesso a integrações. ([ac07e20](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/ac07e207ce2de0a51987878a1b3d0c00435b19a3))

## [1.179.68](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.67...v1.179.68) (2022-01-03)

**Note:** Version bump only for package @digisac/back

## [1.179.67](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.66...v1.179.67) (2021-12-20)

**Note:** Version bump only for package @digisac/back

## [1.179.66](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.65...v1.179.66) (2021-12-20)

### Bug Fixes

- **contacts:** corrige pontos do sistema em que o ofuscamento de número de telefones não estava atendendo a permissão contacts.view.number ([7aa5e2b](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7aa5e2bbf792f263a105aab511e02ed01b99cc48))

## [1.179.65](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.64...v1.179.65) (2021-12-10)

**Note:** Version bump only for package @digisac/back

## [1.179.64](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.63...v1.179.64) (2021-12-10)

**Note:** Version bump only for package @digisac/back

## [1.179.63](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.62...v1.179.63) (2021-12-10)

**Note:** Version bump only for package @digisac/back

## [1.179.62](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.61...v1.179.62) (2021-12-10)

**Note:** Version bump only for package @digisac/back

## [1.179.61](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.60...v1.179.61) (2021-12-10)

### Bug Fixes

- **timezones:** Copia os arquivos do package node_modules/timezones.json, para utils tanto no back como no front; Modifica as abreviações que estão em duplicidade para serem tratadas distintamente. ([746a572](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/746a572317d7b4a310b27f7947c6f44072b19265))

## [1.179.60](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.59...v1.179.60) (2021-11-27)

**Note:** Version bump only for package @digisac/back

## [1.179.59](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.58...v1.179.59) (2021-11-27)

**Note:** Version bump only for package @digisac/back

## [1.179.58](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.57...v1.179.58) (2021-11-27)

**Note:** Version bump only for package @digisac/back

## [1.179.57](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.56...v1.179.57) (2021-11-18)

**Note:** Version bump only for package @digisac/back

## [1.179.56](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.55...v1.179.56) (2021-11-18)

**Note:** Version bump only for package @digisac/back

## [1.179.55](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.54...v1.179.55) (2021-11-12)

**Note:** Version bump only for package @digisac/back

## [1.179.54](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.53...v1.179.54) (2021-11-12)

**Note:** Version bump only for package @digisac/back

## [1.179.53](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.52...v1.179.53) (2021-11-12)

### Features

- **files:** trata range em arquivos ([97cb074](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/97cb0740ad945cd361f444ea569c27a0ea629d78))

- **email:** disponibiliza script que reconverte mensagens de e-mail que já foram convertidas a partir do HTML, necessário quando não foi recebido o text pelo IMAP. ([550adec](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/550adecdd866afa17c371fac6bf0d86d7cede132))

## [1.179.3-rc.7](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.6...v1.179.3-rc.7) (2021-08-26)

**Note:** Version bump only for package @digisac/back

## [1.179.52](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.51...v1.179.52) (2021-11-12)

**Note:** Version bump only for package @digisac/back

## [1.179.51](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.50...v1.179.51) (2021-11-12)

**Note:** Version bump only for package @digisac/back

## [1.179.50](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.49...v1.179.50) (2021-11-12)

**Note:** Version bump only for package @digisac/back

## [1.179.49](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.48...v1.179.49) (2021-11-12)

**Note:** Version bump only for package @digisac/back

## [1.179.48](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.47...v1.179.48) (2021-11-01)

**Note:** Version bump only for package @digisac/back

## [1.179.47](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.46...v1.179.47) (2021-11-01)

**Note:** Version bump only for package @digisac/back

## [1.179.46](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.45...v1.179.46) (2021-10-29)

**Note:** Version bump only for package @digisac/back

## [1.179.45](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.44...v1.179.45) (2021-10-27)

**Note:** Version bump only for package @digisac/back

## [1.179.44](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.43...v1.179.44) (2021-10-26)

**Note:** Version bump only for package @digisac/back

## [1.179.43](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.42...v1.179.43) (2021-10-26)

**Note:** Version bump only for package @digisac/back

## [1.179.42](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.41...v1.179.42) (2021-10-26)

**Note:** Version bump only for package @digisac/back

## [1.179.41](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.40...v1.179.41) (2021-10-26)

**Note:** Version bump only for package @digisac/back

## [1.179.40](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.39...v1.179.40) (2021-10-26)

**Note:** Version bump only for package @digisac/back

## [1.179.39](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.38...v1.179.39) (2021-10-22)

### Bug Fixes

- **migration:** corrige os files que que estão deletados porém associados a contatos e conexões ativas. ([c0929a5](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/c0929a54d9b5ae89dd87114305bd74a8d15eb41e))

## [1.179.38](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.37...v1.179.38) (2021-10-21)

**Note:** Version bump only for package @digisac/back

## [1.179.37](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.36...v1.179.37) (2021-10-21)

**Note:** Version bump only for package @digisac/back

## [1.179.36](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.35...v1.179.36) (2021-10-21)

**Note:** Version bump only for package @digisac/back

## [1.179.35](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.34...v1.179.35) (2021-10-21)

**Note:** Version bump only for package @digisac/back

## [1.179.34](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.33-mr-488.2...v1.179.34) (2021-10-21)

**Note:** Version bump only for package @digisac/back

## [1.179.32](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.31...v1.179.32) (2021-10-18)

**Note:** Version bump only for package @digisac/back

## [1.179.31](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.30...v1.179.31) (2021-10-14)

**Note:** Version bump only for package @digisac/back

## [1.179.30](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.29...v1.179.30) (2021-10-14)

**Note:** Version bump only for package @digisac/back

## [1.179.29](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.9...v1.179.29) (2021-10-13)

### Bug Fixes

- corrige script de check-version.sh ([898142a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/898142a1657a2e33ec627628d74c03d2048e6a06))

## [1.179.9](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.28...v1.179.9) (2021-10-13)

### Bug Fixes

- corrige lint em stable ([cd0604c](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/cd0604c429b7d52c1bcc242244fa8467a5c94ab8))

## [1.179.27](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.27-mr-438.0...v1.179.27) (2021-10-11)

**Note:** Version bump only for package @digisac/back

## [1.179.26](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.13...v1.179.26) (2021-10-04)

**Note:** Version bump only for package @digisac/back

## [1.179.13](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.25...v1.179.13) (2021-10-04)

**Note:** Version bump only for package @digisac/back

## [1.179.25](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.24...v1.179.25) (2021-10-04)

**Note:** Version bump only for package @digisac/back

## [1.179.24](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.23...v1.179.24) (2021-09-30)

**Note:** Version bump only for package @digisac/back

## [1.179.23](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.22...v1.179.23) (2021-09-30)

### Bug Fixes

- corrige export de chats ([ecf912c](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/ecf912c5cff9e858505d6d31f07cf7edd93da1e4))

## [1.179.14](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.14) (2021-09-30)

### Bug Fixes

- corrige savePermissions ([9397c70](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/9397c702a4d31db16100297dfe64c7f571049ca9))

## [1.179.21](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.21) (2021-09-30)

### Bug Fixes

- corrige savePermissions ([9397c70](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/9397c702a4d31db16100297dfe64c7f571049ca9))

## [1.179.20](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.20) (2021-09-24)

### Bug Fixes

- corrige savePermissions ([9397c70](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/9397c702a4d31db16100297dfe64c7f571049ca9))

## [1.179.19](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.19) (2021-09-23)

### Bug Fixes

- corrige savePermissions ([9397c70](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/9397c702a4d31db16100297dfe64c7f571049ca9))

## [1.179.18](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.16...v1.179.18) (2021-09-23)

**Note:** Version bump only for package @digisac/back

## [1.179.17](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.15...v1.179.17) (2021-09-23)

**Note:** Version bump only for package @digisac/back

## [1.179.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.15...v1.179.4) (2021-09-23)

**Note:** Version bump only for package @digisac/back

## [1.179.16](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.15...v1.179.16) (2021-09-23)

**Note:** Version bump only for package @digisac/back

## [1.179.15](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.15) (2021-09-23)

### Bug Fixes

- modifica yarn.lock ([6823fee](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/6823feeb36a9e9be70ae3be0a5a371970d6c8429))
- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))

## [1.179.14](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.14) (2021-09-23)

### Bug Fixes

- modifica yarn.lock ([6823fee](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/6823feeb36a9e9be70ae3be0a5a371970d6c8429))
- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))

## [1.179.15](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.15) (2021-09-21)

### Bug Fixes

- modifica yarn.lock ([6823fee](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/6823feeb36a9e9be70ae3be0a5a371970d6c8429))
- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))

## [1.179.14](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.14) (2021-09-21)

### Bug Fixes

- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))

## [1.179.13](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.13) (2021-09-15)

### Bug Fixes

- **whatsapp:** espera carregamento do pageVersion ([7247c2a](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/7247c2ac8f49fab943ff515897ef0319ecc3a49e))

## [1.179.3-mr-321.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.6...v1.179.3-mr-321.6) (2021-08-26)

## [1.179.12](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.12-mr-420.1...v1.179.12) (2021-09-14)

**Note:** Version bump only for package @digisac/back

## [1.178.18-mr-321.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-321.1...v1.178.18-mr-321.2) (2021-08-11)

**Note:** Version bump only for package @digisac/back

## [1.179.3-rc.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.5...v1.179.3-rc.6) (2021-08-26)

**Note:** Version bump only for package @digisac/back

## [1.179.3-mr-334.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.5...v1.179.3-mr-334.0) (2021-08-26)

**Note:** Version bump only for package @digisac/back

## [1.179.7](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.5...v1.179.7) (2021-09-02)

**Note:** Version bump only for package @digisac/back

## [1.179.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.1...v1.179.5) (2021-08-31)

## [1.179.3-rc.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.4...v1.179.3-rc.5) (2021-08-26)

**Note:** Version bump only for package @digisac/back

## [1.178.21-mr-344.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.4...v1.178.21-mr-344.2) (2021-08-26)

## [1.178.21-mr-344.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-344.0...v1.178.21-mr-344.1) (2021-08-18)

**Note:** Version bump only for package @digisac/back

### Bug Fixes

- adiciona ajustes labels ([5be5acf](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/5be5acf917e83cdcd733e7732bc52acaf35ddea0))

### Features

- estatisticas HSMs ([0db171c](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/0db171cc4c43ea9966d9f0ff4d24bd77072e7fba))

## [1.179.3-rc.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-rc.3...v1.179.3-rc.0) (2021-08-24)

## [1.179.3-rc.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-mr-182.2...v1.179.3-rc.4) (2021-08-26)

**Note:** Version bump only for package @digisac/back

## [1.179.3-mr-362.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.8...v1.179.3-mr-362.1) (2021-08-30)

**Note:** Version bump only for package @digisac/back

## [1.179.3-rc.8](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.7...v1.179.3-rc.8) (2021-08-27)

**Note:** Version bump only for package @digisac/back

## [1.178.21-mr-182.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.3...v1.178.21-mr-182.2) (2021-08-26)

## [1.178.21-mr-182.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-mr-182.0...v1.178.21-mr-182.1) (2021-08-17)

## [1.178.21-mr-182.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21...v1.178.21-mr-182.0) (2021-08-17)

## [1.178.18-mr-182.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-182.4...v1.178.18-mr-182.5) (2021-08-05)

## [1.178.18-mr-182.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-269.1...v1.178.18-mr-182.4) (2021-08-05)

## [1.178.18-mr-182.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-307.4...v1.178.18-mr-182.3) (2021-08-04)

## [1.178.18-mr-182.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-rc.8...v1.178.18-mr-182.0) (2021-08-02)

## [1.178.15-mr-182.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-mr-241.11...v1.178.15-mr-182.2) (2021-07-30)

## [1.178.15-mr-182.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.15-mr-182.0...v1.178.15-mr-182.1) (2021-07-23)

## [1.178.15-mr-182.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.15-mr-245.0...v1.178.15-mr-182.0) (2021-07-23)

## [1.178.12-mr-182.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.14...v1.178.12-mr-182.1) (2021-07-22)

## [1.178.12-mr-182.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.2-mr-269.5...v1.178.12-mr-182.0) (2021-07-20)

## [1.168.1-mr-182.7](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.172.2-rc.8...v1.168.1-mr-182.7) (2021-06-25)

## [1.168.1-mr-182.6](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-177.6...v1.168.1-mr-182.6) (2021-06-22)

## [1.168.1-mr-182.4](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-182.3...v1.168.1-mr-182.4) (2021-06-15)

## [1.168.1-mr-182.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-182.2...v1.168.1-mr-182.3) (2021-06-14)

## [1.168.1-mr-182.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-182.1...v1.168.1-mr-182.2) (2021-06-14)

## [1.168.1-mr-182.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.168.1-mr-182.0...v1.168.1-mr-182.1) (2021-06-14)

# 1.168.0 (2021-05-26)

**Note:** Version bump only for package @digisac/back

## [1.179.3-rc.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.2...v1.179.3-rc.3) (2021-08-25)

**Note:** Version bump only for package @digisac/back

## [1.179.3-rc.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-mr-347.5...v1.179.3-rc.2) (2021-08-25)

## [1.179.3-mr-361.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-mr-361.0...v1.179.3-mr-361.1) (2021-08-25)

## [1.179.3-mr-347.5](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.179.3-rc.1...v1.179.3-mr-347.5) (2021-08-25)

# [1.179.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.22-mr-276.0...v1.179.0) (2021-08-20)

## [1.178.22](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21...v1.178.22) (2021-08-17)

## [1.179.3-rc.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-rc.3...v1.179.3-rc.0) (2021-08-24)

## [1.179.1](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-rc.2...v1.179.1) (2021-08-22)

### Bug Fixes

- **permissions:** [MN-2444] Correção na query de permissões ([a7b6afa](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/a7b6afa0504ec605b6ee83a37b0f43e80701c8f3))

# [1.179.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-mr-352.0...v1.179.0) (2021-08-20)

## [1.178.22](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21...v1.178.22) (2021-08-17)

## [1.178.21](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.21-rc.1...v1.178.21) (2021-08-17)

## [1.178.20-mr-318.3](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-rc.21...v1.178.20-mr-318.3) (2021-08-14)

## [1.178.20-mr-318.2](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.20-mr-318.1...v1.178.20-mr-318.2) (2021-08-12)

## [1.178.20-mr-318.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.178.18-rc.13...v1.178.20-mr-318.0) (2021-08-09)

## [1.161.1-mr-76.0](https://gitlab.mandeumzap.com.br/digisac/digisac/compare/v1.161.0...v1.161.1-mr-76.0) (2021-04-14)

### Features

- **socket:** não receber sockets de mensagens e chamados se estiver com outros atendentes ([4905e80](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/4905e80fc9439339c370102fc3db85422ca1b970))
- **socket:** otimização de socket de mensagens e contatos ([c4dde6d](https://gitlab.mandeumzap.com.br/digisac/digisac/commit/c4dde6d1f242244d22daeed5206a1ab76db2901a))
